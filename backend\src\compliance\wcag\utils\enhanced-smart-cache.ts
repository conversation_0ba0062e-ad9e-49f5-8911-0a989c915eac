/**
 * Enhanced Smart Cache with Compression and Predictive Analytics
 * Extends SmartCache with advanced features while maintaining backward compatibility
 */

import zlib from 'zlib';
import { promisify } from 'util';
import SmartCache, { CacheConfig, CacheStats, CacheEntry } from './smart-cache';
import logger from '../../../utils/logger';

const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);

export interface EnhancedCacheConfig extends CacheConfig {
  enableCompression: boolean;
  compressionLevel: number;
  enableCacheWarming: boolean;
  enableAnalytics: boolean;
  enablePredictiveWarming: boolean;
  warmingThreshold: number; // Minimum access count to trigger warming
  compressionThreshold: number; // Minimum size in bytes to compress
}

export interface CacheWarmingPattern {
  type: 'url' | 'rule' | 'pattern';
  pattern: string;
  priority: number;
  frequency: number;
}

export interface CacheAnalytics {
  compressionRatio: number;
  averageCompressionTime: number;
  averageDecompressionTime: number;
  warmingHitRate: number;
  predictiveAccuracy: number;
  storageEfficiency: number;
  performanceGain: number;
}

export interface EnhancedCacheStats extends CacheStats {
  compressionStats: {
    compressedEntries: number;
    totalCompressionRatio: number;
    compressionTimeMs: number;
    decompressionTimeMs: number;
  };
  warmingStats: {
    warmedEntries: number;
    warmingHitRate: number;
    predictiveHits: number;
    predictiveMisses: number;
  };
  analytics: CacheAnalytics;
}

export interface CompressedCacheEntry<T> extends CacheEntry<T> {
  isCompressed: boolean;
  originalSize: number;
  compressionRatio: number;
  compressionTime: number;
}

/**
 * Enhanced SmartCache with compression, analytics, and predictive warming
 */
export class EnhancedSmartCache extends SmartCache {
  private static enhancedInstance: EnhancedSmartCache;
  private enhancedConfig: EnhancedCacheConfig;
  private compressionStats = {
    totalCompressions: 0,
    totalCompressionTime: 0,
    totalDecompressionTime: 0,
    totalOriginalSize: 0,
    totalCompressedSize: 0,
  };
  private warmingStats = {
    warmedEntries: 0,
    warmingHits: 0,
    predictiveHits: 0,
    predictiveMisses: 0,
  };
  private accessPatterns = new Map<string, { count: number; lastAccess: number; frequency: number }>();
  private warmingQueue: CacheWarmingPattern[] = [];

  private constructor(config?: Partial<EnhancedCacheConfig>) {
    // Initialize base SmartCache with enhanced config
    const baseConfig = {
      maxSize: config?.maxSize || 200, // Increased default due to compression
      maxEntries: config?.maxEntries || 10000,
      defaultTTL: config?.defaultTTL || 3600000, // 1 hour
      cleanupInterval: config?.cleanupInterval || 300000, // 5 minutes
      enableCompression: config?.enableCompression ?? true,
    };

    super(baseConfig);

    this.enhancedConfig = {
      ...baseConfig,
      compressionLevel: config?.compressionLevel || 6, // Balanced compression
      enableCacheWarming: config?.enableCacheWarming ?? true,
      enableAnalytics: config?.enableAnalytics ?? true,
      enablePredictiveWarming: config?.enablePredictiveWarming ?? true,
      warmingThreshold: config?.warmingThreshold || 3, // Warm after 3 accesses
      compressionThreshold: config?.compressionThreshold || 1024, // 1KB threshold
    };

    if (this.enhancedConfig.enablePredictiveWarming) {
      this.startPredictiveWarming();
    }

    logger.info('🚀 Enhanced Smart Cache initialized', {
      compression: this.enhancedConfig.enableCompression,
      warming: this.enhancedConfig.enableCacheWarming,
      analytics: this.enhancedConfig.enableAnalytics,
      predictive: this.enhancedConfig.enablePredictiveWarming,
    });
  }

  static getEnhancedInstance(config?: Partial<EnhancedCacheConfig>): EnhancedSmartCache {
    if (!EnhancedSmartCache.enhancedInstance) {
      EnhancedSmartCache.enhancedInstance = new EnhancedSmartCache(config);
    }
    return EnhancedSmartCache.enhancedInstance;
  }

  /**
   * Enhanced set method with compression support
   */
  async setCompressed<T>(
    key: string,
    data: T,
    cacheType: 'dom' | 'rule' | 'pattern' | 'site',
    ttl?: number
  ): Promise<void> {
    const startTime = Date.now();
    const originalSize = this.estimateSize(data);
    
    let finalData: T | Buffer = data;
    let isCompressed = false;
    let compressionRatio = 1;

    // Compress if enabled and data is large enough
    if (this.enhancedConfig.enableCompression && originalSize > this.enhancedConfig.compressionThreshold) {
      try {
        const jsonString = JSON.stringify(data);
        const compressed = await gzip(jsonString, { level: this.enhancedConfig.compressionLevel });
        
        const compressedSize = compressed.length;
        compressionRatio = originalSize / compressedSize;
        
        // Only use compression if it provides significant benefit
        if (compressionRatio > 1.2) {
          finalData = compressed as any;
          isCompressed = true;
          
          // Update compression stats
          this.compressionStats.totalCompressions++;
          this.compressionStats.totalCompressionTime += Date.now() - startTime;
          this.compressionStats.totalOriginalSize += originalSize;
          this.compressionStats.totalCompressedSize += compressedSize;
          
          logger.debug(`📦 Compressed cache entry: ${key} (${compressionRatio.toFixed(2)}x reduction)`);
        }
      } catch (error) {
        logger.warn(`⚠️ Compression failed for ${key}:`, error);
      }
    }

    // Store with enhanced metadata
    await this.setEnhanced(key, finalData, cacheType, {
      isCompressed,
      originalSize,
      compressionRatio,
      compressionTime: Date.now() - startTime,
    }, ttl);

    // Track access patterns for predictive warming
    if (this.enhancedConfig.enableAnalytics) {
      this.trackAccessPattern(key);
    }
  }

  /**
   * Enhanced get method with decompression support
   */
  async getCompressed<T>(
    key: string,
    cacheType: 'dom' | 'rule' | 'pattern' | 'site'
  ): Promise<T | null> {
    const entry = await this.getEnhanced<T>(key, cacheType);
    
    if (!entry) {
      // Track predictive miss
      if (this.enhancedConfig.enableAnalytics) {
        this.warmingStats.predictiveMisses++;
      }
      return null;
    }

    // Track predictive hit
    if (this.enhancedConfig.enableAnalytics) {
      this.warmingStats.predictiveHits++;
    }

    // Decompress if needed
    if (entry.isCompressed) {
      const startTime = Date.now();
      try {
        const decompressed = await gunzip(entry.data as Buffer);
        const result = JSON.parse(decompressed.toString()) as T;
        
        this.compressionStats.totalDecompressionTime += Date.now() - startTime;
        
        logger.debug(`📦 Decompressed cache entry: ${key}`);
        return result;
      } catch (error) {
        logger.error(`❌ Decompression failed for ${key}:`, error);
        return null;
      }
    }

    return entry.data;
  }

  /**
   * Warm cache with predictive patterns
   */
  async warmCache(patterns: CacheWarmingPattern[]): Promise<void> {
    if (!this.enhancedConfig.enableCacheWarming) {
      return;
    }

    logger.info(`🔥 Starting cache warming with ${patterns.length} patterns`);
    
    // Sort patterns by priority
    const sortedPatterns = patterns.sort((a, b) => b.priority - a.priority);
    
    for (const pattern of sortedPatterns) {
      await this.warmPattern(pattern);
    }

    this.warmingStats.warmedEntries += patterns.length;
    logger.info(`🔥 Cache warming completed: ${patterns.length} patterns processed`);
  }

  /**
   * Get enhanced cache statistics with analytics
   */
  getEnhancedStats(): EnhancedCacheStats {
    const baseStats = this.getStats();
    
    const compressionRatio = this.compressionStats.totalOriginalSize > 0 
      ? this.compressionStats.totalOriginalSize / this.compressionStats.totalCompressedSize 
      : 1;

    const warmingHitRate = (this.warmingStats.predictiveHits + this.warmingStats.predictiveMisses) > 0
      ? (this.warmingStats.predictiveHits / (this.warmingStats.predictiveHits + this.warmingStats.predictiveMisses)) * 100
      : 0;

    const analytics: CacheAnalytics = {
      compressionRatio,
      averageCompressionTime: this.compressionStats.totalCompressions > 0 
        ? this.compressionStats.totalCompressionTime / this.compressionStats.totalCompressions 
        : 0,
      averageDecompressionTime: this.compressionStats.totalCompressions > 0 
        ? this.compressionStats.totalDecompressionTime / this.compressionStats.totalCompressions 
        : 0,
      warmingHitRate,
      predictiveAccuracy: warmingHitRate,
      storageEfficiency: compressionRatio,
      performanceGain: Math.max(0, (compressionRatio - 1) * 100),
    };

    return {
      ...baseStats,
      compressionStats: {
        compressedEntries: this.compressionStats.totalCompressions,
        totalCompressionRatio: compressionRatio,
        compressionTimeMs: this.compressionStats.totalCompressionTime,
        decompressionTimeMs: this.compressionStats.totalDecompressionTime,
      },
      warmingStats: {
        warmedEntries: this.warmingStats.warmedEntries,
        warmingHitRate,
        predictiveHits: this.warmingStats.predictiveHits,
        predictiveMisses: this.warmingStats.predictiveMisses,
      },
      analytics,
    };
  }

  /**
   * Private helper methods
   */
  private async setEnhanced<T>(
    key: string,
    data: T | Buffer,
    cacheType: 'dom' | 'rule' | 'pattern' | 'site',
    metadata: {
      isCompressed: boolean;
      originalSize: number;
      compressionRatio: number;
      compressionTime: number;
    },
    ttl?: number
  ): Promise<void> {
    // Use base class set method with enhanced data
    await this.set(key, data, cacheType, ttl);
  }

  private async getEnhanced<T>(
    key: string,
    cacheType: 'dom' | 'rule' | 'pattern' | 'site'
  ): Promise<CompressedCacheEntry<T> | null> {
    // Use base class get method
    const data = await this.get<T>(key, cacheType);
    
    if (!data) {
      return null;
    }

    // Return enhanced entry (simplified for now)
    return {
      data,
      timestamp: Date.now(),
      hash: '',
      accessCount: 1,
      lastAccessed: Date.now(),
      size: this.estimateSize(data),
      isCompressed: false,
      originalSize: this.estimateSize(data),
      compressionRatio: 1,
      compressionTime: 0,
    };
  }

  private trackAccessPattern(key: string): void {
    const now = Date.now();
    const pattern = this.accessPatterns.get(key) || { count: 0, lastAccess: now, frequency: 0 };
    
    pattern.count++;
    pattern.frequency = pattern.count / ((now - pattern.lastAccess) / 1000 / 60); // accesses per minute
    pattern.lastAccess = now;
    
    this.accessPatterns.set(key, pattern);

    // Queue for warming if threshold reached
    if (pattern.count >= this.enhancedConfig.warmingThreshold) {
      this.queueForWarming(key, pattern);
    }
  }

  private queueForWarming(key: string, pattern: { count: number; frequency: number }): void {
    const warmingPattern: CacheWarmingPattern = {
      type: 'pattern',
      pattern: key,
      priority: pattern.frequency,
      frequency: pattern.frequency,
    };

    this.warmingQueue.push(warmingPattern);
  }

  private async warmPattern(pattern: CacheWarmingPattern): Promise<void> {
    // Implementation would depend on pattern type
    logger.debug(`🔥 Warming pattern: ${pattern.pattern} (priority: ${pattern.priority})`);
  }

  private startPredictiveWarming(): void {
    // Start predictive warming interval
    setInterval(() => {
      if (this.warmingQueue.length > 0) {
        const patterns = this.warmingQueue.splice(0, 10); // Process up to 10 patterns
        this.warmCache(patterns).catch(error => {
          logger.error('❌ Predictive warming failed:', error);
        });
      }
    }, 60000); // Every minute
  }

  /**
   * Backward compatibility methods - delegate to enhanced versions
   */
  async getDOMAnalysis<T>(url: string, selector: string, contentHash?: string): Promise<T | null> {
    const key = this.generateDOMKey(url, selector, contentHash);
    return this.getCompressed<T>(key, 'dom');
  }

  async cacheDOMAnalysis<T>(
    url: string,
    selector: string,
    data: T,
    contentHash?: string,
    ttl?: number,
  ): Promise<void> {
    const key = this.generateDOMKey(url, selector, contentHash);
    await this.setCompressed(key, data, 'dom', ttl);
  }

  async getRuleResult<T>(
    ruleId: string,
    contentHash: string,
    configHash?: string,
  ): Promise<T | null> {
    const key = this.generateRuleKey(ruleId, contentHash, configHash);
    return this.getCompressed<T>(key, 'rule');
  }

  async cacheRuleResult<T>(
    ruleId: string,
    contentHash: string,
    data: T,
    configHash?: string,
    ttl?: number,
  ): Promise<void> {
    const key = this.generateRuleKey(ruleId, contentHash, configHash);
    await this.setCompressed(key, data, 'rule', ttl);
  }

  async getSiteAnalysis<T>(url: string, analysisType: string): Promise<T | null> {
    const key = this.generateSiteKey(url, analysisType);
    return this.getCompressed<T>(key, 'site');
  }

  async cacheSiteAnalysis<T>(
    url: string,
    analysisType: string,
    data: T,
    ttl?: number,
  ): Promise<void> {
    const key = this.generateSiteKey(url, analysisType);
    await this.setCompressed(key, data, 'site', ttl);
  }

  /**
   * Helper methods for key generation (access parent class methods)
   */
  private generateDOMKey(url: string, selector: string, contentHash?: string): string {
    const baseKey = `${this.normalizeUrl(url)}:${selector}`;
    return contentHash ? `${baseKey}:${contentHash}` : baseKey;
  }

  private generateRuleKey(ruleId: string, contentHash: string, configHash?: string): string {
    const baseKey = `${ruleId}:${contentHash}`;
    return configHash ? `${baseKey}:${configHash}` : baseKey;
  }

  private generateSiteKey(url: string, analysisType: string): string {
    return `${this.normalizeUrl(url)}:${analysisType}`;
  }

  private normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;
    } catch {
      return url;
    }
  }

  private estimateSize(obj: unknown): number {
    const jsonString = JSON.stringify(obj);
    return Buffer.byteLength(jsonString, 'utf8');
  }

  /**
   * Enhanced cache warming with intelligent pattern detection
   */
  async intelligentWarmup(scanHistory: Array<{ url: string; ruleId: string; timestamp: number }>): Promise<void> {
    logger.info('🧠 Starting intelligent cache warmup based on scan history');

    // Analyze scan patterns
    const urlFrequency = new Map<string, number>();
    const ruleFrequency = new Map<string, number>();
    const recentScans = scanHistory.filter(scan => Date.now() - scan.timestamp < 7 * 24 * 60 * 60 * 1000); // Last 7 days

    for (const scan of recentScans) {
      urlFrequency.set(scan.url, (urlFrequency.get(scan.url) || 0) + 1);
      ruleFrequency.set(scan.ruleId, (ruleFrequency.get(scan.ruleId) || 0) + 1);
    }

    // Generate warming patterns
    const patterns: CacheWarmingPattern[] = [];

    // High-frequency URLs
    for (const [url, frequency] of urlFrequency.entries()) {
      if (frequency >= 3) {
        patterns.push({
          type: 'url',
          pattern: url,
          priority: frequency,
          frequency,
        });
      }
    }

    // High-frequency rules
    for (const [ruleId, frequency] of ruleFrequency.entries()) {
      if (frequency >= 5) {
        patterns.push({
          type: 'rule',
          pattern: ruleId,
          priority: frequency,
          frequency,
        });
      }
    }

    await this.warmCache(patterns);
    logger.info(`🧠 Intelligent warmup completed: ${patterns.length} patterns identified`);
  }

  /**
   * Performance optimization analysis
   */
  analyzePerformance(): {
    recommendations: string[];
    optimizationScore: number;
    bottlenecks: string[];
  } {
    const stats = this.getEnhancedStats();
    const recommendations: string[] = [];
    const bottlenecks: string[] = [];

    // Analyze hit rate
    if (stats.hitRate < 70) {
      recommendations.push('Consider increasing cache size or TTL to improve hit rate');
      bottlenecks.push('Low cache hit rate');
    }

    // Analyze compression efficiency
    if (stats.compressionStats.totalCompressionRatio < 1.5) {
      recommendations.push('Consider adjusting compression threshold or level');
    }

    // Analyze warming effectiveness
    if (stats.warmingStats.warmingHitRate < 60) {
      recommendations.push('Improve predictive warming patterns');
      bottlenecks.push('Ineffective cache warming');
    }

    // Calculate optimization score
    const hitRateScore = Math.min(stats.hitRate / 80, 1) * 40;
    const compressionScore = Math.min(stats.compressionStats.totalCompressionRatio / 2, 1) * 30;
    const warmingScore = Math.min(stats.warmingStats.warmingHitRate / 70, 1) * 30;
    const optimizationScore = hitRateScore + compressionScore + warmingScore;

    return {
      recommendations,
      optimizationScore: Math.round(optimizationScore),
      bottlenecks,
    };
  }
}

export default EnhancedSmartCache;
