/**
 * WCAG-051: Keyboard Accessible Check
 * Success Criterion: 2.1.1 Keyboard (Level A)
 * Enhanced keyboard accessibility validation
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface KeyboardAccessibleConfig extends EnhancedCheckConfig {
  enableAdvancedKeyboardTracking?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
}

export class KeyboardAccessibleCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();

  async performCheck(config: KeyboardAccessibleConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: KeyboardAccessibleConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAdvancedKeyboardTracking: true,
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-051',
      'Keyboard Accessible',
      'operable',
      0.0687,
      'A',
      enhancedConfig,
      this.executeKeyboardAccessibleCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with keyboard interaction analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-051',
        ruleName: 'Keyboard Accessible',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.85,
          checkType: 'keyboard-interaction-analysis',
          keyboardTesting: true,
          focusManagement: true,
          advancedKeyboardTracking: enhancedConfig.enableAdvancedKeyboardTracking,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 35,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeKeyboardAccessibleCheck(
    page: Page,
    _config: KeyboardAccessibleConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze keyboard accessibility
    const keyboardAnalysis = await page.evaluate(() => {
      const problematicElements: Array<{
        selector: string;
        tagName: string;
        elementType: string;
        isInteractive: boolean;
        isFocusable: boolean;
        hasTabIndex: boolean;
        tabIndex: number;
        hasKeyboardHandlers: boolean;
        hasMouseOnlyEvents: boolean;
        issues: string[];
        severity: 'error' | 'warning' | 'info';
      }> = [];

      // Get all potentially interactive elements
      const interactiveElements = document.querySelectorAll(`
        button, input, select, textarea, a[href], area[href],
        [role="button"], [role="link"], [role="menuitem"], [role="tab"],
        [role="checkbox"], [role="radio"], [role="slider"], [role="spinbutton"],
        [onclick], [onmousedown], [onmouseup], [onmouseover], [onmouseout],
        [tabindex], .clickable, .interactive, [data-toggle], [data-dismiss]
      `);

      interactiveElements.forEach((element, index) => {
        const issues: string[] = [];
        let severity: 'error' | 'warning' | 'info' = 'info';
        
        const tagName = element.tagName.toLowerCase();
        const tabIndex = parseInt(element.getAttribute('tabindex') || '0');
        const hasTabIndex = element.hasAttribute('tabindex');
        
        // Determine if element is naturally focusable
        const naturallyFocusable = ['button', 'input', 'select', 'textarea', 'a', 'area'].includes(tagName);
        const isFocusable = naturallyFocusable || tabIndex >= 0;
        
        // Check for interactive behavior
        const hasClickHandler = element.hasAttribute('onclick') || 
                               element.addEventListener !== undefined;
        const hasMouseEvents = element.hasAttribute('onmousedown') ||
                              element.hasAttribute('onmouseup') ||
                              element.hasAttribute('onmouseover') ||
                              element.hasAttribute('onmouseout');
        
        // Check for keyboard event handlers
        const hasKeyboardHandlers = element.hasAttribute('onkeydown') ||
                                   element.hasAttribute('onkeyup') ||
                                   element.hasAttribute('onkeypress');
        
        // Determine element type
        let elementType = 'unknown';
        if (tagName === 'button' || element.getAttribute('role') === 'button') {
          elementType = 'button';
        } else if (tagName === 'a') {
          elementType = 'link';
        } else if (['input', 'select', 'textarea'].includes(tagName)) {
          elementType = 'form-control';
        } else if (element.getAttribute('role')) {
          elementType = element.getAttribute('role') || 'custom';
        } else if (hasClickHandler || hasMouseEvents) {
          elementType = 'interactive';
        }
        
        const isInteractive = naturallyFocusable || hasClickHandler || hasMouseEvents || 
                             element.getAttribute('role') === 'button' ||
                             element.getAttribute('role') === 'link';
        
        // Check for keyboard accessibility issues
        if (isInteractive && !isFocusable) {
          issues.push('Interactive element not keyboard focusable');
          severity = 'error';
        }
        
        if (isInteractive && hasMouseEvents && !hasKeyboardHandlers && !naturallyFocusable) {
          issues.push('Mouse-only interaction without keyboard equivalent');
          severity = 'error';
        }
        
        if (hasTabIndex && tabIndex < -1) {
          issues.push('Invalid tabindex value (less than -1)');
          severity = 'warning';
        }
        
        if (tabIndex > 0) {
          issues.push('Positive tabindex disrupts natural tab order');
          severity = 'warning';
        }
        
        // Check for custom interactive elements without proper ARIA
        if (elementType === 'interactive' && !element.getAttribute('role')) {
          issues.push('Custom interactive element missing role attribute');
          severity = 'warning';
        }
        
        // Check for links without href
        if (tagName === 'a' && !element.hasAttribute('href')) {
          issues.push('Link element without href not keyboard accessible');
          severity = 'error';
        }
        
        // Check for disabled elements that might still be focusable
        const isDisabled = (element as HTMLInputElement).disabled ||
                          element.getAttribute('aria-disabled') === 'true';
        if (isDisabled && isFocusable && tabIndex >= 0) {
          issues.push('Disabled element still in tab order');
          severity = 'warning';
        }
        
        if (issues.length > 0) {
          problematicElements.push({
            selector: `${tagName}:nth-of-type(${index + 1})`,
            tagName,
            elementType,
            isInteractive,
            isFocusable,
            hasTabIndex,
            tabIndex,
            hasKeyboardHandlers,
            hasMouseOnlyEvents: hasMouseEvents && !hasKeyboardHandlers,
            issues,
            severity,
          });
        }
      });

      // Check for drag and drop without keyboard alternatives
      const draggableElements = document.querySelectorAll('[draggable="true"], .draggable');
      draggableElements.forEach((element, index) => {
        const hasKeyboardDragSupport = element.hasAttribute('onkeydown') ||
                                      element.querySelector('[role="button"]') !== null;
        
        if (!hasKeyboardDragSupport) {
          problematicElements.push({
            selector: `[draggable]:nth-of-type(${index + 1})`,
            tagName: element.tagName.toLowerCase(),
            elementType: 'draggable',
            isInteractive: true,
            isFocusable: false,
            hasTabIndex: false,
            tabIndex: -1,
            hasKeyboardHandlers: false,
            hasMouseOnlyEvents: true,
            issues: ['Drag and drop without keyboard alternative'],
            severity: 'error',
          });
        }
      });

      // Check for hover-only interactions
      const hoverElements = document.querySelectorAll('[onmouseover], [onmouseenter], .hover-trigger');
      hoverElements.forEach((element, index) => {
        const hasFocusEquivalent = element.hasAttribute('onfocus') ||
                                  element.hasAttribute('onkeydown');
        
        if (!hasFocusEquivalent) {
          problematicElements.push({
            selector: `[onmouseover]:nth-of-type(${index + 1})`,
            tagName: element.tagName.toLowerCase(),
            elementType: 'hover-trigger',
            isInteractive: true,
            isFocusable: false,
            hasTabIndex: false,
            tabIndex: -1,
            hasKeyboardHandlers: false,
            hasMouseOnlyEvents: true,
            issues: ['Hover-only interaction without keyboard equivalent'],
            severity: 'error',
          });
        }
      });

      return {
        problematicElements,
        totalInteractiveElements: interactiveElements.length,
        problematicCount: problematicElements.length,
        notFocusableCount: problematicElements.filter(el => !el.isFocusable && el.isInteractive).length,
        mouseOnlyCount: problematicElements.filter(el => el.hasMouseOnlyEvents).length,
        positiveTabIndexCount: problematicElements.filter(el => el.tabIndex > 0).length,
      };
    });

    let score = 100;
    const elementCount = keyboardAnalysis.problematicCount;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      // Calculate score based on severity
      keyboardAnalysis.problematicElements.forEach((element) => {
        const deduction = element.severity === 'error' ? 15 : 
                         element.severity === 'warning' ? 8 : 3;
        score = Math.max(0, score - deduction);
      });

      issues.push(`${elementCount} elements with keyboard accessibility issues found`);
      if (keyboardAnalysis.notFocusableCount > 0) {
        issues.push(`${keyboardAnalysis.notFocusableCount} interactive elements not keyboard focusable`);
      }
      if (keyboardAnalysis.mouseOnlyCount > 0) {
        issues.push(`${keyboardAnalysis.mouseOnlyCount} elements have mouse-only interactions`);
      }

      keyboardAnalysis.problematicElements.forEach((element) => {
        evidence.push({
          type: 'code',
          description: `Keyboard accessibility issue: ${element.issues.join(', ')}`,
          value: `${element.elementType} | Focusable: ${element.isFocusable} | TabIndex: ${element.tabIndex}`,
          selector: element.selector,
          elementCount: 1,
          affectedSelectors: [element.selector],
          severity: element.severity,
          fixExample: {
            before: this.getBeforeExample(element),
            after: this.getAfterExample(element),
            description: this.getFixDescription(element.issues),
            codeExample: this.getCodeExample(element.elementType),
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/keyboard.html',
              'https://www.w3.org/WAI/WCAG21/Techniques/G202',
              'https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              elementType: element.elementType,
              isInteractive: element.isInteractive,
              isFocusable: element.isFocusable,
              hasTabIndex: element.hasTabIndex,
              tabIndex: element.tabIndex,
              hasKeyboardHandlers: element.hasKeyboardHandlers,
              hasMouseOnlyEvents: element.hasMouseOnlyEvents,
            },
          },
        });
      });
    }

    // Add recommendations
    recommendations.push('Ensure all interactive elements are keyboard accessible');
    recommendations.push('Provide keyboard equivalents for mouse-only interactions');
    recommendations.push('Use proper semantic HTML elements for interactive content');
    recommendations.push('Add appropriate ARIA roles and properties for custom controls');
    recommendations.push('Test all functionality using only the keyboard');

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(element: any): string {
    if (element.elementType === 'button') {
      return '<div onclick="doSomething()">Click me</div>';
    }
    if (element.elementType === 'interactive') {
      return '<span onclick="toggleMenu()" onmouseover="showTooltip()">Menu</span>';
    }
    if (element.elementType === 'draggable') {
      return '<div draggable="true" ondragstart="handleDrag()">Drag me</div>';
    }
    return `<${element.tagName} onclick="action()">Interactive element</${element.tagName}>`;
  }

  private getAfterExample(element: any): string {
    if (element.elementType === 'button') {
      return '<button onclick="doSomething()">Click me</button>';
    }
    if (element.elementType === 'interactive') {
      return '<button onclick="toggleMenu()" onkeydown="handleKeydown(event)" onfocus="showTooltip()">Menu</button>';
    }
    if (element.elementType === 'draggable') {
      return '<div draggable="true" tabindex="0" role="button" aria-label="Draggable item" onkeydown="handleKeyboardDrag(event)">Drag me</div>';
    }
    return `<${element.tagName} tabindex="0" role="button" onkeydown="handleKeydown(event)">Interactive element</${element.tagName}>`;
  }

  private getFixDescription(issues: string[]): string {
    if (issues.includes('not keyboard focusable')) {
      return 'Add tabindex="0" or use semantic HTML elements';
    }
    if (issues.includes('Mouse-only interaction')) {
      return 'Add keyboard event handlers for mouse interactions';
    }
    if (issues.includes('Positive tabindex')) {
      return 'Remove positive tabindex values and use document order';
    }
    if (issues.includes('missing role')) {
      return 'Add appropriate ARIA role for custom interactive elements';
    }
    return 'Ensure element is keyboard accessible';
  }

  private getCodeExample(elementType: string): string {
    switch (elementType) {
      case 'button':
        return `
<!-- Before: Non-semantic clickable element -->
<div class="button" onclick="submitForm()">Submit</div>

<!-- After: Semantic button element -->
<button type="submit" onclick="submitForm()">Submit</button>

<!-- Alternative: Custom element with proper accessibility -->
<div role="button" tabindex="0" class="custom-button"
     onclick="submitForm()"
     onkeydown="handleKeydown(event)"
     aria-label="Submit form">
  Submit
</div>

<script>
function handleKeydown(event) {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault();
    submitForm();
  }
}
</script>
        `;
      case 'interactive':
        return `
<!-- Before: Mouse-only interaction -->
<span class="menu-trigger"
      onclick="toggleMenu()"
      onmouseover="showPreview()">
  Menu
</span>

<!-- After: Keyboard accessible interaction -->
<button class="menu-trigger"
        onclick="toggleMenu()"
        onkeydown="handleMenuKeydown(event)"
        onfocus="showPreview()"
        onblur="hidePreview()"
        aria-expanded="false"
        aria-haspopup="true">
  Menu
</button>

<script>
function handleMenuKeydown(event) {
  switch(event.key) {
    case 'Enter':
    case ' ':
      event.preventDefault();
      toggleMenu();
      break;
    case 'ArrowDown':
      event.preventDefault();
      openMenuAndFocusFirst();
      break;
    case 'Escape':
      closeMenu();
      break;
  }
}
</script>
        `;
      case 'draggable':
        return `
<!-- Before: Mouse-only drag and drop -->
<div draggable="true" ondragstart="handleDragStart(event)">
  Draggable item
</div>

<!-- After: Keyboard accessible drag and drop -->
<div draggable="true"
     tabindex="0"
     role="button"
     aria-label="Draggable item. Use arrow keys to move, Enter to pick up/drop"
     ondragstart="handleDragStart(event)"
     onkeydown="handleDragKeydown(event)"
     onfocus="showDragInstructions()"
     onblur="hideDragInstructions()">
  Draggable item
</div>

<script>
let dragMode = false;

function handleDragKeydown(event) {
  switch(event.key) {
    case 'Enter':
    case ' ':
      event.preventDefault();
      toggleDragMode();
      break;
    case 'ArrowUp':
    case 'ArrowDown':
    case 'ArrowLeft':
    case 'ArrowRight':
      if (dragMode) {
        event.preventDefault();
        moveElement(event.key);
      }
      break;
    case 'Escape':
      if (dragMode) {
        cancelDrag();
      }
      break;
  }
}

function toggleDragMode() {
  dragMode = !dragMode;
  updateDragState();
}
</script>
        `;
      default:
        return `
<!-- General keyboard accessibility principles -->

<!-- 1. Use semantic HTML when possible -->
<button>Button</button>
<a href="/page">Link</a>
<input type="text" aria-label="Search">

<!-- 2. For custom interactive elements -->
<div role="button"
     tabindex="0"
     onclick="action()"
     onkeydown="handleKeydown(event)"
     aria-label="Custom button">
  Custom Interactive Element
</div>

<!-- 3. Keyboard event handling -->
<script>
function handleKeydown(event) {
  // Handle Enter and Space for button-like elements
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault();
    action();
  }

  // Handle Escape for dismissible elements
  if (event.key === 'Escape') {
    closeElement();
  }

  // Handle arrow keys for navigation
  if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
    handleArrowNavigation(event.key);
  }
}
</script>

<!-- 4. Focus management -->
<style>
.interactive-element:focus {
  outline: 2px solid #007cba;
  outline-offset: 2px;
}

/* Don't remove focus indicators */
.interactive-element:focus:not(:focus-visible) {
  outline: none;
}
</style>
        `;
    }
  }
}
