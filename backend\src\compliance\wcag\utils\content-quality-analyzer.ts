/**
 * Enhanced Content Quality Analyzer
 * Advanced content readability analysis, semantic relationship validation, and context-aware assessment
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

// Content analysis libraries with fallback
let readabilityLibrary: any = null;
let languageDetector: any = null;

try {
  // Try to load readability analysis libraries
  readabilityLibrary = require('automated-readability');
} catch (error) {
  logger.debug('Readability analysis library not available');
}

try {
  // Try to load language detection
  languageDetector = require('franc');
} catch (error) {
  logger.debug('Language detection library not available');
}

export interface ContentQualityConfig {
  enableReadabilityAnalysis: boolean;
  enableSemanticAnalysis: boolean;
  enableLanguageDetection: boolean;
  enableContextAnalysis: boolean;
  enableAccessibilityScoring: boolean;
  readabilityThresholds: {
    excellent: number;
    good: number;
    fair: number;
    poor: number;
  };
  minimumContentLength: number;
  maximumSentenceLength: number;
  complexWordThreshold: number;
}

export interface ReadabilityMetrics {
  fleschKincaidGrade: number;
  fleschReadingEase: number;
  gunningFogIndex: number;
  colemanLiauIndex: number;
  automatedReadabilityIndex: number;
  averageGrade: number;
  readingLevel: 'elementary' | 'middle-school' | 'high-school' | 'college' | 'graduate';
  difficulty: 'very-easy' | 'easy' | 'fairly-easy' | 'standard' | 'fairly-difficult' | 'difficult' | 'very-difficult';
}

export interface SemanticAnalysis {
  contentStructure: {
    hasIntroduction: boolean;
    hasConclusion: boolean;
    hasLogicalFlow: boolean;
    paragraphCount: number;
    averageParagraphLength: number;
  };
  vocabularyAnalysis: {
    uniqueWords: number;
    totalWords: number;
    vocabularyRichness: number;
    complexWords: string[];
    technicalTerms: string[];
    jargonLevel: 'low' | 'medium' | 'high';
  };
  sentenceAnalysis: {
    averageLength: number;
    variability: number;
    complexSentences: number;
    passiveVoice: number;
    readabilityIssues: string[];
  };
}

export interface LanguageAnalysis {
  detectedLanguage: string;
  confidence: number;
  isMultilingual: boolean;
  languageChanges: Array<{
    element: string;
    language: string;
    confidence: number;
  }>;
  missingLanguageAttributes: string[];
  recommendations: string[];
}

export interface ContextualContentAnalysis {
  contentType: 'informational' | 'instructional' | 'navigational' | 'transactional' | 'mixed';
  audienceLevel: 'general' | 'professional' | 'technical' | 'academic';
  purposeClarity: number; // 0-100 score
  informationArchitecture: {
    hasHeadings: boolean;
    headingHierarchy: number[];
    hasLists: boolean;
    hasTables: boolean;
    hasDefinitions: boolean;
    structureScore: number;
  };
  cognitiveLoad: {
    informationDensity: number;
    conceptualComplexity: number;
    navigationComplexity: number;
    overallLoad: 'low' | 'medium' | 'high' | 'very-high';
  };
}

export interface AccessibilityContentScoring {
  readabilityScore: number;
  structureScore: number;
  languageScore: number;
  cognitiveScore: number;
  overallScore: number;
  level: 'excellent' | 'good' | 'fair' | 'poor' | 'very-poor';
  improvements: Array<{
    category: string;
    issue: string;
    recommendation: string;
    impact: 'high' | 'medium' | 'low';
  }>;
}

export interface ContentQualityReport {
  readability: ReadabilityMetrics;
  semantic: SemanticAnalysis;
  language: LanguageAnalysis;
  contextual: ContextualContentAnalysis;
  accessibility: AccessibilityContentScoring;
  summary: {
    overallQuality: number;
    strengths: string[];
    weaknesses: string[];
    priorityRecommendations: string[];
  };
}

/**
 * Enhanced content quality analyzer with comprehensive assessment capabilities
 */
export class ContentQualityAnalyzer {
  private static instance: ContentQualityAnalyzer;
  private config: ContentQualityConfig;

  private constructor(config?: Partial<ContentQualityConfig>) {
    this.config = {
      enableReadabilityAnalysis: config?.enableReadabilityAnalysis ?? true,
      enableSemanticAnalysis: config?.enableSemanticAnalysis ?? true,
      enableLanguageDetection: config?.enableLanguageDetection ?? true,
      enableContextAnalysis: config?.enableContextAnalysis ?? true,
      enableAccessibilityScoring: config?.enableAccessibilityScoring ?? true,
      readabilityThresholds: config?.readabilityThresholds || {
        excellent: 80,
        good: 70,
        fair: 60,
        poor: 50,
      },
      minimumContentLength: config?.minimumContentLength || 100,
      maximumSentenceLength: config?.maximumSentenceLength || 25,
      complexWordThreshold: config?.complexWordThreshold || 3,
    };

    logger.info('📊 Enhanced Content Quality Analyzer initialized', {
      readabilityAnalysis: this.config.enableReadabilityAnalysis,
      semanticAnalysis: this.config.enableSemanticAnalysis,
      languageDetection: this.config.enableLanguageDetection,
    });
  }

  static getInstance(config?: Partial<ContentQualityConfig>): ContentQualityAnalyzer {
    if (!ContentQualityAnalyzer.instance) {
      ContentQualityAnalyzer.instance = new ContentQualityAnalyzer(config);
    }
    return ContentQualityAnalyzer.instance;
  }

  /**
   * Analyze content quality comprehensively
   */
  async analyzeContentQuality(page: Page): Promise<ContentQualityReport> {
    logger.debug('📊 Starting comprehensive content quality analysis');

    // Extract content from page
    const contentData = await this.extractPageContent(page);

    if (contentData.textContent.length < this.config.minimumContentLength) {
      logger.warn('Insufficient content for quality analysis');
      return this.generateMinimalReport();
    }

    // Perform parallel analysis
    const [readability, semantic, language, contextual] = await Promise.all([
      this.config.enableReadabilityAnalysis ? this.analyzeReadability(contentData.textContent) : this.getEmptyReadability(),
      this.config.enableSemanticAnalysis ? this.analyzeSemanticStructure(contentData) : this.getEmptySemantic(),
      this.config.enableLanguageDetection ? this.analyzeLanguage(page, contentData.textContent) : this.getEmptyLanguage(),
      this.config.enableContextAnalysis ? this.analyzeContextualContent(page, contentData) : this.getEmptyContextual(),
    ]);

    // Calculate accessibility scoring
    const accessibility = this.config.enableAccessibilityScoring 
      ? this.calculateAccessibilityScoring(readability, semantic, language, contextual)
      : this.getEmptyAccessibilityScoring();

    // Generate summary
    const summary = this.generateContentSummary(readability, semantic, language, contextual, accessibility);

    const report: ContentQualityReport = {
      readability,
      semantic,
      language,
      contextual,
      accessibility,
      summary,
    };

    logger.info(`✅ Content quality analysis completed`, {
      overallQuality: summary.overallQuality,
      readabilityGrade: readability.averageGrade,
      accessibilityLevel: accessibility.level,
    });

    return report;
  }

  /**
   * Extract meaningful content from page
   */
  private async extractPageContent(page: Page): Promise<{
    textContent: string;
    headings: Array<{ level: number; text: string }>;
    paragraphs: string[];
    lists: string[];
    tables: number;
    links: number;
  }> {
    return await page.evaluate(() => {
      // Extract main text content
      const walker = document.createTreeWalker(
        document.body,
        NodeFilter.SHOW_TEXT,
        {
          acceptNode: (node) => {
            const parent = node.parentElement;
            if (!parent) return NodeFilter.FILTER_REJECT;
            
            // Skip script, style, and hidden elements
            const style = window.getComputedStyle(parent);
            if (style.display === 'none' || style.visibility === 'hidden') {
              return NodeFilter.FILTER_REJECT;
            }
            
            const tagName = parent.tagName.toLowerCase();
            if (['script', 'style', 'noscript', 'nav', 'header', 'footer'].includes(tagName)) {
              return NodeFilter.FILTER_REJECT;
            }
            
            return NodeFilter.FILTER_ACCEPT;
          }
        }
      );

      const textNodes: string[] = [];
      let node;
      while (node = walker.nextNode()) {
        const text = node.textContent?.trim();
        if (text && text.length > 3) {
          textNodes.push(text);
        }
      }

      // Extract headings
      const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6')).map(h => ({
        level: parseInt(h.tagName.charAt(1)),
        text: h.textContent?.trim() || '',
      }));

      // Extract paragraphs
      const paragraphs = Array.from(document.querySelectorAll('p')).map(p => p.textContent?.trim() || '').filter(text => text.length > 0);

      // Extract lists
      const lists = Array.from(document.querySelectorAll('ul, ol')).map(list => list.textContent?.trim() || '').filter(text => text.length > 0);

      // Count tables and links
      const tables = document.querySelectorAll('table').length;
      const links = document.querySelectorAll('a[href]').length;

      return {
        textContent: textNodes.join(' '),
        headings,
        paragraphs,
        lists,
        tables,
        links,
      };
    });
  }

  /**
   * Analyze readability metrics
   */
  private analyzeReadability(textContent: string): ReadabilityMetrics {
    const words = textContent.split(/\s+/).filter(word => word.length > 0);
    const sentences = textContent.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const syllables = this.countTotalSyllables(words);

    const avgWordsPerSentence = sentences.length > 0 ? words.length / sentences.length : 0;
    const avgSyllablesPerWord = words.length > 0 ? syllables / words.length : 0;

    // Calculate various readability metrics
    const fleschReadingEase = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);
    const fleschKincaidGrade = (0.39 * avgWordsPerSentence) + (11.8 * avgSyllablesPerWord) - 15.59;
    
    // Simplified Gunning Fog Index
    const complexWords = words.filter(word => this.countSyllables(word) >= 3).length;
    const gunningFogIndex = 0.4 * (avgWordsPerSentence + (100 * complexWords / words.length));
    
    // Simplified Coleman-Liau Index
    const avgSentencesPer100Words = sentences.length > 0 ? (sentences.length / words.length) * 100 : 0;
    const avgLettersPer100Words = textContent.replace(/\s/g, '').length / words.length * 100;
    const colemanLiauIndex = (0.0588 * avgLettersPer100Words) - (0.296 * avgSentencesPer100Words) - 15.8;
    
    // Automated Readability Index
    const automatedReadabilityIndex = (4.71 * (textContent.replace(/\s/g, '').length / words.length)) + (0.5 * avgWordsPerSentence) - 21.43;

    const averageGrade = (fleschKincaidGrade + gunningFogIndex + colemanLiauIndex + automatedReadabilityIndex) / 4;

    return {
      fleschKincaidGrade: Math.round(fleschKincaidGrade * 10) / 10,
      fleschReadingEase: Math.round(fleschReadingEase * 10) / 10,
      gunningFogIndex: Math.round(gunningFogIndex * 10) / 10,
      colemanLiauIndex: Math.round(colemanLiauIndex * 10) / 10,
      automatedReadabilityIndex: Math.round(automatedReadabilityIndex * 10) / 10,
      averageGrade: Math.round(averageGrade * 10) / 10,
      readingLevel: this.determineReadingLevel(averageGrade),
      difficulty: this.determineDifficulty(fleschReadingEase),
    };
  }

  /**
   * Analyze semantic structure
   */
  private analyzeSemanticStructure(contentData: any): SemanticAnalysis {
    const words = contentData.textContent.split(/\s+/).filter((word: string) => word.length > 0);
    const sentences = contentData.textContent.split(/[.!?]+/).filter((s: string) => s.trim().length > 0);
    
    // Analyze vocabulary
    const uniqueWords = new Set(words.map((word: string) => word.toLowerCase())).size;
    const vocabularyRichness = words.length > 0 ? uniqueWords / words.length : 0;
    
    // Identify complex words
    const complexWords = words.filter((word: string) => 
      word.length > 7 || this.countSyllables(word) >= this.config.complexWordThreshold
    ).slice(0, 20);

    // Analyze sentence structure
    const sentenceLengths = sentences.map((s: string) => s.split(/\s+/).length);
    const averageSentenceLength = sentenceLengths.length > 0 
      ? sentenceLengths.reduce((sum: number, len: number) => sum + len, 0) / sentenceLengths.length 
      : 0;
    
    const sentenceVariability = this.calculateVariability(sentenceLengths);
    const complexSentences = sentenceLengths.filter(len => len > this.config.maximumSentenceLength).length;

    // Analyze content structure
    const hasIntroduction = contentData.paragraphs.length > 0 && contentData.paragraphs[0].length > 50;
    const hasConclusion = contentData.paragraphs.length > 1 && 
      contentData.paragraphs[contentData.paragraphs.length - 1].length > 30;
    const hasLogicalFlow = contentData.headings.length > 0 && contentData.paragraphs.length > 2;

    return {
      contentStructure: {
        hasIntroduction,
        hasConclusion,
        hasLogicalFlow,
        paragraphCount: contentData.paragraphs.length,
        averageParagraphLength: contentData.paragraphs.length > 0 
          ? contentData.paragraphs.reduce((sum: number, p: string) => sum + p.split(/\s+/).length, 0) / contentData.paragraphs.length 
          : 0,
      },
      vocabularyAnalysis: {
        uniqueWords,
        totalWords: words.length,
        vocabularyRichness: Math.round(vocabularyRichness * 100) / 100,
        complexWords,
        technicalTerms: [], // Would be populated with domain-specific analysis
        jargonLevel: vocabularyRichness > 0.7 ? 'high' : vocabularyRichness > 0.5 ? 'medium' : 'low',
      },
      sentenceAnalysis: {
        averageLength: Math.round(averageSentenceLength * 10) / 10,
        variability: Math.round(sentenceVariability * 10) / 10,
        complexSentences,
        passiveVoice: 0, // Would require more sophisticated analysis
        readabilityIssues: this.identifyReadabilityIssues(averageSentenceLength, complexSentences, sentences.length),
      },
    };
  }

  /**
   * Analyze language and multilingual aspects
   */
  private async analyzeLanguage(page: Page, textContent: string): Promise<LanguageAnalysis> {
    let detectedLanguage = 'en';
    let confidence = 0.5;

    // Use language detection library if available
    if (languageDetector) {
      try {
        detectedLanguage = languageDetector(textContent);
        confidence = 0.8; // Simplified confidence
      } catch (error) {
        logger.debug('Language detection failed', { error });
      }
    }

    // Check for language attributes and changes
    const languageData = await page.evaluate(() => {
      const htmlLang = document.documentElement.lang;
      const elementsWithLang = Array.from(document.querySelectorAll('[lang]'));
      const missingLangElements = Array.from(document.querySelectorAll('*')).filter(el => {
        const text = el.textContent?.trim();
        return text && text.length > 20 && !el.closest('[lang]') && !el.lang;
      });

      return {
        htmlLang,
        languageChanges: elementsWithLang.map(el => ({
          element: el.tagName.toLowerCase(),
          language: el.getAttribute('lang') || '',
          confidence: 0.9,
        })),
        missingLanguageAttributes: missingLangElements.slice(0, 5).map(el =>
          el.tagName.toLowerCase()
        ),
      };
    });

    const isMultilingual = languageData.languageChanges.length > 0;
    const recommendations: string[] = [];

    if (!languageData.htmlLang) {
      recommendations.push('Add lang attribute to html element');
    }

    if (languageData.missingLanguageAttributes.length > 0) {
      recommendations.push('Add lang attributes to content in different languages');
    }

    if (isMultilingual && languageData.languageChanges.length < 2) {
      recommendations.push('Ensure all language changes are properly marked');
    }

    return {
      detectedLanguage,
      confidence,
      isMultilingual,
      languageChanges: languageData.languageChanges,
      missingLanguageAttributes: languageData.missingLanguageAttributes,
      recommendations,
    };
  }

  /**
   * Analyze contextual content aspects
   */
  private async analyzeContextualContent(page: Page, contentData: any): Promise<ContextualContentAnalysis> {
    // Determine content type
    const contentType = this.determineContentType(contentData);

    // Determine audience level
    const audienceLevel = this.determineAudienceLevel(contentData);

    // Analyze purpose clarity
    const purposeClarity = this.analyzePurposeClarity(contentData);

    // Analyze information architecture
    const informationArchitecture = this.analyzeInformationArchitecture(contentData);

    // Analyze cognitive load
    const cognitiveLoad = this.analyzeCognitiveLoad(contentData, informationArchitecture);

    return {
      contentType,
      audienceLevel,
      purposeClarity,
      informationArchitecture,
      cognitiveLoad,
    };
  }

  /**
   * Calculate accessibility scoring
   */
  private calculateAccessibilityScoring(
    readability: ReadabilityMetrics,
    semantic: SemanticAnalysis,
    language: LanguageAnalysis,
    contextual: ContextualContentAnalysis
  ): AccessibilityContentScoring {
    // Calculate individual scores
    const readabilityScore = this.scoreReadability(readability);
    const structureScore = this.scoreStructure(semantic, contextual);
    const languageScore = this.scoreLanguage(language);
    const cognitiveScore = this.scoreCognitive(contextual);

    // Calculate overall score
    const overallScore = Math.round((readabilityScore + structureScore + languageScore + cognitiveScore) / 4);

    // Determine level
    const level = this.determineAccessibilityLevel(overallScore);

    // Generate improvements
    const improvements = this.generateImprovements(readability, semantic, language, contextual);

    return {
      readabilityScore,
      structureScore,
      languageScore,
      cognitiveScore,
      overallScore,
      level,
      improvements,
    };
  }

  /**
   * Generate content summary
   */
  private generateContentSummary(
    readability: ReadabilityMetrics,
    semantic: SemanticAnalysis,
    language: LanguageAnalysis,
    contextual: ContextualContentAnalysis,
    accessibility: AccessibilityContentScoring
  ): ContentQualityReport['summary'] {
    const strengths: string[] = [];
    const weaknesses: string[] = [];
    const priorityRecommendations: string[] = [];

    // Analyze strengths
    if (readability.fleschReadingEase > 70) {
      strengths.push('Good readability score');
    }
    if (semantic.contentStructure.hasLogicalFlow) {
      strengths.push('Well-structured content');
    }
    if (language.confidence > 0.8) {
      strengths.push('Clear language usage');
    }
    if (contextual.cognitiveLoad.overallLoad === 'low') {
      strengths.push('Low cognitive load');
    }

    // Analyze weaknesses
    if (readability.averageGrade > 12) {
      weaknesses.push('Content may be too complex');
      priorityRecommendations.push('Simplify sentence structure and vocabulary');
    }
    if (!semantic.contentStructure.hasLogicalFlow) {
      weaknesses.push('Poor content structure');
      priorityRecommendations.push('Improve content organization with headings');
    }
    if (language.missingLanguageAttributes.length > 0) {
      weaknesses.push('Missing language attributes');
      priorityRecommendations.push('Add proper language markup');
    }
    if (contextual.cognitiveLoad.overallLoad === 'high') {
      weaknesses.push('High cognitive load');
      priorityRecommendations.push('Reduce information density');
    }

    return {
      overallQuality: accessibility.overallScore,
      strengths,
      weaknesses,
      priorityRecommendations: priorityRecommendations.slice(0, 5),
    };
  }

  /**
   * Helper methods for calculations
   */
  private countSyllables(word: string): number {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;

    word = word.replace(/(?:[^laeiouy]es|ed|[^laeiouy]e)$/, '');
    word = word.replace(/^y/, '');

    const matches = word.match(/[aeiouy]{1,2}/g);
    return matches ? Math.max(1, matches.length) : 1;
  }

  private countTotalSyllables(words: string[]): number {
    return words.reduce((total, word) => total + this.countSyllables(word), 0);
  }

  private determineReadingLevel(grade: number): ReadabilityMetrics['readingLevel'] {
    if (grade <= 6) return 'elementary';
    if (grade <= 8) return 'middle-school';
    if (grade <= 12) return 'high-school';
    if (grade <= 16) return 'college';
    return 'graduate';
  }

  private determineDifficulty(fleschScore: number): ReadabilityMetrics['difficulty'] {
    if (fleschScore >= 90) return 'very-easy';
    if (fleschScore >= 80) return 'easy';
    if (fleschScore >= 70) return 'fairly-easy';
    if (fleschScore >= 60) return 'standard';
    if (fleschScore >= 50) return 'fairly-difficult';
    if (fleschScore >= 30) return 'difficult';
    return 'very-difficult';
  }

  private calculateVariability(values: number[]): number {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }

  private identifyReadabilityIssues(avgLength: number, complexSentences: number, totalSentences: number): string[] {
    const issues: string[] = [];

    if (avgLength > this.config.maximumSentenceLength) {
      issues.push('Sentences are too long on average');
    }

    if (complexSentences / totalSentences > 0.3) {
      issues.push('Too many complex sentences');
    }

    return issues;
  }

  private determineContentType(contentData: any): ContextualContentAnalysis['contentType'] {
    if (contentData.headings.length > 3 && contentData.paragraphs.length > 5) {
      return 'informational';
    }
    if (contentData.lists.length > 2) {
      return 'instructional';
    }
    if (contentData.links > contentData.paragraphs.length) {
      return 'navigational';
    }
    return 'mixed';
  }

  private determineAudienceLevel(contentData: any): ContextualContentAnalysis['audienceLevel'] {
    const avgWordLength = contentData.textContent.split(/\s+/)
      .reduce((sum: number, word: string) => sum + word.length, 0) /
      contentData.textContent.split(/\s+/).length;

    if (avgWordLength <= 4) return 'general';
    if (avgWordLength <= 6) return 'professional';
    if (avgWordLength <= 8) return 'technical';
    return 'academic';
  }

  private analyzePurposeClarity(contentData: any): number {
    let score = 50;

    if (contentData.headings.length > 0) score += 20;
    if (contentData.paragraphs.length > 0) score += 15;
    if (contentData.textContent.length > 200) score += 15;

    return Math.min(100, score);
  }

  private analyzeInformationArchitecture(contentData: any): ContextualContentAnalysis['informationArchitecture'] {
    const hasHeadings = contentData.headings.length > 0;
    const headingHierarchy = contentData.headings.map((h: any) => h.level);
    const hasLists = contentData.lists.length > 0;
    const hasTables = contentData.tables > 0;
    const hasDefinitions = false; // Would require more sophisticated analysis

    let structureScore = 0;
    if (hasHeadings) structureScore += 30;
    if (hasLists) structureScore += 20;
    if (hasTables) structureScore += 15;
    if (headingHierarchy.length > 1) structureScore += 25;
    if (contentData.paragraphs.length > 2) structureScore += 10;

    return {
      hasHeadings,
      headingHierarchy,
      hasLists,
      hasTables,
      hasDefinitions,
      structureScore: Math.min(100, structureScore),
    };
  }

  private analyzeCognitiveLoad(contentData: any, architecture: any): ContextualContentAnalysis['cognitiveLoad'] {
    const wordCount = contentData.textContent.split(/\s+/).length;
    const informationDensity = wordCount / Math.max(1, contentData.paragraphs.length);
    const conceptualComplexity = contentData.headings.length > 0 ? contentData.headings.length * 10 : 50;
    const navigationComplexity = contentData.links > 10 ? 80 : contentData.links * 8;

    const averageLoad = (informationDensity + conceptualComplexity + navigationComplexity) / 3;

    let overallLoad: ContextualContentAnalysis['cognitiveLoad']['overallLoad'];
    if (averageLoad < 30) overallLoad = 'low';
    else if (averageLoad < 60) overallLoad = 'medium';
    else if (averageLoad < 80) overallLoad = 'high';
    else overallLoad = 'very-high';

    return {
      informationDensity: Math.round(informationDensity),
      conceptualComplexity: Math.round(conceptualComplexity),
      navigationComplexity: Math.round(navigationComplexity),
      overallLoad,
    };
  }

  private scoreReadability(readability: ReadabilityMetrics): number {
    // Score based on Flesch Reading Ease
    if (readability.fleschReadingEase >= this.config.readabilityThresholds.excellent) return 90;
    if (readability.fleschReadingEase >= this.config.readabilityThresholds.good) return 80;
    if (readability.fleschReadingEase >= this.config.readabilityThresholds.fair) return 70;
    if (readability.fleschReadingEase >= this.config.readabilityThresholds.poor) return 60;
    return 50;
  }

  private scoreStructure(semantic: SemanticAnalysis, contextual: ContextualContentAnalysis): number {
    let score = 0;

    if (semantic.contentStructure.hasLogicalFlow) score += 25;
    if (semantic.contentStructure.hasIntroduction) score += 20;
    if (semantic.contentStructure.hasConclusion) score += 15;
    if (contextual.informationArchitecture.structureScore > 70) score += 25;
    if (semantic.vocabularyAnalysis.vocabularyRichness > 0.5) score += 15;

    return Math.min(100, score);
  }

  private scoreLanguage(language: LanguageAnalysis): number {
    let score = 70; // Base score

    if (language.confidence > 0.8) score += 15;
    if (language.missingLanguageAttributes.length === 0) score += 15;
    if (language.isMultilingual && language.languageChanges.length > 0) score += 10;
    else if (!language.isMultilingual) score += 10;

    return Math.min(100, score);
  }

  private scoreCognitive(contextual: ContextualContentAnalysis): number {
    let score = 100;

    switch (contextual.cognitiveLoad.overallLoad) {
      case 'low': score = 90; break;
      case 'medium': score = 75; break;
      case 'high': score = 60; break;
      case 'very-high': score = 40; break;
    }

    if (contextual.purposeClarity > 80) score += 10;

    return Math.min(100, score);
  }

  private determineAccessibilityLevel(score: number): AccessibilityContentScoring['level'] {
    if (score >= 85) return 'excellent';
    if (score >= 75) return 'good';
    if (score >= 65) return 'fair';
    if (score >= 50) return 'poor';
    return 'very-poor';
  }

  private generateImprovements(
    readability: ReadabilityMetrics,
    semantic: SemanticAnalysis,
    language: LanguageAnalysis,
    contextual: ContextualContentAnalysis
  ): AccessibilityContentScoring['improvements'] {
    const improvements: AccessibilityContentScoring['improvements'] = [];

    if (readability.fleschReadingEase < 60) {
      improvements.push({
        category: 'Readability',
        issue: 'Content is difficult to read',
        recommendation: 'Simplify sentence structure and use common vocabulary',
        impact: 'high',
      });
    }

    if (!semantic.contentStructure.hasLogicalFlow) {
      improvements.push({
        category: 'Structure',
        issue: 'Poor content organization',
        recommendation: 'Add clear headings and logical content flow',
        impact: 'high',
      });
    }

    if (language.missingLanguageAttributes.length > 0) {
      improvements.push({
        category: 'Language',
        issue: 'Missing language attributes',
        recommendation: 'Add lang attributes to all content sections',
        impact: 'medium',
      });
    }

    if (contextual.cognitiveLoad.overallLoad === 'high') {
      improvements.push({
        category: 'Cognitive Load',
        issue: 'High information density',
        recommendation: 'Break content into smaller, digestible sections',
        impact: 'medium',
      });
    }

    return improvements.slice(0, 8);
  }

  private generateMinimalReport(): ContentQualityReport {
    return {
      readability: this.getEmptyReadability(),
      semantic: this.getEmptySemantic(),
      language: this.getEmptyLanguage(),
      contextual: this.getEmptyContextual(),
      accessibility: this.getEmptyAccessibilityScoring(),
      summary: {
        overallQuality: 0,
        strengths: [],
        weaknesses: ['Insufficient content for analysis'],
        priorityRecommendations: ['Add more meaningful content'],
      },
    };
  }

  private getEmptyReadability(): ReadabilityMetrics {
    return {
      fleschKincaidGrade: 0,
      fleschReadingEase: 0,
      gunningFogIndex: 0,
      colemanLiauIndex: 0,
      automatedReadabilityIndex: 0,
      averageGrade: 0,
      readingLevel: 'elementary',
      difficulty: 'very-easy',
    };
  }

  private getEmptySemantic(): SemanticAnalysis {
    return {
      contentStructure: {
        hasIntroduction: false,
        hasConclusion: false,
        hasLogicalFlow: false,
        paragraphCount: 0,
        averageParagraphLength: 0,
      },
      vocabularyAnalysis: {
        uniqueWords: 0,
        totalWords: 0,
        vocabularyRichness: 0,
        complexWords: [],
        technicalTerms: [],
        jargonLevel: 'low',
      },
      sentenceAnalysis: {
        averageLength: 0,
        variability: 0,
        complexSentences: 0,
        passiveVoice: 0,
        readabilityIssues: [],
      },
    };
  }

  private getEmptyLanguage(): LanguageAnalysis {
    return {
      detectedLanguage: 'en',
      confidence: 0,
      isMultilingual: false,
      languageChanges: [],
      missingLanguageAttributes: [],
      recommendations: [],
    };
  }

  private getEmptyContextual(): ContextualContentAnalysis {
    return {
      contentType: 'mixed',
      audienceLevel: 'general',
      purposeClarity: 0,
      informationArchitecture: {
        hasHeadings: false,
        headingHierarchy: [],
        hasLists: false,
        hasTables: false,
        hasDefinitions: false,
        structureScore: 0,
      },
      cognitiveLoad: {
        informationDensity: 0,
        conceptualComplexity: 0,
        navigationComplexity: 0,
        overallLoad: 'low',
      },
    };
  }

  private getEmptyAccessibilityScoring(): AccessibilityContentScoring {
    return {
      readabilityScore: 0,
      structureScore: 0,
      languageScore: 0,
      cognitiveScore: 0,
      overallScore: 0,
      level: 'very-poor',
      improvements: [],
    };
  }
}

export default ContentQualityAnalyzer;
