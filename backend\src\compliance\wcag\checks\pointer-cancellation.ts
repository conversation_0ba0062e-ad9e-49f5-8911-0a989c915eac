/**
 * WCAG-054: Pointer Cancellation Check
 * Success Criterion: 2.5.2 Pointer Cancellation (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig } from '../utils/check-template';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

export class PointerCancellationCheck {
  private checkTemplate = new CheckTemplate();

  async performCheck(config: CheckConfig): Promise<WcagCheckResultEnhanced> {
    const result = await this.checkTemplate.executeCheck(
      'WCAG-054',
      'Pointer Cancellation',
      'operable',
      0.0458,
      'A',
      config,
      this.executePointerCancellationCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with pointer interaction analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-054',
        ruleName: 'Pointer Cancellation',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.7,
          checkType: 'pointer-interaction-analysis',
          pointerEventTesting: true,
          cancellationMechanisms: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.75,
        maxEvidenceItems: 25,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executePointerCancellationCheck(
    page: Page,
    _config: CheckConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze pointer event handling for cancellation
    const cancellationAnalysis = await page.evaluate(() => {
      const problematicElements: Array<{
        selector: string;
        tagName: string;
        eventType: string;
        hasDownEvent: boolean;
        hasUpEvent: boolean;
        hasPreventDefault: boolean;
        isEssential: boolean;
        issues: string[];
        severity: 'error' | 'warning' | 'info';
      }> = [];

      // Check elements with pointer/mouse event handlers
      const elementsWithPointerEvents = document.querySelectorAll(`
        [onmousedown], [onmouseup], [onclick],
        [onpointerdown], [onpointerup], [onpointercancel],
        [ontouchstart], [ontouchend], [ontouchcancel],
        .clickable, .draggable, [role="button"]
      `);

      elementsWithPointerEvents.forEach((element, index) => {
        const issues: string[] = [];
        let severity: 'error' | 'warning' | 'info' = 'info';
        
        const hasMouseDown = element.hasAttribute('onmousedown');
        const hasMouseUp = element.hasAttribute('onmouseup');
        const hasClick = element.hasAttribute('onclick');
        const hasPointerDown = element.hasAttribute('onpointerdown');
        const hasPointerUp = element.hasAttribute('onpointerup');
        const hasTouchStart = element.hasAttribute('ontouchstart');
        const hasTouchEnd = element.hasAttribute('ontouchend');
        
        // Check for down events without corresponding up events
        if (hasMouseDown && !hasMouseUp && !hasClick) {
          issues.push('Mouse down event without up event or click handler');
          severity = 'warning';
        }
        
        if (hasPointerDown && !hasPointerUp) {
          issues.push('Pointer down event without up event');
          severity = 'warning';
        }
        
        if (hasTouchStart && !hasTouchEnd) {
          issues.push('Touch start event without end event');
          severity = 'warning';
        }
        
        // Check for immediate action on down events
        const downHandlers = [
          element.getAttribute('onmousedown'),
          element.getAttribute('onpointerdown'),
          element.getAttribute('ontouchstart')
        ].filter(Boolean);
        
        const hasImmediateAction = downHandlers.some(handler => 
          handler && (
            handler.includes('submit') ||
            handler.includes('navigate') ||
            handler.includes('delete') ||
            handler.includes('confirm') ||
            handler.includes('action')
          )
        );
        
        if (hasImmediateAction) {
          issues.push('Action triggered on down event (not cancellable)');
          severity = 'error';
        }
        
        // Determine if element is essential (form submission, navigation)
        const isEssential = element.tagName === 'BUTTON' && 
                           (element.getAttribute('type') === 'submit' ||
                            element.textContent?.toLowerCase().includes('submit'));
        
        // Check for drag operations
        const isDraggable = element.hasAttribute('draggable') || 
                           element.classList.contains('draggable');
        
        if (isDraggable && hasMouseDown && !hasMouseUp) {
          issues.push('Draggable element may not support cancellation');
          severity = 'warning';
        }
        
        if (issues.length > 0) {
          problematicElements.push({
            selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
            tagName: element.tagName.toLowerCase(),
            eventType: hasPointerDown ? 'pointer' : hasMouseDown ? 'mouse' : 'touch',
            hasDownEvent: hasMouseDown || hasPointerDown || hasTouchStart,
            hasUpEvent: hasMouseUp || hasPointerUp || hasTouchEnd,
            hasPreventDefault: false, // Cannot determine from static analysis
            isEssential,
            issues,
            severity,
          });
        }
      });

      // Check JavaScript for event handling patterns
      const scripts = document.querySelectorAll('script');
      scripts.forEach((script) => {
        const content = script.textContent || '';
        
        // Look for immediate actions on down events
        const downEventPatterns = [
          /addEventListener\s*\(\s*['"`]mousedown['"`]\s*,.*?\)/gs,
          /addEventListener\s*\(\s*['"`]pointerdown['"`]\s*,.*?\)/gs,
          /addEventListener\s*\(\s*['"`]touchstart['"`]\s*,.*?\)/gs,
        ];
        
        downEventPatterns.forEach(pattern => {
          const matches = content.match(pattern);
          if (matches) {
            matches.forEach(match => {
              // Check if the handler performs immediate actions
              if (match.includes('preventDefault') && 
                  (match.includes('submit') || match.includes('navigate'))) {
                problematicElements.push({
                  selector: 'script',
                  tagName: 'script',
                  eventType: 'javascript',
                  hasDownEvent: true,
                  hasUpEvent: false,
                  hasPreventDefault: true,
                  isEssential: false,
                  issues: ['JavaScript down event may prevent cancellation'],
                  severity: 'warning',
                });
              }
            });
          }
        });
      });

      return {
        problematicElements,
        totalPointerElements: elementsWithPointerEvents.length,
        problematicCount: problematicElements.length,
        immediateActionCount: problematicElements.filter(el => 
          el.issues.some(issue => issue.includes('immediate action'))
        ).length,
        missingUpEventCount: problematicElements.filter(el => 
          el.hasDownEvent && !el.hasUpEvent
        ).length,
      };
    });

    let score = 100;
    const elementCount = cancellationAnalysis.problematicCount;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      // Calculate score based on severity
      cancellationAnalysis.problematicElements.forEach((element) => {
        const deduction = element.severity === 'error' ? 15 : 
                         element.severity === 'warning' ? 8 : 3;
        score = Math.max(0, score - deduction);
      });

      issues.push(`${elementCount} elements with pointer cancellation issues found`);
      if (cancellationAnalysis.immediateActionCount > 0) {
        issues.push(`${cancellationAnalysis.immediateActionCount} elements trigger immediate actions`);
      }
      if (cancellationAnalysis.missingUpEventCount > 0) {
        issues.push(`${cancellationAnalysis.missingUpEventCount} elements missing up event handlers`);
      }

      cancellationAnalysis.problematicElements.forEach((element) => {
        evidence.push({
          type: 'code',
          description: `Pointer cancellation issue: ${element.issues.join(', ')}`,
          value: `${element.eventType} events | Down: ${element.hasDownEvent} | Up: ${element.hasUpEvent}`,
          selector: element.selector,
          elementCount: 1,
          affectedSelectors: [element.selector],
          severity: element.severity,
          fixExample: {
            before: this.getBeforeExample(element),
            after: this.getAfterExample(element),
            description: this.getFixDescription(element.issues),
            codeExample: this.getCodeExample(element.eventType),
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/pointer-cancellation.html',
              'https://www.w3.org/WAI/WCAG21/Techniques/G210',
              'https://www.w3.org/WAI/WCAG21/Techniques/G212'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              eventType: element.eventType,
              hasDownEvent: element.hasDownEvent,
              hasUpEvent: element.hasUpEvent,
              isEssential: element.isEssential,
              issues: element.issues,
            },
          },
        });
      });
    }

    // Add recommendations
    recommendations.push('Use up events (click, mouseup, pointerup) for triggering actions');
    recommendations.push('Avoid triggering actions on down events unless essential');
    recommendations.push('Provide abort or undo mechanisms for essential down events');
    recommendations.push('Allow users to cancel actions by moving pointer away');
    recommendations.push('Test pointer cancellation with various input methods');

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(element: any): string {
    if (element.issues.includes('immediate action')) {
      return '<button onmousedown="submitForm()">Submit</button>';
    }
    if (element.issues.includes('without up event')) {
      return '<div onmousedown="startAction()">Action</div>';
    }
    return `<${element.tagName} onmousedown="action()">Element</${element.tagName}>`;
  }

  private getAfterExample(element: any): string {
    if (element.issues.includes('immediate action')) {
      return '<button onclick="submitForm()">Submit</button>';
    }
    if (element.issues.includes('without up event')) {
      return '<div onmousedown="startAction()" onmouseup="completeAction()" onmouseleave="cancelAction()">Action</div>';
    }
    return `<${element.tagName} onclick="action()">Element</${element.tagName}>`;
  }

  private getFixDescription(issues: string[]): string {
    if (issues.includes('immediate action')) {
      return 'Move action to up event (click) instead of down event';
    }
    if (issues.includes('without up event')) {
      return 'Add up event handler to complete or cancel action';
    }
    if (issues.includes('prevent cancellation')) {
      return 'Allow users to cancel actions by moving pointer away';
    }
    return 'Ensure pointer actions can be cancelled';
  }

  private getCodeExample(eventType: string): string {
    switch (eventType) {
      case 'mouse':
        return `
<!-- Before: Action on mouse down (not cancellable) -->
<button onmousedown="deleteItem()">Delete</button>

<!-- After: Action on click (cancellable) -->
<button onclick="deleteItem()">Delete</button>

<!-- Alternative: Cancellable down event -->
<button onmousedown="startDelete()" 
        onmouseup="confirmDelete()" 
        onmouseleave="cancelDelete()">
  Delete
</button>
        `;
      case 'pointer':
        return `
<!-- Before: Immediate action on pointer down -->
<div onpointerdown="performAction(event)">Action</div>

<!-- After: Cancellable pointer interaction -->
<div onpointerdown="startAction(event)" 
     onpointerup="completeAction(event)"
     onpointercancel="cancelAction(event)"
     onpointerleave="cancelAction(event)">
  Action
</div>

<script>
let actionStarted = false;

function startAction(event) {
  actionStarted = true;
  // Visual feedback only, no irreversible action
  event.target.classList.add('action-pending');
}

function completeAction(event) {
  if (actionStarted) {
    // Perform the actual action
    performAction();
    actionStarted = false;
    event.target.classList.remove('action-pending');
  }
}

function cancelAction(event) {
  actionStarted = false;
  event.target.classList.remove('action-pending');
}
</script>
        `;
      default:
        return `
<!-- General pointer cancellation principles -->

<!-- 1. Use click events for simple actions -->
<button onclick="action()">Safe Action</button>

<!-- 2. For complex interactions, allow cancellation -->
<div class="interactive-element"
     onpointerdown="handlePointerDown(event)"
     onpointerup="handlePointerUp(event)"
     onpointercancel="handlePointerCancel(event)"
     onpointerleave="handlePointerLeave(event)">
  Complex Action
</div>

<!-- 3. Provide undo for essential immediate actions -->
<button onclick="essentialAction()">
  Essential Action
</button>
<button onclick="undoAction()" style="display: none;" id="undo-btn">
  Undo
</button>

<script>
function essentialAction() {
  // Perform essential action
  performEssentialAction();
  
  // Show undo option
  document.getElementById('undo-btn').style.display = 'inline';
  
  // Auto-hide undo after timeout
  setTimeout(() => {
    document.getElementById('undo-btn').style.display = 'none';
  }, 5000);
}
</script>
        `;
    }
  }
}
