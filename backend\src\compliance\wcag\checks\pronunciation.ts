/**
 * WCAG-063: Pronunciation Check (3.1.6 Level AAA)
 * 50% Automated - Detects words that may need pronunciation guidance
 */

import { Page } from 'puppeteer';
import { CheckConfig, CheckTemplate } from '../utils/check-template';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced, WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

export class PronunciationCheck {
  private checkTemplate = new CheckTemplate();

  // Words that commonly need pronunciation guidance
  private readonly pronunciationCandidates = new Set([
    // Foreign words commonly used in English
    'café', 'naïve', 'résumé', 'fiancé', 'fiancée', 'cliché', 'façade', 'protégé', 'début', 'coup',
    'genre', 'cache', 'niche', 'suite', 'route', 'vogue', 'rouge', 'beige', 'garage', 'mirage',
    'ballet', 'buffet', 'bouquet', 'croquet', 'gourmet', 'parquet', 'ricochet', 'silhouette',
    'karaoke', 'karate', 'tsunami', 'origami', 'sushi', 'sake', 'anime', 'manga', 'haiku',
    'pizza', 'pasta', 'cappuccino', 'espresso', 'gelato', 'bruschetta', 'gnocchi', 'chianti',
    'schadenfreude', 'kindergarten', 'doppelganger', 'zeitgeist', 'wanderlust', 'gesundheit',
    // Technical terms with ambiguous pronunciation
    'api', 'gui', 'sql', 'ajax', 'nginx', 'apache', 'mysql', 'postgresql', 'kubernetes',
    'debian', 'ubuntu', 'linux', 'nginx', 'redis', 'mongodb', 'elasticsearch',
    // Names and proper nouns that may need guidance
    'worcestershire', 'leicester', 'gloucester', 'edinburgh', 'birmingham',
    // Scientific/medical terms
    'pneumonia', 'mnemonic', 'psychology', 'psychiatry', 'pharmaceutical', 'ophthalmology',
    // Words with silent letters or unusual pronunciation
    'colonel', 'yacht', 'debris', 'chassis', 'rendezvous', 'bourgeois', 'entrepreneur',
  ]);

  async performCheck(config: CheckConfig): Promise<WcagCheckResultEnhanced> {
    const result = await this.checkTemplate.executeCheck(
      'WCAG-063',
      'Pronunciation',
      'understandable',
      0.0305,
      'AAA',
      config,
      this.executePronunciationCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with pronunciation analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-063',
        ruleName: 'Pronunciation',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.5,
          checkType: 'pronunciation-analysis',
          pronunciationGuidanceDetection: true,
          ambiguousWordIdentification: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.7,
        maxEvidenceItems: 30,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executePronunciationCheck(
    page: Page,
    config: CheckConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze text for words that may need pronunciation guidance
    const pronunciationAnalysis = await page.evaluate((candidateWords: string[]) => {
      const textElements = document.querySelectorAll('p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, article, section, main, aside');
      const wordsNeedingPronunciation: Array<{
        word: string;
        context: string;
        element: string;
        selector: string;
        hasPronunciationGuide: boolean;
        hasPhonetic: boolean;
        hasAudio: boolean;
        hasTooltip: boolean;
        frequency: number;
        category: string;
      }> = [];

      const wordFrequency = new Map<string, number>();
      const wordContexts = new Map<string, Array<{ context: string; element: string; selector: string }>>();
      const candidateSet = new Set(candidateWords.map(w => w.toLowerCase()));

      textElements.forEach((element, index) => {
        const text = element.textContent || '';
        if (text.trim().length < 10) return;

        const selector = generateSelector(element, index);
        const words = text.toLowerCase().match(/\b[a-záàâäãåæçéèêëíìîïñóòôöõøúùûüýÿ]+\b/g) || [];

        words.forEach(word => {
          if (candidateSet.has(word) || needsPronunciationGuidance(word)) {
            // Check for pronunciation guidance
            const hasPronunciationGuide = checkForPronunciationGuide(element, word);
            const hasPhonetic = checkForPhoneticNotation(element, word);
            const hasAudio = checkForAudioPronunciation(element, word);
            const hasTooltip = checkForPronunciationTooltip(element, word);

            // Get context
            const wordIndex = text.toLowerCase().indexOf(word);
            const contextStart = Math.max(0, wordIndex - 30);
            const contextEnd = Math.min(text.length, wordIndex + word.length + 30);
            const context = text.substring(contextStart, contextEnd).trim();

            // Track frequency
            const currentFreq = wordFrequency.get(word) || 0;
            wordFrequency.set(word, currentFreq + 1);

            // Track contexts
            if (!wordContexts.has(word)) {
              wordContexts.set(word, []);
            }
            wordContexts.get(word)!.push({
              context,
              element: element.tagName.toLowerCase(),
              selector,
            });
          }
        });
      });

      // Process collected words
      wordFrequency.forEach((frequency, word) => {
        const contexts = wordContexts.get(word) || [];
        const firstContext = contexts[0];
        
        if (firstContext) {
          const element = document.querySelector(firstContext.selector);
          const hasPronunciationGuide = element ? checkForPronunciationGuide(element, word) : false;
          const hasPhonetic = element ? checkForPhoneticNotation(element, word) : false;
          const hasAudio = element ? checkForAudioPronunciation(element, word) : false;
          const hasTooltip = element ? checkForPronunciationTooltip(element, word) : false;

          wordsNeedingPronunciation.push({
            word,
            context: firstContext.context,
            element: firstContext.element,
            selector: firstContext.selector,
            hasPronunciationGuide,
            hasPhonetic,
            hasAudio,
            hasTooltip,
            frequency,
            category: categorizeWord(word, candidateWords),
          });
        }
      });

      return {
        wordsNeedingPronunciation,
        totalTextElements: textElements.length,
        totalWords: Array.from(wordFrequency.values()).reduce((sum, freq) => sum + freq, 0),
      };

      function generateSelector(element: Element, index: number): string {
        if (element.id) return `#${element.id}`;
        if (element.className) {
          const classes = element.className.split(' ').filter(c => c.trim());
          if (classes.length > 0) return `.${classes[0]}`;
        }
        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function needsPronunciationGuidance(word: string): boolean {
        // Additional heuristics for words that might need pronunciation guidance
        return (
          // Words with unusual letter combinations
          /(?:ough|augh|eigh|tion|sion|cion|phth|chth|gn|kn|wr|mb|bt|pt|ct|ft|ght)/i.test(word) ||
          // Words with silent letters patterns
          /(?:^[gkpw]n|^ps|^pt|mb$|bt$|^wr|^gn|^kn|lm$|st[^aeiou]|^rh)/i.test(word) ||
          // Foreign-looking words
          /[àáâäãåæçéèêëíìîïñóòôöõøúùûüýÿ]/i.test(word) ||
          // Very long words that might be complex
          word.length > 12
        );
      }

      function checkForPronunciationGuide(element: Element, word: string): boolean {
        const text = element.textContent || '';
        // Look for pronunciation guides in parentheses or brackets
        const pronunciationPatterns = [
          new RegExp(`${word}\\s*\\([^)]*(?:pronounced|sounds like|say)[^)]*\\)`, 'i'),
          new RegExp(`${word}\\s*\\[[^\\]]*(?:pronounced|sounds like|say)[^\\]]*\\]`, 'i'),
          new RegExp(`${word}\\s*[/\\\\][^/\\\\]*[/\\\\]`, 'i'), // Phonetic notation
        ];
        return pronunciationPatterns.some(pattern => pattern.test(text));
      }

      function checkForPhoneticNotation(element: Element, word: string): boolean {
        const text = element.textContent || '';
        // Look for IPA or phonetic notation
        return new RegExp(`${word}\\s*[/\\\\][^/\\\\]*[/\\\\]`, 'i').test(text) ||
               element.querySelector(`[data-pronunciation*="${word}"], [data-phonetic*="${word}"]`) !== null;
      }

      function checkForAudioPronunciation(element: Element, word: string): boolean {
        // Check for audio elements or pronunciation buttons
        return element.querySelector(`audio[data-word*="${word}"], button[data-pronunciation*="${word}"], .pronunciation-audio`) !== null ||
               element.querySelector(`[onclick*="pronounce"][onclick*="${word}"]`) !== null;
      }

      function checkForPronunciationTooltip(element: Element, word: string): boolean {
        // Check for tooltips with pronunciation information
        const wordElement = element.querySelector(`[title*="${word}"], [data-tooltip*="${word}"]`);
        if (wordElement) {
          const title = wordElement.getAttribute('title') || wordElement.getAttribute('data-tooltip') || '';
          return /(?:pronounced|sounds like|say|pronunciation)/i.test(title);
        }
        return false;
      }

      function categorizeWord(word: string, candidateWords: string[]): string {
        if (candidateWords.includes(word)) {
          if (/café|naïve|résumé|fiancé|genre|ballet|karaoke|pizza|schadenfreude/.test(word)) {
            return 'foreign-word';
          }
          if (/api|gui|sql|ajax|nginx|apache/.test(word)) {
            return 'technical-term';
          }
          if (/worcestershire|leicester|gloucester/.test(word)) {
            return 'proper-noun';
          }
          if (/pneumonia|mnemonic|psychology/.test(word)) {
            return 'scientific-term';
          }
        }
        return 'complex-word';
      }
    }, Array.from(this.pronunciationCandidates));

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const elementCount = pronunciationAnalysis.wordsNeedingPronunciation.length;

    if (elementCount > 0) {
      // Filter words that don't have pronunciation guidance
      const wordsWithoutGuidance = pronunciationAnalysis.wordsNeedingPronunciation.filter(
        word => !word.hasPronunciationGuide && !word.hasPhonetic && !word.hasAudio && !word.hasTooltip
      );

      if (wordsWithoutGuidance.length > 0) {
        // Calculate score based on number of words without pronunciation guidance
        const penalty = Math.min(40, wordsWithoutGuidance.length * 6);
        score -= penalty;
        
        issues.push(`${wordsWithoutGuidance.length} words may need pronunciation guidance`);

        evidence.push({
          type: 'content',
          description: 'Words that may need pronunciation guidance',
          value: `Found ${wordsWithoutGuidance.length} words that could benefit from pronunciation guidance for AAA compliance`,
          elementCount: wordsWithoutGuidance.length,
          affectedSelectors: wordsWithoutGuidance.map(word => word.selector),
          severity: 'warning',
          fixExample: {
            before: 'The API uses a GUI interface.',
            after: 'The API (pronounced "A-P-I") uses a GUI (pronounced "gooey") interface.',
            description: 'Provide pronunciation guidance for words that may be ambiguous',
            codeExample: `
<!-- Before: No pronunciation guidance -->
<p>Our API uses AJAX for asynchronous requests to the SQL database.</p>

<!-- After: With pronunciation guidance -->
<p>Our <abbr title="Application Programming Interface (pronounced A-P-I)">API</abbr> 
uses <abbr title="Asynchronous JavaScript and XML (pronounced AY-jaks)">AJAX</abbr> 
for asynchronous requests to the <abbr title="Structured Query Language (pronounced SEE-kwel)">SQL</abbr> database.</p>

<!-- Alternative: With phonetic notation -->
<p>The word "cache" <span class="pronunciation">/kæʃ/</span> refers to temporary storage.</p>

<!-- Alternative: With audio pronunciation -->
<p>The word "nginx" 
<button type="button" onclick="playPronunciation('nginx')" aria-label="Play pronunciation of nginx">
  🔊
</button> 
is a web server.</p>

<!-- Alternative: With tooltip -->
<p>The <span title="Pronounced: zhahn-ruh">genre</span> of music was classical.</p>
            `,
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/pronunciation.html',
              'https://en.wikipedia.org/wiki/International_Phonetic_Alphabet',
              'https://developer.mozilla.org/en-US/docs/Web/HTML/Element/audio'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: pronunciationAnalysis.totalTextElements,
            checkSpecificData: {
              totalWordsNeedingGuidance: elementCount,
              wordsWithoutGuidance: wordsWithoutGuidance.length,
              wordsWithGuidance: elementCount - wordsWithoutGuidance.length,
              categoryBreakdown: {
                foreignWords: wordsWithoutGuidance.filter(w => w.category === 'foreign-word').length,
                technicalTerms: wordsWithoutGuidance.filter(w => w.category === 'technical-term').length,
                properNouns: wordsWithoutGuidance.filter(w => w.category === 'proper-noun').length,
                scientificTerms: wordsWithoutGuidance.filter(w => w.category === 'scientific-term').length,
                complexWords: wordsWithoutGuidance.filter(w => w.category === 'complex-word').length,
              },
            },
          },
        });

        // Add specific examples for words without guidance
        wordsWithoutGuidance.slice(0, 10).forEach(wordInfo => {
          evidence.push({
            type: 'content',
            description: `Word may need pronunciation guidance: "${wordInfo.word}"`,
            value: `Context: "${wordInfo.context}" (Category: ${wordInfo.category})`,
            selector: wordInfo.selector,
            severity: 'warning',
            metadata: {
              checkSpecificData: {
                word: wordInfo.word,
                category: wordInfo.category,
                frequency: wordInfo.frequency,
                hasPronunciationGuide: wordInfo.hasPronunciationGuide,
                hasPhonetic: wordInfo.hasPhonetic,
                hasAudio: wordInfo.hasAudio,
                hasTooltip: wordInfo.hasTooltip,
              },
            },
          });
        });

        recommendations.push('Provide pronunciation guidance for foreign words and technical terms');
        recommendations.push('Use phonetic notation (IPA) or simple pronunciation guides');
        recommendations.push('Consider audio pronunciation for complex words');
        recommendations.push('Use tooltips or expandable sections for pronunciation information');
        recommendations.push('Test pronunciation guidance with users who may be unfamiliar with the terms');
      } else {
        // All words have pronunciation guidance
        evidence.push({
          type: 'info',
          description: 'Words with pronunciation guidance found',
          value: `Found ${elementCount} words that may need pronunciation guidance, all with appropriate guidance provided`,
          elementCount,
          severity: 'info',
          metadata: {
            scanDuration,
            elementsAnalyzed: pronunciationAnalysis.totalTextElements,
            checkSpecificData: {
              totalWordsNeedingGuidance: elementCount,
              allHaveGuidance: true,
            },
          },
        });
      }
    } else {
      // No words needing pronunciation guidance found
      evidence.push({
        type: 'info',
        description: 'No words requiring pronunciation guidance detected',
        value: 'Page content uses familiar words that do not require pronunciation guidance',
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: pronunciationAnalysis.totalTextElements,
          checkSpecificData: {
            noWordsNeedingGuidance: true,
            totalWords: pronunciationAnalysis.totalWords,
          },
        },
      });
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
