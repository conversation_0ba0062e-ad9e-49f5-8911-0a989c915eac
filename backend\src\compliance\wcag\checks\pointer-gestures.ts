/**
 * WCAG-053: Pointer Gestures Check
 * Success Criterion: 2.5.1 Pointer Gestures (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface PointerGesturesConfig extends EnhancedCheckConfig {
  enableAdvancedGestureDetection?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
}

export class PointerGesturesCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();

  async performCheck(config: PointerGesturesConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: PointerGesturesConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAdvancedGestureDetection: true,
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-053',
      'Pointer Gestures',
      'operable',
      0.0458,
      'A',
      enhancedConfig,
      this.executePointerGesturesCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with pointer gesture analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-049',
        ruleName: 'Pointer Gestures',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.75,
          checkType: 'pointer-gesture-analysis',
          gestureDetection: true,
          alternativeInputValidation: true,
          advancedGestureDetection: enhancedConfig.enableAdvancedGestureDetection,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.75,
        maxEvidenceItems: 25,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executePointerGesturesCheck(
    page: Page,
    _config: PointerGesturesConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze pointer gesture implementations
    const gestureAnalysis = await page.evaluate(() => {
      const problematicGestures: Array<{
        selector: string;
        gestureType: string;
        hasAlternative: boolean;
        alternativeType: string;
        issues: string[];
        severity: 'error' | 'warning' | 'info';
      }> = [];

      // Check for touch event handlers that might implement gestures
      const elementsWithTouch = document.querySelectorAll(`
        [ontouchstart], [ontouchmove], [ontouchend],
        [onpointerdown], [onpointermove], [onpointerup],
        .swipeable, .pinchable, .rotatable, .gesture-enabled
      `);

      elementsWithTouch.forEach((element, index) => {
        const issues: string[] = [];
        let severity: 'error' | 'warning' | 'info' = 'warning';
        let gestureType = 'unknown';
        let hasAlternative = false;
        let alternativeType = 'none';

        // Check for swipe gestures
        if (element.classList.contains('swipeable') || 
            element.hasAttribute('ontouchmove')) {
          gestureType = 'swipe';
          
          // Look for alternative navigation
          const hasButtons = element.querySelector('button, [role="button"], .nav-button') !== null;
          const hasKeyboardNav = element.hasAttribute('onkeydown');
          
          if (hasButtons) {
            hasAlternative = true;
            alternativeType = 'buttons';
          } else if (hasKeyboardNav) {
            hasAlternative = true;
            alternativeType = 'keyboard';
          } else {
            issues.push('Swipe gesture without single-pointer alternative');
            severity = 'error';
          }
        }

        // Check for pinch/zoom gestures
        if (element.classList.contains('pinchable') ||
            element.classList.contains('zoomable')) {
          gestureType = 'pinch';
          
          // Look for zoom controls
          const hasZoomControls = element.querySelector('.zoom-in, .zoom-out, [aria-label*="zoom"]') !== null;
          
          if (hasZoomControls) {
            hasAlternative = true;
            alternativeType = 'zoom-controls';
          } else {
            issues.push('Pinch/zoom gesture without single-pointer alternative');
            severity = 'error';
          }
        }

        // Check for rotation gestures
        if (element.classList.contains('rotatable')) {
          gestureType = 'rotation';
          
          // Look for rotation controls
          const hasRotateControls = element.querySelector('.rotate-left, .rotate-right, [aria-label*="rotate"]') !== null;
          
          if (hasRotateControls) {
            hasAlternative = true;
            alternativeType = 'rotate-controls';
          } else {
            issues.push('Rotation gesture without single-pointer alternative');
            severity = 'error';
          }
        }

        // Check for drag gestures
        if (element.hasAttribute('draggable') || 
            element.classList.contains('draggable')) {
          gestureType = 'drag';
          
          // Look for keyboard alternatives
          const hasKeyboardDrag = element.hasAttribute('onkeydown') ||
                                 element.querySelector('[role="button"]') !== null;
          
          if (hasKeyboardDrag) {
            hasAlternative = true;
            alternativeType = 'keyboard';
          } else {
            issues.push('Drag gesture without keyboard alternative');
            severity = 'error';
          }
        }

        if (issues.length > 0 || gestureType !== 'unknown') {
          problematicGestures.push({
            selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
            gestureType,
            hasAlternative,
            alternativeType,
            issues,
            severity,
          });
        }
      });

      // Check JavaScript for gesture libraries
      const scripts = document.querySelectorAll('script');
      let hasGestureLibrary = false;
      const gestureLibraries = ['hammer.js', 'interact.js', 'gesture', 'swipe', 'pinch'];
      
      scripts.forEach((script) => {
        const content = script.textContent || '';
        const src = script.src || '';
        
        if (gestureLibraries.some(lib => content.includes(lib) || src.includes(lib))) {
          hasGestureLibrary = true;
        }
      });

      if (hasGestureLibrary) {
        problematicGestures.push({
          selector: 'script',
          gestureType: 'library',
          hasAlternative: false, // Cannot determine from static analysis
          alternativeType: 'unknown',
          issues: ['Gesture library detected - verify single-pointer alternatives'],
          severity: 'warning',
        });
      }

      return {
        problematicGestures,
        totalGestureElements: elementsWithTouch.length,
        problematicCount: problematicGestures.length,
        withoutAlternatives: problematicGestures.filter(g => !g.hasAlternative).length,
        hasGestureLibrary,
      };
    });

    let score = 100;
    const elementCount = gestureAnalysis.problematicCount;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      // Calculate score based on severity
      gestureAnalysis.problematicGestures.forEach((gesture) => {
        const deduction = gesture.severity === 'error' ? 20 : 
                         gesture.severity === 'warning' ? 10 : 5;
        score = Math.max(0, score - deduction);
      });

      issues.push(`${elementCount} elements with potential gesture issues found`);
      if (gestureAnalysis.withoutAlternatives > 0) {
        issues.push(`${gestureAnalysis.withoutAlternatives} gestures without single-pointer alternatives`);
      }

      gestureAnalysis.problematicGestures.forEach((gesture) => {
        evidence.push({
          type: 'code',
          description: `Pointer gesture issue: ${gesture.issues.join(', ')}`,
          value: `${gesture.gestureType} gesture | Alternative: ${gesture.alternativeType}`,
          selector: gesture.selector,
          elementCount: 1,
          affectedSelectors: [gesture.selector],
          severity: gesture.severity,
          fixExample: {
            before: this.getBeforeExample(gesture),
            after: this.getAfterExample(gesture),
            description: this.getFixDescription(gesture.gestureType),
            codeExample: this.getCodeExample(gesture.gestureType),
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/pointer-gestures.html',
              'https://www.w3.org/WAI/WCAG21/Techniques/G215',
              'https://www.w3.org/WAI/WCAG21/Techniques/G216'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              gestureType: gesture.gestureType,
              hasAlternative: gesture.hasAlternative,
              alternativeType: gesture.alternativeType,
              issues: gesture.issues,
            },
          },
        });
      });
    }

    // Add recommendations
    recommendations.push('Provide single-pointer alternatives for multipoint gestures');
    recommendations.push('Ensure path-based gestures have simple alternatives');
    recommendations.push('Add buttons or controls for swipe, pinch, and rotation actions');
    recommendations.push('Test functionality with single-pointer input only');
    recommendations.push('Consider users with limited dexterity or mobility');

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(gesture: any): string {
    switch (gesture.gestureType) {
      case 'swipe':
        return '<div class="swipeable" ontouchmove="handleSwipe()">Swipe to navigate</div>';
      case 'pinch':
        return '<div class="pinchable">Pinch to zoom</div>';
      case 'rotation':
        return '<div class="rotatable">Rotate to adjust</div>';
      case 'drag':
        return '<div draggable="true">Drag to reorder</div>';
      default:
        return '<div class="gesture-enabled">Gesture-controlled element</div>';
    }
  }

  private getAfterExample(gesture: any): string {
    switch (gesture.gestureType) {
      case 'swipe':
        return '<div class="carousel">\n  <button class="prev">Previous</button>\n  <div class="content">Content</div>\n  <button class="next">Next</button>\n</div>';
      case 'pinch':
        return '<div class="zoomable">\n  <button class="zoom-out">-</button>\n  <div class="content">Content</div>\n  <button class="zoom-in">+</button>\n</div>';
      case 'rotation':
        return '<div class="rotatable">\n  <button class="rotate-left">↺</button>\n  <div class="content">Content</div>\n  <button class="rotate-right">↻</button>\n</div>';
      case 'drag':
        return '<div class="sortable">\n  <button onclick="moveUp()">↑</button>\n  <div class="item">Item</div>\n  <button onclick="moveDown()">↓</button>\n</div>';
      default:
        return '<div class="accessible-control">Element with single-pointer alternative</div>';
    }
  }

  private getFixDescription(gestureType: string): string {
    switch (gestureType) {
      case 'swipe':
        return 'Add navigation buttons as alternative to swipe gestures';
      case 'pinch':
        return 'Provide zoom controls as alternative to pinch gestures';
      case 'rotation':
        return 'Add rotation buttons as alternative to rotation gestures';
      case 'drag':
        return 'Provide keyboard or button alternatives to drag operations';
      default:
        return 'Ensure single-pointer alternatives are available';
    }
  }

  private getCodeExample(gestureType: string): string {
    switch (gestureType) {
      case 'swipe':
        return `
<!-- Before: Swipe-only navigation -->
<div class="carousel" ontouchmove="handleSwipe(event)">
  <div class="slide">Slide 1</div>
  <div class="slide">Slide 2</div>
  <div class="slide">Slide 3</div>
</div>

<!-- After: Swipe with button alternatives -->
<div class="carousel">
  <button class="nav-btn prev" onclick="previousSlide()" aria-label="Previous slide">‹</button>
  <div class="slides" ontouchmove="handleSwipe(event)">
    <div class="slide">Slide 1</div>
    <div class="slide">Slide 2</div>
    <div class="slide">Slide 3</div>
  </div>
  <button class="nav-btn next" onclick="nextSlide()" aria-label="Next slide">›</button>
  <div class="indicators">
    <button onclick="goToSlide(0)" aria-label="Go to slide 1">1</button>
    <button onclick="goToSlide(1)" aria-label="Go to slide 2">2</button>
    <button onclick="goToSlide(2)" aria-label="Go to slide 3">3</button>
  </div>
</div>
        `;
      case 'pinch':
        return `
<!-- Before: Pinch-only zoom -->
<div class="image-viewer" ontouchstart="handlePinch(event)">
  <img src="image.jpg" alt="Zoomable image">
</div>

<!-- After: Pinch with zoom controls -->
<div class="image-viewer">
  <div class="zoom-controls">
    <button onclick="zoomOut()" aria-label="Zoom out">-</button>
    <span class="zoom-level">100%</span>
    <button onclick="zoomIn()" aria-label="Zoom in">+</button>
    <button onclick="resetZoom()" aria-label="Reset zoom">Reset</button>
  </div>
  <div class="image-container" ontouchstart="handlePinch(event)">
    <img src="image.jpg" alt="Zoomable image">
  </div>
</div>
        `;
      default:
        return `
<!-- General principle: Always provide single-pointer alternatives -->

<!-- Multi-touch gesture with single-pointer alternative -->
<div class="interactive-element">
  <!-- Gesture-enabled area -->
  <div class="gesture-area" 
       ontouchstart="handleGesture(event)"
       onpointerdown="handlePointer(event)">
    Content
  </div>
  
  <!-- Single-pointer alternatives -->
  <div class="controls">
    <button onclick="action1()">Action 1</button>
    <button onclick="action2()">Action 2</button>
    <button onclick="action3()">Action 3</button>
  </div>
</div>

<!-- Keyboard alternatives for complex gestures -->
<div class="sortable-list" onkeydown="handleKeyboardSort(event)">
  <div class="item" draggable="true" tabindex="0">
    Item 1
    <button onclick="moveUp(0)">↑</button>
    <button onclick="moveDown(0)">↓</button>
  </div>
</div>
        `;
    }
  }
}
