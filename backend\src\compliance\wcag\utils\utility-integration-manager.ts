/**
 * Utility Integration Manager
 * Centralized manager for integrating all WCAG utilities into checks
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';
import AISemanticValidator from './ai-semantic-validator';
import AccessibilityPatternLibrary from './accessibility-pattern-library';
import ContentQualityAnalyzer from './content-quality-analyzer';
import ModernFrameworkOptimizer from './modern-framework-optimizer';
import ComponentLibraryDetector from './component-library-detector';
import HeadlessCMSDetector from './headless-cms-detector';

export interface UtilityIntegrationConfig {
  // Utility enablement flags
  enableSemanticValidation?: boolean;
  enablePatternValidation?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableFrameworkOptimization?: boolean;
  enableComponentLibraryDetection?: boolean;
  enableCMSDetection?: boolean;
  
  // Integration strategy
  integrationStrategy?: 'supplement' | 'enhance' | 'validate';
  
  // Performance settings
  enableCaching?: boolean;
  maxExecutionTime?: number;
  
  // Fallback settings
  enableGracefulFallback?: boolean;
  fallbackOnError?: boolean;
}

export interface UtilityAnalysisResult {
  semanticAnalysis?: any;
  patternAnalysis?: any;
  contentQualityAnalysis?: any;
  frameworkAnalysis?: any;
  componentLibraryAnalysis?: any;
  cmsAnalysis?: any;
  
  // Integration metadata
  executionTime: number;
  utilitiesUsed: string[];
  errors: string[];
  recommendations: string[];
  
  // Enhanced scoring
  utilityConfidence: number;
  enhancedAccuracy: number;
}

export interface CheckEnhancementResult {
  originalScore: number;
  enhancedScore: number;
  confidenceBoost: number;
  additionalEvidence: any[];
  utilityRecommendations: string[];
  frameworkSpecificGuidance: string[];
}

/**
 * Priority mapping for different check types
 */
export const CHECK_UTILITY_PRIORITY: Record<string, UtilityIntegrationConfig> = {
  // High Priority - Content and Semantic Checks
  'WCAG-001': { // Non-text Content
    enableSemanticValidation: true,
    enablePatternValidation: true,
    enableContentQualityAnalysis: true,
    enableFrameworkOptimization: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-003': { // Info and Relationships
    enableSemanticValidation: true,
    enablePatternValidation: true,
    enableContentQualityAnalysis: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-025': { // Landmarks
    enableSemanticValidation: true,
    enablePatternValidation: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  
  // Medium Priority - Interactive Elements
  'WCAG-007': { // Focus Visible
    enablePatternValidation: true,
    enableFrameworkOptimization: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-014': { // Target Size
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'supplement',
  },
  
  // Framework-Specific Checks
  'WCAG-009': { // Name, Role, Value
    enableSemanticValidation: true,
    enablePatternValidation: true,
    enableFrameworkOptimization: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'enhance',
  },
  
  // Content Quality Checks
  'WCAG-031': { // Page Titled
    enableContentQualityAnalysis: true,
    enableCMSDetection: true,
    integrationStrategy: 'supplement',
  },
  
  // Default configuration for other checks
  'default': {
    enableSemanticValidation: false,
    enablePatternValidation: true,
    enableContentQualityAnalysis: false,
    enableFrameworkOptimization: false,
    enableComponentLibraryDetection: false,
    enableCMSDetection: false,
    integrationStrategy: 'supplement',
    enableCaching: true,
    maxExecutionTime: 5000,
    enableGracefulFallback: true,
    fallbackOnError: true,
  },
};

/**
 * Utility Integration Manager
 */
export class UtilityIntegrationManager {
  private static instance: UtilityIntegrationManager;
  
  // Utility instances
  private semanticValidator: AISemanticValidator;
  private patternLibrary: AccessibilityPatternLibrary;
  private contentAnalyzer: ContentQualityAnalyzer;
  private frameworkOptimizer: ModernFrameworkOptimizer;
  private componentDetector: ComponentLibraryDetector;
  private cmsDetector: HeadlessCMSDetector;
  
  // Cache for utility results
  private utilityCache = new Map<string, any>();
  
  private constructor() {
    this.semanticValidator = AISemanticValidator.getInstance();
    this.patternLibrary = AccessibilityPatternLibrary.getInstance();
    this.contentAnalyzer = ContentQualityAnalyzer.getInstance();
    this.frameworkOptimizer = ModernFrameworkOptimizer.getModernInstance();
    this.componentDetector = ComponentLibraryDetector.getInstance();
    this.cmsDetector = HeadlessCMSDetector.getInstance();
    
    logger.info('🔧 Utility Integration Manager initialized');
  }
  
  static getInstance(): UtilityIntegrationManager {
    if (!UtilityIntegrationManager.instance) {
      UtilityIntegrationManager.instance = new UtilityIntegrationManager();
    }
    return UtilityIntegrationManager.instance;
  }
  
  /**
   * Get utility configuration for a specific check
   */
  getUtilityConfig(ruleId: string): UtilityIntegrationConfig {
    return CHECK_UTILITY_PRIORITY[ruleId] || CHECK_UTILITY_PRIORITY['default'];
  }
  
  /**
   * Execute utility analysis for a check
   */
  async executeUtilityAnalysis(
    page: Page,
    ruleId: string,
    scanId: string,
    customConfig?: Partial<UtilityIntegrationConfig>
  ): Promise<UtilityAnalysisResult> {
    const startTime = Date.now();
    const config = { ...this.getUtilityConfig(ruleId), ...customConfig };
    const utilitiesUsed: string[] = [];
    const errors: string[] = [];
    const recommendations: string[] = [];
    
    logger.debug(`🔧 [${scanId}] Starting utility analysis for ${ruleId}`, { config });
    
    const result: UtilityAnalysisResult = {
      executionTime: 0,
      utilitiesUsed,
      errors,
      recommendations,
      utilityConfidence: 0,
      enhancedAccuracy: 0,
    };
    
    try {
      // Execute utilities based on configuration
      const analysisPromises: Promise<any>[] = [];
      
      if (config.enableSemanticValidation) {
        analysisPromises.push(
          this.executeWithFallback(
            () => this.semanticValidator.validateSemanticStructure(page),
            'semantic-validation',
            utilitiesUsed,
            errors
          )
        );
      }
      
      if (config.enablePatternValidation) {
        analysisPromises.push(
          this.executeWithFallback(
            () => this.patternLibrary.validateAccessibilityPatterns(page),
            'pattern-validation',
            utilitiesUsed,
            errors
          )
        );
      }
      
      if (config.enableContentQualityAnalysis) {
        analysisPromises.push(
          this.executeWithFallback(
            () => this.contentAnalyzer.analyzeContentQuality(page),
            'content-quality',
            utilitiesUsed,
            errors
          )
        );
      }
      
      if (config.enableFrameworkOptimization) {
        analysisPromises.push(
          this.executeWithFallback(
            () => this.frameworkOptimizer.analyzeModernFrameworks(page),
            'framework-optimization',
            utilitiesUsed,
            errors
          )
        );
      }
      
      if (config.enableComponentLibraryDetection) {
        analysisPromises.push(
          this.executeWithFallback(
            () => this.componentDetector.analyzeComponentLibraries(page),
            'component-library',
            utilitiesUsed,
            errors
          )
        );
      }
      
      if (config.enableCMSDetection) {
        analysisPromises.push(
          this.executeWithFallback(
            () => this.cmsDetector.analyzeHeadlessCMS(page),
            'cms-detection',
            utilitiesUsed,
            errors
          )
        );
      }
      
      // Execute all utilities in parallel with timeout
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Utility analysis timeout')), config.maxExecutionTime || 10000)
      );
      
      const analysisResults = await Promise.race([
        Promise.allSettled(analysisPromises),
        timeoutPromise
      ]) as PromiseSettledResult<any>[];
      
      // Process results
      let utilityIndex = 0;
      if (config.enableSemanticValidation && analysisResults[utilityIndex]) {
        const semanticResult = analysisResults[utilityIndex];
        if (semanticResult.status === 'fulfilled') {
          result.semanticAnalysis = semanticResult.value;
          recommendations.push(...(semanticResult.value?.recommendations || []));
        }
        utilityIndex++;
      }
      
      if (config.enablePatternValidation && analysisResults[utilityIndex]) {
        const patternResult = analysisResults[utilityIndex];
        if (patternResult.status === 'fulfilled') {
          result.patternAnalysis = patternResult.value;
          recommendations.push(...(patternResult.value?.recommendations || []));
        }
        utilityIndex++;
      }
      
      if (config.enableContentQualityAnalysis && analysisResults[utilityIndex]) {
        const contentResult = analysisResults[utilityIndex];
        if (contentResult.status === 'fulfilled') {
          result.contentQualityAnalysis = contentResult.value;
          recommendations.push(...(contentResult.value?.summary?.priorityRecommendations || []));
        }
        utilityIndex++;
      }
      
      if (config.enableFrameworkOptimization && analysisResults[utilityIndex]) {
        const frameworkResult = analysisResults[utilityIndex];
        if (frameworkResult.status === 'fulfilled') {
          result.frameworkAnalysis = frameworkResult.value;
          recommendations.push(...(frameworkResult.value?.recommendations || []));
        }
        utilityIndex++;
      }
      
      if (config.enableComponentLibraryDetection && analysisResults[utilityIndex]) {
        const componentResult = analysisResults[utilityIndex];
        if (componentResult.status === 'fulfilled') {
          result.componentLibraryAnalysis = componentResult.value;
          recommendations.push(...(componentResult.value?.recommendations || []));
        }
        utilityIndex++;
      }
      
      if (config.enableCMSDetection && analysisResults[utilityIndex]) {
        const cmsResult = analysisResults[utilityIndex];
        if (cmsResult.status === 'fulfilled') {
          result.cmsAnalysis = cmsResult.value;
          recommendations.push(...(cmsResult.value?.recommendations || []));
        }
        utilityIndex++;
      }
      
      // Calculate confidence and accuracy metrics
      result.utilityConfidence = this.calculateUtilityConfidence(result, utilitiesUsed);
      result.enhancedAccuracy = this.calculateEnhancedAccuracy(result, config);
      
      result.executionTime = Date.now() - startTime;
      
      logger.debug(`✅ [${scanId}] Utility analysis completed for ${ruleId}`, {
        utilitiesUsed: result.utilitiesUsed,
        confidence: result.utilityConfidence,
        accuracy: result.enhancedAccuracy,
        executionTime: result.executionTime,
      });
      
      return result;
      
    } catch (error) {
      logger.error(`❌ [${scanId}] Utility analysis failed for ${ruleId}:`, error);
      errors.push(`Utility analysis error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      result.executionTime = Date.now() - startTime;
      return result;
    }
  }
  
  /**
   * Enhance check results with utility analysis
   */
  enhanceCheckResult(
    originalResult: any,
    utilityAnalysis: UtilityAnalysisResult,
    ruleId: string
  ): CheckEnhancementResult {
    const enhancement: CheckEnhancementResult = {
      originalScore: originalResult.score,
      enhancedScore: originalResult.score,
      confidenceBoost: 0,
      additionalEvidence: [],
      utilityRecommendations: utilityAnalysis.recommendations,
      frameworkSpecificGuidance: [],
    };
    
    // Apply utility-based enhancements
    if (utilityAnalysis.semanticAnalysis) {
      enhancement.confidenceBoost += utilityAnalysis.semanticAnalysis.confidence * 0.2;
      enhancement.additionalEvidence.push({
        type: 'semantic-analysis',
        data: utilityAnalysis.semanticAnalysis,
      });
    }
    
    if (utilityAnalysis.patternAnalysis) {
      enhancement.confidenceBoost += utilityAnalysis.patternAnalysis.overallScore * 0.001;
      enhancement.additionalEvidence.push({
        type: 'pattern-analysis',
        data: utilityAnalysis.patternAnalysis,
      });
    }
    
    if (utilityAnalysis.frameworkAnalysis) {
      enhancement.frameworkSpecificGuidance.push(
        ...utilityAnalysis.frameworkAnalysis.recommendations
      );
      enhancement.additionalEvidence.push({
        type: 'framework-analysis',
        data: utilityAnalysis.frameworkAnalysis,
      });
    }
    
    // Calculate enhanced score (conservative approach)
    const confidenceMultiplier = Math.min(1.1, 1 + enhancement.confidenceBoost);
    enhancement.enhancedScore = Math.min(
      originalResult.maxScore,
      Math.round(originalResult.score * confidenceMultiplier)
    );
    
    return enhancement;
  }
  
  /**
   * Execute utility with fallback handling
   */
  private async executeWithFallback<T>(
    utilityFunction: () => Promise<T>,
    utilityName: string,
    utilitiesUsed: string[],
    errors: string[]
  ): Promise<T | null> {
    try {
      const result = await utilityFunction();
      utilitiesUsed.push(utilityName);
      return result;
    } catch (error) {
      logger.warn(`⚠️ Utility ${utilityName} failed, continuing with fallback`, error);
      errors.push(`${utilityName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }
  
  /**
   * Calculate utility confidence score
   */
  private calculateUtilityConfidence(result: UtilityAnalysisResult, utilitiesUsed: string[]): number {
    let confidence = 0.5; // Base confidence
    
    // Boost confidence based on successful utility executions
    confidence += utilitiesUsed.length * 0.1;
    
    // Factor in specific utility confidence scores
    if (result.semanticAnalysis?.confidence) {
      confidence += result.semanticAnalysis.confidence * 0.2;
    }
    
    if (result.patternAnalysis?.overallScore) {
      confidence += (result.patternAnalysis.overallScore / 100) * 0.15;
    }
    
    return Math.min(1.0, confidence);
  }
  
  /**
   * Calculate enhanced accuracy improvement
   */
  private calculateEnhancedAccuracy(result: UtilityAnalysisResult, config: UtilityIntegrationConfig): number {
    let accuracy = 0;
    
    // Base accuracy improvement from utility usage
    if (config.enableSemanticValidation && result.semanticAnalysis) accuracy += 0.15;
    if (config.enablePatternValidation && result.patternAnalysis) accuracy += 0.20;
    if (config.enableContentQualityAnalysis && result.contentQualityAnalysis) accuracy += 0.10;
    if (config.enableFrameworkOptimization && result.frameworkAnalysis) accuracy += 0.25;
    if (config.enableComponentLibraryDetection && result.componentLibraryAnalysis) accuracy += 0.20;
    if (config.enableCMSDetection && result.cmsAnalysis) accuracy += 0.10;
    
    return Math.min(1.0, accuracy);
  }
  
  /**
   * Clear utility cache
   */
  clearCache(): void {
    this.utilityCache.clear();
    logger.debug('🧹 Utility cache cleared');
  }
}

export default UtilityIntegrationManager;
