/**
 * WCAG-050: Audio Control Check
 * Success Criterion: 1.4.2 Audio Control (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig } from '../utils/check-template';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export class AudioControlCheck {
  private checkTemplate = new CheckTemplate();
  private smartCache = SmartCache.getInstance();

  async performCheck(config: CheckConfig): Promise<WcagCheckResultEnhanced> {
    const result = await this.checkTemplate.executeCheck(
      'WCAG-050',
      'Audio Control',
      'perceivable',
      0.0458,
      'A',
      config,
      this.executeAudioControlCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with audio control analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-027',
        ruleName: 'Audio Control',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'audio-control-analysis',
          autoplayDetection: true,
          audioControlValidation: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85,
        maxEvidenceItems: 20,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeAudioControlCheck(
    page: Page,
    _config: CheckConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze audio elements and autoplay behavior
    const audioAnalysis = await page.evaluate(() => {
      const problematicAudio: Array<{
        selector: string;
        tagName: string;
        src: string;
        hasAutoplay: boolean;
        hasControls: boolean;
        duration: number;
        hasLoop: boolean;
        volume: number;
        muted: boolean;
        issues: string[];
        severity: 'error' | 'warning' | 'info';
      }> = [];

      // Check audio elements
      const audioElements = document.querySelectorAll('audio, video');
      audioElements.forEach((element, index) => {
        const media = element as HTMLMediaElement;
        const issues: string[] = [];
        let severity: 'error' | 'warning' | 'info' = 'info';
        
        const hasAutoplay = media.hasAttribute('autoplay');
        const hasControls = media.hasAttribute('controls');
        const hasLoop = media.hasAttribute('loop');
        const muted = media.muted;
        const volume = media.volume;
        const duration = media.duration || 0;
        
        // Check for autoplay without controls
        if (hasAutoplay && !muted) {
          if (!hasControls) {
            issues.push('Autoplay audio without user controls');
            severity = 'error';
          }
          
          // Check duration (3+ seconds requires controls)
          if (duration > 3 || duration === 0) { // 0 means unknown/infinite
            issues.push('Autoplay audio longer than 3 seconds');
            severity = 'error';
          }
          
          if (hasLoop) {
            issues.push('Autoplay audio with loop (infinite duration)');
            severity = 'error';
          }
        }
        
        // Check for high volume autoplay
        if (hasAutoplay && volume > 0.5 && !muted) {
          issues.push('Autoplay audio with high volume');
          severity = 'warning';
        }
        
        if (issues.length > 0) {
          problematicAudio.push({
            selector: `${media.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
            tagName: media.tagName.toLowerCase(),
            src: media.src || media.currentSrc || 'unknown',
            hasAutoplay,
            hasControls,
            duration,
            hasLoop,
            volume,
            muted,
            issues,
            severity,
          });
        }
      });

      // Check for background audio (Web Audio API, embedded players)
      const embeddedPlayers = document.querySelectorAll(`
        iframe[src*="youtube"], iframe[src*="vimeo"], iframe[src*="soundcloud"],
        embed[src*="audio"], object[data*="audio"], object[data*="sound"],
        .audio-player, .music-player, [data-autoplay], [data-audio]
      `);
      
      embeddedPlayers.forEach((element, index) => {
        const issues: string[] = [];
        let severity: 'error' | 'warning' | 'info' = 'warning';
        
        // Check for autoplay parameters in iframe src
        const src = element.getAttribute('src') || element.getAttribute('data') || '';
        if (src.includes('autoplay=1') || src.includes('auto_play=true')) {
          issues.push('Embedded player with autoplay enabled');
          severity = 'error';
        }
        
        // Check for data attributes suggesting autoplay
        if (element.hasAttribute('data-autoplay') || 
            element.hasAttribute('data-auto-start')) {
          issues.push('Element configured for automatic audio playback');
          severity = 'warning';
        }
        
        if (issues.length > 0) {
          problematicAudio.push({
            selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
            tagName: element.tagName.toLowerCase(),
            src: src.substring(0, 100),
            hasAutoplay: true,
            hasControls: false, // Assume no controls for embedded
            duration: 0, // Unknown
            hasLoop: false,
            volume: 1,
            muted: false,
            issues,
            severity,
          });
        }
      });

      // Check for JavaScript audio initialization
      const scripts = document.querySelectorAll('script');
      let hasJSAudio = false;
      const jsAudioPatterns = [
        /new Audio\(/,
        /\.play\(\)/,
        /audioContext/i,
        /createOscillator/,
        /autoplay.*true/i,
      ];
      
      scripts.forEach((script) => {
        const content = script.textContent || '';
        if (jsAudioPatterns.some(pattern => pattern.test(content))) {
          hasJSAudio = true;
        }
      });

      if (hasJSAudio) {
        problematicAudio.push({
          selector: 'script',
          tagName: 'script',
          src: 'JavaScript audio detected',
          hasAutoplay: true,
          hasControls: false,
          duration: 0,
          hasLoop: false,
          volume: 1,
          muted: false,
          issues: ['JavaScript audio may autoplay without user controls'],
          severity: 'warning',
        });
      }

      return {
        problematicAudio,
        totalAudioElements: audioElements.length,
        problematicCount: problematicAudio.length,
        autoplayCount: problematicAudio.filter(audio => audio.hasAutoplay).length,
        noControlsCount: problematicAudio.filter(audio => !audio.hasControls).length,
        longDurationCount: problematicAudio.filter(audio => audio.duration > 3).length,
        hasJSAudio,
      };
    });

    let score = 100;
    const elementCount = audioAnalysis.problematicCount;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      // Calculate score based on severity
      audioAnalysis.problematicAudio.forEach((audio) => {
        const deduction = audio.severity === 'error' ? 20 : 
                         audio.severity === 'warning' ? 10 : 5;
        score = Math.max(0, score - deduction);
      });

      issues.push(`${elementCount} audio elements with control issues found`);
      if (audioAnalysis.autoplayCount > 0) {
        issues.push(`${audioAnalysis.autoplayCount} elements have autoplay enabled`);
      }
      if (audioAnalysis.noControlsCount > 0) {
        issues.push(`${audioAnalysis.noControlsCount} audio elements lack user controls`);
      }

      audioAnalysis.problematicAudio.forEach((audio) => {
        evidence.push({
          type: 'code',
          description: `Audio control issue: ${audio.issues.join(', ')}`,
          value: `${audio.tagName}: ${audio.src} | Duration: ${audio.duration}s | Controls: ${audio.hasControls}`,
          selector: audio.selector,
          elementCount: 1,
          affectedSelectors: [audio.selector],
          severity: audio.severity,
          fixExample: {
            before: this.getBeforeExample(audio),
            after: this.getAfterExample(audio),
            description: this.getFixDescription(audio.issues),
            codeExample: this.getCodeExample(audio.tagName),
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/audio-control.html',
              'https://www.w3.org/WAI/WCAG21/Techniques/G60',
              'https://www.w3.org/WAI/WCAG21/Techniques/G170'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              hasAutoplay: audio.hasAutoplay,
              hasControls: audio.hasControls,
              duration: audio.duration,
              hasLoop: audio.hasLoop,
              volume: audio.volume,
              muted: audio.muted,
              issues: audio.issues,
            },
          },
        });
      });
    }

    // Add recommendations
    recommendations.push('Provide user controls for all audio that plays automatically');
    recommendations.push('Avoid autoplay for audio longer than 3 seconds');
    recommendations.push('Include pause, stop, and volume controls for background audio');
    recommendations.push('Allow users to turn off or control background sounds');
    recommendations.push('Ensure audio controls are keyboard accessible');

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(audio: any): string {
    if (audio.tagName === 'audio') {
      return `<audio src="${audio.src}" autoplay${audio.hasLoop ? ' loop' : ''}>`;
    }
    if (audio.tagName === 'video') {
      return `<video src="${audio.src}" autoplay${audio.hasLoop ? ' loop' : ''}>`;
    }
    if (audio.tagName === 'iframe') {
      return `<iframe src="${audio.src}"></iframe>`;
    }
    return `<${audio.tagName} autoplay>Audio content</${audio.tagName}>`;
  }

  private getAfterExample(audio: any): string {
    if (audio.tagName === 'audio') {
      return `<audio src="${audio.src}" controls>\n  <p>Your browser doesn't support audio.</p>\n</audio>`;
    }
    if (audio.tagName === 'video') {
      return `<video src="${audio.src}" controls>\n  <p>Your browser doesn't support video.</p>\n</video>`;
    }
    if (audio.tagName === 'iframe') {
      return `<iframe src="${audio.src.replace('autoplay=1', 'autoplay=0')}"></iframe>`;
    }
    return `<${audio.tagName} controls>Audio content with controls</${audio.tagName}>`;
  }

  private getFixDescription(issues: string[]): string {
    if (issues.includes('without user controls')) {
      return 'Add controls attribute to provide user audio controls';
    }
    if (issues.includes('longer than 3 seconds')) {
      return 'Remove autoplay or ensure audio is shorter than 3 seconds';
    }
    if (issues.includes('with loop')) {
      return 'Remove autoplay from looping audio or provide stop controls';
    }
    if (issues.includes('high volume')) {
      return 'Reduce default volume or provide volume controls';
    }
    return 'Provide user controls for audio playback';
  }

  private getCodeExample(tagName: string): string {
    switch (tagName) {
      case 'audio':
        return `
<!-- Before: Autoplay audio without controls -->
<audio src="background-music.mp3" autoplay loop>
  Your browser doesn't support audio.
</audio>

<!-- After: User-controlled audio -->
<audio src="background-music.mp3" controls>
  Your browser doesn't support audio.
  <p><a href="background-music.mp3">Download audio file</a></p>
</audio>

<!-- Alternative: Short notification sounds (under 3 seconds) -->
<audio src="notification.mp3" autoplay>
  <p>New message received</p>
</audio>
        `;
      case 'video':
        return `
<!-- Before: Autoplay video with audio -->
<video src="intro-video.mp4" autoplay>
  Your browser doesn't support video.
</video>

<!-- After: User-controlled video -->
<video src="intro-video.mp4" controls poster="video-thumbnail.jpg">
  Your browser doesn't support video.
  <p><a href="intro-video.mp4">Download video file</a></p>
</video>

<!-- Alternative: Muted autoplay (no audio) -->
<video src="background-video.mp4" autoplay muted loop>
  <button onclick="toggleAudio()">Enable Audio</button>
</video>
        `;
      case 'iframe':
        return `
<!-- Before: Embedded player with autoplay -->
<iframe src="https://www.youtube.com/embed/VIDEO_ID?autoplay=1"
        width="560" height="315">
</iframe>

<!-- After: Embedded player without autoplay -->
<iframe src="https://www.youtube.com/embed/VIDEO_ID?autoplay=0"
        width="560" height="315"
        title="Video Title">
</iframe>

<!-- Alternative: User-initiated playback -->
<div class="video-container">
  <img src="video-thumbnail.jpg" alt="Video thumbnail">
  <button onclick="loadVideo()">Play Video</button>
</div>
        `;
      default:
        return `
<!-- General audio control principles -->

<!-- 1. Always provide controls for audio longer than 3 seconds -->
<audio src="long-audio.mp3" controls>
  <p>Audio description of the content</p>
</audio>

<!-- 2. Short sounds (under 3 seconds) can autoplay -->
<audio src="click-sound.mp3" autoplay>
  <!-- No controls needed for very short sounds -->
</audio>

<!-- 3. Background audio should be user-controlled -->
<div class="audio-controls">
  <audio id="background-audio" src="ambient.mp3" loop>
  </audio>
  <button onclick="toggleBackgroundAudio()">Toggle Background Audio</button>
  <input type="range" min="0" max="1" step="0.1" onchange="setVolume(this.value)">
</div>

<script>
function toggleBackgroundAudio() {
  const audio = document.getElementById('background-audio');
  if (audio.paused) {
    audio.play();
  } else {
    audio.pause();
  }
}

function setVolume(value) {
  document.getElementById('background-audio').volume = value;
}
</script>
        `;
    }
  }
}
