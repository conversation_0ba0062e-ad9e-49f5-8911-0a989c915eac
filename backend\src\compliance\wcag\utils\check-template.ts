/**
 * WCAG Check Template System
 * Provides consistent structure for all WCAG checks
 */

import { Page } from 'puppeteer';
import {
  WcagCheckResult,
  WcagEvidence,
  WcagCategory,
  WcagVersion,
  WcagLevel,
  // AutomatedCheckStatus,
  // WcagManualReviewItem,
} from '../types';
import logger from '../../../utils/logger';

export interface CheckConfig {
  targetUrl: string;
  timeout: number;
  scanId: string;
  page?: Page;
}

export interface EnhancedCheckConfig extends CheckConfig {
  retryAttempts: number;
  enableJavaScript: boolean;
  enableImages: boolean;
  followRedirects: boolean;
}

export type CheckFunction<T extends CheckConfig> = (
  page: Page,
  config: T,
) => Promise<{
  score: number;
  maxScore: number;
  evidence: WcagEvidence[];
  issues: string[];
  recommendations: string[];
}>;

export class CheckTemplate {
  /**
   * Execute a WCAG check with consistent error handling and logging
   */
  async executeCheck<T extends CheckConfig>(
    ruleId: string,
    ruleName: string,
    category: string,
    weight: number,
    level: string,
    config: T,
    checkFunction: CheckFunction<T>,
    requiresBrowser: boolean = true,
    requiresManualReview: boolean = false,
  ): Promise<WcagCheckResult> {
    const startTime = Date.now();

    try {
      logger.info(`🔍 [${config.scanId}] Starting ${ruleId}: ${ruleName}`);

      if (requiresManualReview) {
        throw new Error('Manual review checks should not use fully automated template');
      }

      try {
        if (requiresBrowser && !config.page) {
          throw new Error('Browser instance required - page not provided in config');
        }

        // Execute the specific check function
        const result = await checkFunction(config.page!, config);

        const executionTime = Date.now() - startTime;

        // WCAG Compliance Fix: Enforce binary scoring (100% pass or 0% fail)
        // For WCAG compliance, partial compliance is still non-compliance
        const isPassed = result.score === result.maxScore;
        const binaryScore = isPassed ? result.maxScore : 0;
        const status = isPassed ? 'passed' : 'failed';

        // Log the scoring decision
        if (!isPassed) {
          logger.warn(
            `⚠️ [${config.scanId}] ${ruleId} failed: ${result.score}/${result.maxScore} (${((result.score / result.maxScore) * 100).toFixed(1)}%) - converted to 0% for WCAG compliance`,
          );
        }

        logger.info(
          `✅ [${config.scanId}] Completed ${ruleId} in ${executionTime}ms - Status: ${status} (${binaryScore}/${result.maxScore})`,
        );

        return {
          ruleId,
          ruleName,
          category: category as WcagCategory,
          wcagVersion: this.getVersionFromRuleId(ruleId),
          successCriterion: this.getSuccessCriterionFromRuleId(ruleId),
          level: level as WcagLevel,
          status,
          score: binaryScore, // Binary score: 100% or 0%
          maxScore: result.maxScore,
          weight,
          automated: true,
          evidence: result.evidence,
          recommendations: result.recommendations,
          executionTime,
          // Store original partial score in evidence for debugging
          originalScore: result.score, // Keep track of original score for analysis
        };
      } finally {
        // Browser cleanup will be handled by orchestrator
      }
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      logger.error(`❌ [${config.scanId}] Error in ${ruleId}`, {
        error: {
          message: errorMessage,
          stack: errorStack,
          name: error instanceof Error ? error.name : 'UnknownError',
        },
        ruleId,
        ruleName,
        executionTime,
      });

      return {
        ruleId,
        ruleName,
        category: category as WcagCategory,
        wcagVersion: this.getVersionFromRuleId(ruleId),
        successCriterion: this.getSuccessCriterionFromRuleId(ruleId),
        level: level as WcagLevel,
        status: 'failed',
        score: 0,
        maxScore: 100,
        weight,
        automated: true,
        evidence: [
          {
            type: 'text',
            description: 'Technical error during check execution',
            value: errorMessage,
            severity: 'error',
          },
        ],
        recommendations: [
          'Check failed due to technical error - manual review recommended',
          `Error details: ${errorMessage}`,
          'Check browser console and server logs for more information',
        ],
        executionTime,
        errorMessage,
      };
    }
  }

  /**
   * Get WCAG version from rule ID
   */
  protected getVersionFromRuleId(ruleId: string): WcagVersion {
    const ruleNumber = parseInt(ruleId.split('-')[1]);

    if (ruleNumber <= 9) return '2.1';
    if (ruleNumber <= 16) return '2.2';
    return '3.0';
  }

  /**
   * Get success criterion from rule ID
   */
  protected getSuccessCriterionFromRuleId(ruleId: string): string {
    const criterionMap: Record<string, string> = {
      'WCAG-001': '1.1.1',
      'WCAG-002': '1.2.2',
      'WCAG-003': '1.3.1',
      'WCAG-004': '1.4.3',
      'WCAG-005': '2.1.1',
      'WCAG-006': '2.4.3',
      'WCAG-007': '2.4.7',
      'WCAG-008': '3.3.1',
      'WCAG-009': '4.1.2',
      'WCAG-010': '2.4.11',
      'WCAG-011': '2.4.12',
      'WCAG-012': '2.4.13',
      'WCAG-013': '2.5.7',
      'WCAG-014': '2.5.8',
      'WCAG-015': '3.2.6',
      'WCAG-016': '3.3.7',
      'WCAG-017': '2.1',
      'WCAG-018': '2.2',
      'WCAG-019': '2.4',
      'WCAG-020': '2.5',
      'WCAG-021': '3.1',
    };

    return criterionMap[ruleId] || 'Unknown';
  }
}
