/**
 * WCAG Scan Form Component
 * Form for initiating new WCAG compliance scans
 */

'use client';

import React, { useState, useCallback } from 'react';
import { Play, Settings, RotateCcw } from 'lucide-react';
import { WcagScanFormData, WcagVersion, WcagLevel } from '../../types/wcag';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Checkbox } from '../ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Alert } from '../ui/alert';

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '../ui/collapsible';

interface WcagScanFormProps {
  onSubmit: (formData: WcagScanFormData) => Promise<void>;
  isLoading?: boolean;
  error?: string;
  disabled?: boolean;
}

const WcagScanForm: React.FC<WcagScanFormProps> = ({
  onSubmit,
  isLoading = false,
  error,
  disabled = false,
}) => {
  const [formData, setFormData] = useState<WcagScanFormData>({
    targetUrl: '',
    enableContrastAnalysis: true,
    enableKeyboardTesting: true,
    enableFocusAnalysis: true,
    enableSemanticValidation: true,
    enableManualReview: false,
    wcagVersion: 'all',
    level: 'AA',
    maxPages: 5,
    // Enhanced Phase 1-3 Options (enabled by default)
    enableBrowserPool: true,
    enableSmartCache: true,
    enablePerformanceMonitoring: true,
    enableCMSDetection: true,
    enableEcommerceAnalysis: true,
    enableFrameworkDetection: true,
    enableMediaAnalysis: true,
    enableVPSOptimization: true,
    timeout: 60000,
    // Advanced Options (from orphaned component)
    includeHiddenElements: false,
    enableScreenshots: false,
    customRules: [],
    excludeRules: [],
    // Authentication Options (from orphaned component)
    requiresAuth: false,
    authType: 'none',
    authCredentials: undefined,
    customHeaders: {},
  });

  const [showAdvanced, setShowAdvanced] = useState(false);
  const [urlError, setUrlError] = useState<string>('');

  /**
   * Validate URL format
   */
  const validateUrl = useCallback((url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }, []);

  /**
   * Handle form field changes
   */
  const handleChange = useCallback(
    (
      field: keyof WcagScanFormData,
      value:
        | string
        | boolean
        | number
        | WcagVersion
        | WcagLevel
        | 'all'
        | { username: string; password: string }
        | string[],
    ) => {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));

      // Validate URL on change
      if (field === 'targetUrl') {
        if (value && typeof value === 'string' && !validateUrl(value)) {
          setUrlError('Please enter a valid URL (including http:// or https://)');
        } else {
          setUrlError('');
        }
      }
    },
    [validateUrl],
  );

  /**
   * Handle form submission
   */
  const handleSubmit = useCallback(
    async (event: React.FormEvent) => {
      event.preventDefault();

      // Final validation
      if (!formData.targetUrl) {
        setUrlError('URL is required');
        return;
      }

      if (!validateUrl(formData.targetUrl)) {
        setUrlError('Please enter a valid URL');
        return;
      }

      try {
        await onSubmit(formData);
      } catch (error) {
        // eslint-disable-next-line no-console
        // eslint-disable-next-line no-console
        console.error('Form submission failed:', error);
      }
    },
    [formData, onSubmit, validateUrl],
  );

  /**
   * Reset form to defaults
   */
  const handleReset = useCallback(() => {
    setFormData({
      targetUrl: '',
      enableContrastAnalysis: true,
      enableKeyboardTesting: true,
      enableFocusAnalysis: true,
      enableSemanticValidation: true,
      enableManualReview: false,
      wcagVersion: 'all',
      level: 'AA',
      maxPages: 5,
      // Enhanced Phase 1-3 Options (enabled by default)
      enableBrowserPool: true,
      enableSmartCache: true,
      enablePerformanceMonitoring: true,
      enableCMSDetection: true,
      enableEcommerceAnalysis: true,
      enableFrameworkDetection: true,
      enableMediaAnalysis: true,
      enableVPSOptimization: true,
      timeout: 60000,
      // Advanced Options (from orphaned component)
      includeHiddenElements: false,
      enableScreenshots: false,
      customRules: [],
      excludeRules: [],
      // Authentication Options (from orphaned component)
      requiresAuth: false,
      authType: 'none',
      authCredentials: undefined,
      customHeaders: {},
    });
    setUrlError('');
  }, []);

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Play className="h-5 w-5" />
          Start WCAG Compliance Scan
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Analyze your website for WCAG 2.1, 2.2, and 3.0 compliance
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <p>{error}</p>
            </Alert>
          )}

          {/* Target URL */}
          <div className="space-y-2">
            <Label htmlFor="targetUrl">Website URL *</Label>
            <Input
              id="targetUrl"
              type="url"
              placeholder="https://example.com"
              value={formData.targetUrl}
              onChange={(e) => handleChange('targetUrl', e.target.value)}
              disabled={disabled || isLoading}
              className={urlError ? 'border-destructive' : ''}
            />
            {urlError && <p className="text-sm text-destructive">{urlError}</p>}
            {!urlError && (
              <p className="text-sm text-muted-foreground">Enter the URL of the website to scan</p>
            )}
          </div>

          {/* WCAG Version and Level Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="wcagVersion">WCAG Version</Label>
              <Select
                value={formData.wcagVersion}
                onValueChange={(value) => handleChange('wcagVersion', value as WcagVersion | 'all')}
                disabled={disabled || isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select WCAG version" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Versions (2.1, 2.2, 3.0)</SelectItem>
                  <SelectItem value="2.1">WCAG 2.1</SelectItem>
                  <SelectItem value="2.2">WCAG 2.2</SelectItem>
                  <SelectItem value="3.0">WCAG 3.0 (Draft)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="level">Compliance Level</Label>
              <Select
                value={formData.level}
                onValueChange={(value) => handleChange('level', value as WcagLevel)}
                disabled={disabled || isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select compliance level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A">Level A (Minimum)</SelectItem>
                  <SelectItem value="AA">Level AA (Standard)</SelectItem>
                  <SelectItem value="AAA">Level AAA (Enhanced)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Scan Options */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Scan Options</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableContrastAnalysis"
                  checked={formData.enableContrastAnalysis}
                  onCheckedChange={(checked) => handleChange('enableContrastAnalysis', checked)}
                  disabled={disabled || isLoading}
                />
                <Label htmlFor="enableContrastAnalysis" className="text-sm font-normal">
                  Color Contrast Analysis
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableKeyboardTesting"
                  checked={formData.enableKeyboardTesting}
                  onCheckedChange={(checked) => handleChange('enableKeyboardTesting', checked)}
                  disabled={disabled || isLoading}
                />
                <Label htmlFor="enableKeyboardTesting" className="text-sm font-normal">
                  Keyboard Accessibility Testing
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableFocusAnalysis"
                  checked={formData.enableFocusAnalysis}
                  onCheckedChange={(checked) => handleChange('enableFocusAnalysis', checked)}
                  disabled={disabled || isLoading}
                />
                <Label htmlFor="enableFocusAnalysis" className="text-sm font-normal">
                  Focus Management Analysis
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableSemanticValidation"
                  checked={formData.enableSemanticValidation}
                  onCheckedChange={(checked) => handleChange('enableSemanticValidation', checked)}
                  disabled={disabled || isLoading}
                />
                <Label htmlFor="enableSemanticValidation" className="text-sm font-normal">
                  Semantic Structure Validation
                </Label>
              </div>
            </div>

            {/* Manual Review Option */}
            <div className="mt-4 p-4 border rounded-lg bg-blue-50 dark:bg-blue-900/20">
              <div className="flex items-center space-x-2 mb-2">
                <Checkbox
                  id="enableManualReview"
                  checked={formData.enableManualReview}
                  onCheckedChange={(checked) => handleChange('enableManualReview', checked)}
                  disabled={disabled || isLoading}
                />
                <Label htmlFor="enableManualReview" className="text-sm font-medium">
                  Enable Manual Review Items
                </Label>
              </div>
              <p className="text-xs text-muted-foreground ml-6">
                Generate manual review items for accessibility checks that require human evaluation.
                These can be reviewed later in the Manual Review section.
              </p>
            </div>
          </div>

          {/* Advanced Options */}
          <Collapsible open={showAdvanced} onOpenChange={setShowAdvanced}>
            <CollapsibleTrigger asChild>
              <Button
                type="button"
                variant="outline"
                className="w-full justify-between"
                disabled={disabled || isLoading}
              >
                <span className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  {showAdvanced ? 'Hide' : 'Show'} Advanced Options
                </span>
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="maxPages">Maximum Pages to Scan: {formData.maxPages}</Label>
                <div className="px-3">
                  <input
                    type="range"
                    id="maxPages"
                    min="1"
                    max="10"
                    value={formData.maxPages}
                    onChange={(e) => handleChange('maxPages', parseInt(e.target.value))}
                    disabled={disabled || isLoading}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>1</span>
                    <span>5</span>
                    <span>10</span>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  Scanning more pages provides comprehensive results but takes longer
                </p>
              </div>

              {/* Enhanced Performance Options */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-muted-foreground">
                  Performance Optimization
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="browser-pool"
                      checked={formData.enableBrowserPool}
                      onCheckedChange={(checked) =>
                        handleChange('enableBrowserPool', checked as boolean)
                      }
                      disabled={disabled || isLoading}
                    />
                    <Label htmlFor="browser-pool" className="text-sm font-normal">
                      Browser Pool (50% faster)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="smart-cache"
                      checked={formData.enableSmartCache}
                      onCheckedChange={(checked) =>
                        handleChange('enableSmartCache', checked as boolean)
                      }
                      disabled={disabled || isLoading}
                    />
                    <Label htmlFor="smart-cache" className="text-sm font-normal">
                      Smart Caching (40% faster)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="performance-monitoring"
                      checked={formData.enablePerformanceMonitoring}
                      onCheckedChange={(checked) =>
                        handleChange('enablePerformanceMonitoring', checked as boolean)
                      }
                      disabled={disabled || isLoading}
                    />
                    <Label htmlFor="performance-monitoring" className="text-sm font-normal">
                      Performance Monitoring
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="vps-optimization"
                      checked={formData.enableVPSOptimization}
                      onCheckedChange={(checked) =>
                        handleChange('enableVPSOptimization', checked as boolean)
                      }
                      disabled={disabled || isLoading}
                    />
                    <Label htmlFor="vps-optimization" className="text-sm font-normal">
                      VPS Optimization
                    </Label>
                  </div>
                </div>
              </div>

              {/* Enhanced Website Analysis */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-muted-foreground">Website Analysis</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="cms-detection"
                      checked={formData.enableCMSDetection}
                      onCheckedChange={(checked) =>
                        handleChange('enableCMSDetection', checked as boolean)
                      }
                      disabled={disabled || isLoading}
                    />
                    <Label htmlFor="cms-detection" className="text-sm font-normal">
                      CMS Detection
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="ecommerce-analysis"
                      checked={formData.enableEcommerceAnalysis}
                      onCheckedChange={(checked) =>
                        handleChange('enableEcommerceAnalysis', checked as boolean)
                      }
                      disabled={disabled || isLoading}
                    />
                    <Label htmlFor="ecommerce-analysis" className="text-sm font-normal">
                      E-commerce Analysis
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="framework-detection"
                      checked={formData.enableFrameworkDetection}
                      onCheckedChange={(checked) =>
                        handleChange('enableFrameworkDetection', checked as boolean)
                      }
                      disabled={disabled || isLoading}
                    />
                    <Label htmlFor="framework-detection" className="text-sm font-normal">
                      Framework Detection
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="media-analysis"
                      checked={formData.enableMediaAnalysis}
                      onCheckedChange={(checked) =>
                        handleChange('enableMediaAnalysis', checked as boolean)
                      }
                      disabled={disabled || isLoading}
                    />
                    <Label htmlFor="media-analysis" className="text-sm font-normal">
                      Media Analysis
                    </Label>
                  </div>
                </div>
              </div>

              {/* Advanced Scan Options */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-muted-foreground">Advanced Scan Options</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="hidden-elements"
                      checked={formData.includeHiddenElements}
                      onCheckedChange={(checked) =>
                        handleChange('includeHiddenElements', checked as boolean)
                      }
                      disabled={disabled || isLoading}
                    />
                    <Label htmlFor="hidden-elements" className="text-sm font-normal">
                      Include Hidden Elements
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="screenshots"
                      checked={formData.enableScreenshots}
                      onCheckedChange={(checked) =>
                        handleChange('enableScreenshots', checked as boolean)
                      }
                      disabled={disabled || isLoading}
                    />
                    <Label htmlFor="screenshots" className="text-sm font-normal">
                      Capture Screenshots
                    </Label>
                  </div>
                </div>
              </div>

              {/* Authentication Options */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-muted-foreground">Authentication</h4>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="requires-auth"
                    checked={formData.requiresAuth}
                    onCheckedChange={(checked) => handleChange('requiresAuth', checked as boolean)}
                    disabled={disabled || isLoading}
                  />
                  <Label htmlFor="requires-auth" className="text-sm font-normal">
                    Website requires authentication
                  </Label>
                </div>

                {formData.requiresAuth && (
                  <div className="ml-6 space-y-3 p-3 border rounded-md bg-muted/50">
                    <div className="space-y-2">
                      <Label htmlFor="auth-type">Authentication Type</Label>
                      <Select
                        value={formData.authType || 'basic'}
                        onValueChange={(value) =>
                          handleChange('authType', value as 'none' | 'basic' | 'form' | 'oauth')
                        }
                        disabled={disabled || isLoading}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="basic">Basic Authentication</SelectItem>
                          <SelectItem value="form">Form-based Login</SelectItem>
                          <SelectItem value="oauth">OAuth</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {(formData.authType === 'basic' || formData.authType === 'form') && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div className="space-y-2">
                          <Label htmlFor="auth-username">Username</Label>
                          <Input
                            id="auth-username"
                            type="text"
                            value={formData.authCredentials?.username || ''}
                            onChange={(e) =>
                              handleChange('authCredentials', {
                                ...formData.authCredentials,
                                username: e.target.value,
                                password: formData.authCredentials?.password || '',
                              })
                            }
                            disabled={disabled || isLoading}
                            placeholder="Enter username"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="auth-password">Password</Label>
                          <Input
                            id="auth-password"
                            type="password"
                            value={formData.authCredentials?.password || ''}
                            onChange={(e) =>
                              handleChange('authCredentials', {
                                ...formData.authCredentials,
                                username: formData.authCredentials?.username || '',
                                password: e.target.value,
                              })
                            }
                            disabled={disabled || isLoading}
                            placeholder="Enter password"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleReset}
              disabled={disabled || isLoading}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Reset
            </Button>

            <Button
              type="submit"
              disabled={disabled || isLoading || !!urlError || !formData.targetUrl}
              className="flex items-center gap-2 flex-1"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Starting Scan...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  Start Scan
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default WcagScanForm;
