/**
 * CMS Platform Detector and Optimizer for WCAG Scanning
 * Specialized detection patterns and accessibility optimizations for popular CMS platforms
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

export interface CMSDetectionResult {
  platform: string;
  version?: string;
  confidence: number; // 0-1
  theme?: string;
  plugins: string[];
  accessibilityFeatures: string[];
  commonIssues: string[];
  optimizations: string[];
}

export interface CMSAccessibilityPattern {
  platform: string;
  selectors: {
    navigation: string[];
    content: string[];
    sidebar: string[];
    footer: string[];
    forms: string[];
    media: string[];
  };
  commonIssues: {
    pattern: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
    recommendation: string;
  }[];
  accessibilityPlugins: string[];
  themePatterns: string[];
}

export interface CMSOptimizationConfig {
  enableWordPressOptimization: boolean;
  enableDrupalOptimization: boolean;
  enableJoomlaOptimization: boolean;
  enableShopifyOptimization: boolean;
  enableWixOptimization: boolean;
  enableSquarespaceOptimization: boolean;
  enableCustomCMSDetection: boolean;
  deepAnalysis: boolean;
}

/**
 * Advanced CMS platform detector with accessibility-focused optimizations
 */
export class CMSDetector {
  private static instance: CMSDetector;
  private cmsPatterns: Map<string, CMSAccessibilityPattern> = new Map();

  private constructor() {
    this.initializeCMSPatterns();
  }

  static getInstance(): CMSDetector {
    if (!CMSDetector.instance) {
      CMSDetector.instance = new CMSDetector();
    }
    return CMSDetector.instance;
  }

  /**
   * Detect CMS platform and return optimization recommendations
   */
  async detectCMS(
    page: Page,
    config: Partial<CMSOptimizationConfig> = {},
  ): Promise<CMSDetectionResult> {
    const fullConfig: CMSOptimizationConfig = {
      enableWordPressOptimization: config.enableWordPressOptimization ?? true,
      enableDrupalOptimization: config.enableDrupalOptimization ?? true,
      enableJoomlaOptimization: config.enableJoomlaOptimization ?? true,
      enableShopifyOptimization: config.enableShopifyOptimization ?? true,
      enableWixOptimization: config.enableWixOptimization ?? true,
      enableSquarespaceOptimization: config.enableSquarespaceOptimization ?? true,
      enableCustomCMSDetection: config.enableCustomCMSDetection ?? true,
      deepAnalysis: config.deepAnalysis ?? true,
    };

    logger.debug('🔍 Starting CMS platform detection');

    // Inject CMS detection functions
    await this.injectCMSDetectionFunctions(page);

    // Perform detection
    const detectionResults = await page.evaluate(() => {
      return (window as any).wcagCMSDetection.detectAllPlatforms();
    });

    // Process and enhance results
    const result = await this.processDetectionResults(page, detectionResults, fullConfig);

    logger.info(
      `✅ CMS detection completed: ${result.platform} (confidence: ${Math.round(result.confidence * 100)}%)`,
      {
        version: result.version,
        plugins: result.plugins.length,
        accessibilityFeatures: result.accessibilityFeatures.length,
      },
    );

    return result;
  }

  /**
   * Initialize CMS accessibility patterns
   */
  private initializeCMSPatterns(): void {
    // WordPress patterns
    this.cmsPatterns.set('wordpress', {
      platform: 'WordPress',
      selectors: {
        navigation: ['.wp-nav-menu', '.menu', '#primary-menu', '.navbar'],
        content: ['.entry-content', '.post-content', '.page-content', '#content'],
        sidebar: ['.sidebar', '.widget-area', '#secondary'],
        footer: ['.site-footer', '#colophon', '.footer'],
        forms: ['.wpcf7-form', '.contact-form', '.comment-form'],
        media: ['.wp-caption', '.gallery', '.wp-block-image'],
      },
      commonIssues: [
        {
          pattern: '.wp-caption img:not([alt])',
          description: 'WordPress caption images missing alt text',
          severity: 'high',
          recommendation: 'Add alt text to all images in WordPress media library',
        },
        {
          pattern: '.menu-item a:not([aria-label]):not([title])',
          description: 'Menu items without accessible labels',
          severity: 'medium',
          recommendation: 'Add descriptive labels to navigation menu items',
        },
        {
          pattern: '.widget:not([role]):not([aria-label])',
          description: 'Widgets without proper ARIA labeling',
          severity: 'medium',
          recommendation: 'Add ARIA labels to sidebar widgets',
        },
      ],
      accessibilityPlugins: [
        'wp-accessibility',
        'accessibility-checker',
        'userway-accessibility',
        'accessibe',
        'one-click-accessibility',
      ],
      themePatterns: ['twentytwentythree', 'twentytwentytwo', 'astra', 'generatepress', 'oceanwp'],
    });

    // Drupal patterns
    this.cmsPatterns.set('drupal', {
      platform: 'Drupal',
      selectors: {
        navigation: ['.region-navigation', '.menu', '#main-menu'],
        content: ['.region-content', '.node-content', '.field-content'],
        sidebar: ['.region-sidebar-first', '.region-sidebar-second'],
        footer: ['.region-footer'],
        forms: ['.webform', '.contact-form', '.user-form'],
        media: ['.field-type-image', '.media-element'],
      },
      commonIssues: [
        {
          pattern: '.field-type-image img:not([alt])',
          description: 'Drupal image fields missing alt text',
          severity: 'high',
          recommendation: 'Configure alt text requirements in Drupal image field settings',
        },
        {
          pattern: '.view-content .views-row:not([role])',
          description: 'Views content without semantic structure',
          severity: 'medium',
          recommendation: 'Add proper ARIA roles to Drupal views',
        },
      ],
      accessibilityPlugins: [
        'accessibility',
        'block_aria_landmark_roles',
        'fluidproject_ui_options',
      ],
      themePatterns: ['bartik', 'seven', 'classy', 'stable'],
    });

    // Shopify patterns
    this.cmsPatterns.set('shopify', {
      platform: 'Shopify',
      selectors: {
        navigation: ['.site-nav', '.main-nav', '.header-nav'],
        content: ['.main-content', '.product-content', '.collection-content'],
        sidebar: ['.sidebar', '.collection-sidebar'],
        footer: ['.site-footer', '.footer'],
        forms: ['.contact-form', '.newsletter-form', '.product-form'],
        media: ['.product-image', '.collection-image', '.featured-image'],
      },
      commonIssues: [
        {
          pattern: '.product-image img:not([alt])',
          description: 'Product images missing alt text',
          severity: 'high',
          recommendation: 'Add alt text to all product images in Shopify admin',
        },
        {
          pattern: '.btn:not([aria-label]):not([title])',
          description: 'Buttons without accessible labels',
          severity: 'medium',
          recommendation: 'Add descriptive labels to all interactive buttons',
        },
      ],
      accessibilityPlugins: ['accessibility-enabler', 'accessibly', 'userway'],
      themePatterns: ['dawn', 'debut', 'brooklyn', 'minimal'],
    });

    // Add more CMS patterns...
    this.addAdditionalCMSPatterns();
  }

  /**
   * Add additional CMS patterns
   */
  private addAdditionalCMSPatterns(): void {
    // Joomla patterns
    this.cmsPatterns.set('joomla', {
      platform: 'Joomla',
      selectors: {
        navigation: ['.nav', '.menu', '#nav'],
        content: ['.item-page', '.article-content', '#content'],
        sidebar: ['.moduletable', '.sidebar'],
        footer: ['.footer'],
        forms: ['.contact-form', '.form'],
        media: ['.img-responsive', '.image'],
      },
      commonIssues: [
        {
          pattern: 'img:not([alt])',
          description: 'Images missing alt attributes',
          severity: 'high',
          recommendation: 'Add alt text to images in Joomla media manager',
        },
      ],
      accessibilityPlugins: ['accessibility', 'joomla-accessibility'],
      themePatterns: ['cassiopeia', 'protostar'],
    });

    // Wix patterns
    this.cmsPatterns.set('wix', {
      platform: 'Wix',
      selectors: {
        navigation: ['[data-testid="linkBar"]', '.wixui-navigation'],
        content: ['[data-testid="richTextElement"]', '.wixui-rich-text'],
        sidebar: ['.wixui-sidebar'],
        footer: ['[data-testid="footer"]'],
        forms: ['[data-testid="form"]'],
        media: ['[data-testid="image"]', 'wix-image'],
      },
      commonIssues: [
        {
          pattern: '[data-testid="image"]:not([alt])',
          description: 'Wix images missing alt text',
          severity: 'high',
          recommendation: 'Add alt text in Wix editor image settings',
        },
      ],
      accessibilityPlugins: ['wix-accessibility'],
      themePatterns: ['wix-template'],
    });

    // Squarespace patterns
    this.cmsPatterns.set('squarespace', {
      platform: 'Squarespace',
      selectors: {
        navigation: ['.header-nav', '.main-nav'],
        content: ['.sqs-block-content', '.entry-content'],
        sidebar: ['.sidebar'],
        footer: ['.footer'],
        forms: ['.sqs-block-form'],
        media: ['.sqs-block-image', '.image-block'],
      },
      commonIssues: [
        {
          pattern: '.sqs-block-image img:not([alt])',
          description: 'Squarespace images missing alt text',
          severity: 'high',
          recommendation: 'Add alt text in Squarespace image block settings',
        },
      ],
      accessibilityPlugins: ['squarespace-accessibility'],
      themePatterns: ['squarespace-template'],
    });
  }

  /**
   * Inject CMS detection functions into the page
   */
  private async injectCMSDetectionFunctions(page: Page): Promise<void> {
    await page.evaluateOnNewDocument(() => {
      (window as any).wcagCMSDetection = {
        /**
         * Detect all CMS platforms
         */
        detectAllPlatforms() {
          const detections = [
            this.detectWordPress(),
            this.detectDrupal(),
            this.detectJoomla(),
            this.detectShopify(),
            this.detectWix(),
            this.detectSquarespace(),
            this.detectCustomCMS(),
          ].filter((detection) => detection.confidence > 0);

          // Return the detection with highest confidence
          return (
            detections.sort((a, b) => b.confidence - a.confidence)[0] || {
              platform: 'unknown',
              confidence: 0,
              version: null,
              theme: null,
              plugins: [],
              indicators: [],
            }
          );
        },

        /**
         * Detect WordPress
         */
        detectWordPress() {
          let confidence = 0;
          const indicators = [];
          const plugins = [];
          let version = null;
          let theme = null;

          // Check for WordPress indicators
          if (document.querySelector('meta[name="generator"][content*="WordPress"]')) {
            confidence += 0.4;
            indicators.push('WordPress generator meta tag');
            const versionMatch = document
              .querySelector('meta[name="generator"]')
              ?.getAttribute('content')
              ?.match(/WordPress ([\d.]+)/);
            if (versionMatch) version = versionMatch[1];
          }

          if (
            document.querySelector('link[href*="wp-content"]') ||
            document.querySelector('script[src*="wp-content"]')
          ) {
            confidence += 0.3;
            indicators.push('wp-content directory detected');
          }

          if (
            document.querySelector('body[class*="wp-"]') ||
            document.querySelector('[class*="wp-"]')
          ) {
            confidence += 0.2;
            indicators.push('WordPress CSS classes');
          }

          if (document.querySelector('#wpadminbar')) {
            confidence += 0.1;
            indicators.push('WordPress admin bar');
          }

          // Detect theme
          const themeLink = document.querySelector('link[href*="/themes/"]');
          if (themeLink) {
            const themeMatch = themeLink.getAttribute('href')?.match(/\/themes\/([^\/]+)/);
            if (themeMatch) theme = themeMatch[1];
          }

          // Detect common plugins
          if (document.querySelector('[class*="wpcf7"]')) plugins.push('Contact Form 7');
          if (document.querySelector('[class*="yoast"]')) plugins.push('Yoast SEO');
          if (document.querySelector('[class*="elementor"]')) plugins.push('Elementor');
          if (document.querySelector('[class*="wp-accessibility"]'))
            plugins.push('WP Accessibility');

          return {
            platform: 'wordpress',
            confidence: Math.min(confidence, 1),
            version,
            theme,
            plugins,
            indicators,
          };
        },

        /**
         * Detect Drupal
         */
        detectDrupal() {
          let confidence = 0;
          const indicators = [];
          const plugins = [];
          let version = null;

          if (document.querySelector('meta[name="Generator"][content*="Drupal"]')) {
            confidence += 0.4;
            indicators.push('Drupal generator meta tag');
            const versionMatch = document
              .querySelector('meta[name="Generator"]')
              ?.getAttribute('content')
              ?.match(/Drupal ([\d.]+)/);
            if (versionMatch) version = versionMatch[1];
          }

          if (
            document.querySelector('script[src*="/sites/"]') ||
            document.querySelector('link[href*="/sites/"]')
          ) {
            confidence += 0.3;
            indicators.push('Drupal sites directory');
          }

          if (
            document.querySelector('body[class*="page-"]') &&
            document.querySelector('[class*="region-"]')
          ) {
            confidence += 0.2;
            indicators.push('Drupal CSS classes');
          }

          if (document.querySelector('#toolbar') || document.querySelector('.toolbar')) {
            confidence += 0.1;
            indicators.push('Drupal toolbar');
          }

          // Detect modules
          if (document.querySelector('[class*="webform"]')) plugins.push('Webform');
          if (document.querySelector('[class*="views-"]')) plugins.push('Views');

          return {
            platform: 'drupal',
            confidence: Math.min(confidence, 1),
            version,
            theme: null,
            plugins,
            indicators,
          };
        },

        /**
         * Detect Joomla
         */
        detectJoomla() {
          let confidence = 0;
          const indicators = [];

          if (document.querySelector('meta[name="generator"][content*="Joomla"]')) {
            confidence += 0.4;
            indicators.push('Joomla generator meta tag');
          }

          if (
            document.querySelector('script[src*="/media/jui/"]') ||
            document.querySelector('link[href*="/media/"]')
          ) {
            confidence += 0.3;
            indicators.push('Joomla media directory');
          }

          if (
            document.querySelector('body[class*="site"]') &&
            document.querySelector('[class*="moduletable"]')
          ) {
            confidence += 0.2;
            indicators.push('Joomla CSS classes');
          }

          return {
            platform: 'joomla',
            confidence: Math.min(confidence, 1),
            version: null,
            theme: null,
            plugins: [],
            indicators,
          };
        },

        /**
         * Detect Shopify
         */
        detectShopify() {
          let confidence = 0;
          const indicators = [];

          if (
            document.querySelector('script[src*="cdn.shopify.com"]') ||
            document.querySelector('link[href*="cdn.shopify.com"]')
          ) {
            confidence += 0.5;
            indicators.push('Shopify CDN detected');
          }

          if ((window as any).Shopify || (window as any).ShopifyAnalytics) {
            confidence += 0.3;
            indicators.push('Shopify JavaScript objects');
          }

          if (document.querySelector('form[action*="/cart/add"]')) {
            confidence += 0.2;
            indicators.push('Shopify cart form');
          }

          return {
            platform: 'shopify',
            confidence: Math.min(confidence, 1),
            version: null,
            theme: null,
            plugins: [],
            indicators,
          };
        },

        /**
         * Detect Wix
         */
        detectWix() {
          let confidence = 0;
          const indicators = [];

          if (document.querySelector('meta[name="generator"][content*="Wix"]')) {
            confidence += 0.4;
            indicators.push('Wix generator meta tag');
          }

          if (
            document.querySelector('[data-testid]') &&
            document.querySelector('script[src*="static.wixstatic.com"]')
          ) {
            confidence += 0.4;
            indicators.push('Wix static resources and test IDs');
          }

          if ((window as any).wixBiSession || (window as any).wixDevelopersAnalytics) {
            confidence += 0.2;
            indicators.push('Wix JavaScript objects');
          }

          return {
            platform: 'wix',
            confidence: Math.min(confidence, 1),
            version: null,
            theme: null,
            plugins: [],
            indicators,
          };
        },

        /**
         * Detect Squarespace
         */
        detectSquarespace() {
          let confidence = 0;
          const indicators = [];

          if (
            document.querySelector('script[src*="assets.squarespace.com"]') ||
            document.querySelector('link[href*="assets.squarespace.com"]')
          ) {
            confidence += 0.5;
            indicators.push('Squarespace assets detected');
          }

          if (document.querySelector('[class*="sqs-"]')) {
            confidence += 0.3;
            indicators.push('Squarespace CSS classes');
          }

          if ((window as any).Static || (window as any).Y) {
            confidence += 0.2;
            indicators.push('Squarespace JavaScript framework');
          }

          return {
            platform: 'squarespace',
            confidence: Math.min(confidence, 1),
            version: null,
            theme: null,
            plugins: [],
            indicators,
          };
        },

        /**
         * Detect custom or other CMS platforms
         */
        detectCustomCMS() {
          let confidence = 0;
          const indicators: string[] = [];

          // Look for common CMS indicators
          const cmsIndicators = [
            'meta[name="generator"]',
            '[class*="cms-"]',
            '[id*="cms-"]',
            'script[src*="/admin/"]',
            'link[href*="/admin/"]',
          ];

          cmsIndicators.forEach((selector) => {
            if (document.querySelector(selector)) {
              confidence += 0.1;
              indicators.push(`Generic CMS indicator: ${selector}`);
            }
          });

          return {
            platform: 'custom',
            confidence: Math.min(confidence, 0.5), // Cap at 50% for custom detection
            version: null,
            theme: null,
            plugins: [],
            indicators,
          };
        },
      };
    });
  }

  /**
   * Process detection results and add accessibility analysis
   */
  private async processDetectionResults(
    page: Page,
    detectionResult: any,
    config: CMSOptimizationConfig,
  ): Promise<CMSDetectionResult> {
    const platform = detectionResult.platform;
    const cmsPattern = this.cmsPatterns.get(platform);

    if (!cmsPattern) {
      return {
        platform: detectionResult.platform,
        confidence: detectionResult.confidence,
        version: detectionResult.version,
        theme: detectionResult.theme,
        plugins: detectionResult.plugins || [],
        accessibilityFeatures: [],
        commonIssues: [],
        optimizations: [],
      };
    }

    // Analyze accessibility features and issues
    const accessibilityAnalysis = await this.analyzeAccessibilityFeatures(page, cmsPattern);

    return {
      platform: cmsPattern.platform,
      confidence: detectionResult.confidence,
      version: detectionResult.version,
      theme: detectionResult.theme,
      plugins: detectionResult.plugins || [],
      accessibilityFeatures: accessibilityAnalysis.features,
      commonIssues: accessibilityAnalysis.issues,
      optimizations: this.generateOptimizations(cmsPattern, accessibilityAnalysis),
    };
  }

  /**
   * Analyze accessibility features for detected CMS
   */
  private async analyzeAccessibilityFeatures(
    page: Page,
    pattern: CMSAccessibilityPattern,
  ): Promise<{
    features: string[];
    issues: string[];
  }> {
    const features: string[] = [];
    const issues: string[] = [];

    // Check for accessibility plugins
    for (const plugin of pattern.accessibilityPlugins) {
      const hasPlugin = await page.evaluate((pluginName) => {
        return (
          document.querySelector(`[class*="${pluginName}"]`) !== null ||
          document.querySelector(`[id*="${pluginName}"]`) !== null ||
          document.querySelector(`script[src*="${pluginName}"]`) !== null
        );
      }, plugin);

      if (hasPlugin) {
        features.push(`${plugin} accessibility plugin detected`);
      }
    }

    // Check for common issues
    for (const issue of pattern.commonIssues) {
      const hasIssue = await page.evaluate((selector) => {
        return document.querySelectorAll(selector).length > 0;
      }, issue.pattern);

      if (hasIssue) {
        issues.push(issue.description);
      }
    }

    return { features, issues };
  }

  /**
   * Generate optimization recommendations
   */
  private generateOptimizations(pattern: CMSAccessibilityPattern, analysis: any): string[] {
    const optimizations: string[] = [];

    // Add platform-specific optimizations
    switch (pattern.platform) {
      case 'WordPress':
        optimizations.push('Install WP Accessibility plugin for enhanced accessibility features');
        optimizations.push('Use semantic HTML5 themes like Twenty Twenty-Three');
        optimizations.push('Configure alt text requirements in media library settings');
        break;
      case 'Drupal':
        optimizations.push('Enable Accessibility module for enhanced features');
        optimizations.push('Configure proper ARIA landmarks in block layouts');
        optimizations.push('Use semantic field templates for content types');
        break;
      case 'Shopify':
        optimizations.push('Use accessibility-focused themes like Dawn');
        optimizations.push('Add descriptive alt text to all product images');
        optimizations.push('Implement proper form labels and error messages');
        break;
    }

    // Add issue-specific optimizations
    if (analysis.issues.length > 0) {
      optimizations.push('Review and address detected accessibility issues');
      optimizations.push('Implement regular accessibility audits for CMS content');
    }

    return optimizations;
  }
}

export default CMSDetector;
