/**
 * Lazy-loaded Components for HIPAA Dashboard
 * Implements code splitting for better performance
 */

import React, { Suspense } from 'react';
import { lazyLoadWithPerformance } from '@/utils/performance';
import {
  LoadingState,
  SkeletonOverviewCard,
  SkeletonModuleCard,
} from '@/components/ui/LoadingStates';

// ===== LAZY LOADED COMPONENTS =====

// Main dashboard components
export const LazyHipaaOverviewCard = lazyLoadWithPerformance(
  () =>
    import('./HipaaOverviewCard').then((module) => ({ default: module.HipaaOverviewCard })),
  'HipaaOverviewCard',
);

export const LazyHipaaModuleCard = lazyLoadWithPerformance(
  () => import('./HipaaModuleCard').then((module) => ({ default: module.HipaaModuleCard })),
  'HipaaModuleCard',
);

// Shared components
export const LazyComplianceMetrics = lazyLoadWithPerformance(
  () =>
    import('../shared/ComplianceMetrics').then((module) => ({
      default: module.ComplianceMetrics,
    })),
  'ComplianceMetrics',
);

export const LazyRiskLevelIndicator = lazyLoadWithPerformance(
  () =>
    import('../shared/RiskLevelIndicator').then((module) => ({
      default: module.RiskLevelIndicator,
    })),
  'RiskLevelIndicator',
);

export const LazyScanStatusBadge = lazyLoadWithPerformance(
  () =>
    import('../shared/ScanStatusBadge').then((module) => ({
      default: module.ScanStatusBadge,
    })),
  'ScanStatusBadge',
);

// Navigation components
export const LazyComplianceBreadcrumb = lazyLoadWithPerformance(
  () =>
    import('../../navigation/ComplianceBreadcrumb').then((module) => ({
      default: module.ComplianceBreadcrumb,
    })),
  'ComplianceBreadcrumb',
);

// Chart components (if any)
// ComplianceChart component not yet implemented
// export const LazyComplianceChart = lazyLoadWithPerformance(
//   () => import('../shared/ComplianceChart').then((module) => ({ default: module.ComplianceChart })),
//   'ComplianceChart',
// );

// ===== WRAPPER COMPONENTS WITH SUSPENSE =====

interface LazyWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  name?: string;
}

function LazyWrapper({ children, fallback, name }: LazyWrapperProps) {
  const defaultFallback = <LoadingState message={`Loading ${name || 'component'}...`} />;

  return <Suspense fallback={fallback || defaultFallback}>{children}</Suspense>;
}

// ===== DASHBOARD SPECIFIC LAZY COMPONENTS =====

interface LazyHipaaOverviewProps {
  overallScore: number;
  riskLevel: 'critical' | 'high' | 'medium' | 'low';
  lastScanDate: string;
  totalScans: number;
  complianceStatus: 'compliant' | 'partially_compliant' | 'non_compliant';
  loading?: boolean;
}

export function LazyHipaaOverview(props: LazyHipaaOverviewProps) {
  return (
    <LazyWrapper fallback={<SkeletonOverviewCard />} name="HIPAA Overview">
      <LazyHipaaOverviewCard {...props} />
    </LazyWrapper>
  );
}

interface LazyHipaaModuleProps {
  moduleType: 'privacy' | 'security';
  latestScore: number;
  scanCount: number;
  lastScanDate: string;
  status: 'active' | 'needs_attention' | 'not_scanned';
  onStartScan: () => void;
  onViewResults: () => void;
  loading?: boolean;
}

export function LazyHipaaModule(props: LazyHipaaModuleProps) {
  return (
    <LazyWrapper fallback={<SkeletonModuleCard />} name={`${props.moduleType} module`}>
      <LazyHipaaModuleCard {...props} />
    </LazyWrapper>
  );
}

// ===== ROUTE-BASED LAZY LOADING =====

// Privacy module pages
export const LazyPrivacyScansPage = lazyLoadWithPerformance(
  () => import('../../../app/dashboard/hipaa/privacy/page'),
  'PrivacyScansPage',
);

export const LazyPrivacyScanDetailsPage = lazyLoadWithPerformance(
  () => import('../../../app/dashboard/hipaa/privacy/[scanId]/page'),
  'PrivacyScanDetailsPage',
);

// Security module pages
export const LazySecurityScansPage = lazyLoadWithPerformance(
  () => import('../../../app/dashboard/hipaa/security/page'),
  'SecurityScansPage',
);

export const LazySecurityScanDetailsPage = lazyLoadWithPerformance(
  () => import('../../../app/dashboard/hipaa/security/[scanId]/page'),
  'SecurityScanDetailsPage',
);

// Test page
export const LazyTestPage = lazyLoadWithPerformance(
  () => import('../../../app/dashboard/hipaa/test/page'),
  'TestPage',
);

// ===== DYNAMIC IMPORTS FOR HEAVY COMPONENTS =====

/**
 * Dynamically import chart library only when needed
 * TODO: Install chart.js dependencies
 */
// export async function loadChartLibrary() {
//   const [{ Chart }, { CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend }] =
//     await Promise.all([import('chart.js/auto'), import('chart.js')]);

//   Chart.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

//   return Chart;
// }

/**
 * Dynamically import date library only when needed
 * TODO: Install dayjs dependencies
 */
// export async function loadDateLibrary() {
//   const { default: dayjs } = await import('dayjs');
//   const relativeTime = await import('dayjs/plugin/relativeTime');
//   const utc = await import('dayjs/plugin/utc');
//   const timezone = await import('dayjs/plugin/timezone');

//   dayjs.extend(relativeTime.default);
//   dayjs.extend(utc.default);
//   dayjs.extend(timezone.default);

//   return dayjs;
// }

/**
 * Dynamically import PDF generation library only when needed
 * TODO: Install jspdf and html2canvas dependencies
 */
// export async function loadPdfLibrary() {
//   const { default: jsPDF } = await import('jspdf');
//   const html2canvas = await import('html2canvas');

//   return { jsPDF, html2canvas: html2canvas.default };
// }

/**
 * Dynamically import CSV export library only when needed
 * TODO: Install papaparse dependency
 */
// export async function loadCsvLibrary() {
//   const { default: Papa } = await import('papaparse');
//   return Papa;
// }

// ===== PRELOADING UTILITIES =====

/**
 * Preload critical components
 */
export function preloadCriticalComponents() {
  // Preload components that are likely to be needed soon
  const criticalComponents = [
    () => import('./HipaaOverviewCard'),
    () => import('./HipaaModuleCard'),
    () => import('../shared/ComplianceMetrics'),
  ];

  criticalComponents.forEach((importFunc) => {
    // Use requestIdleCallback if available, otherwise use setTimeout
    if (typeof requestIdleCallback !== 'undefined') {
      requestIdleCallback(() => importFunc());
    } else {
      setTimeout(() => importFunc(), 100);
    }
  });
}

/**
 * Preload components based on user interaction
 */
export function preloadOnHover(
  componentImport: () => Promise<{ default: React.ComponentType<unknown> }>,
) {
  return {
    onMouseEnter: () => {
      componentImport();
    },
    onFocus: () => {
      componentImport();
    },
  };
}

/**
 * Preload route components
 */
export function preloadRouteComponents() {
  const routeComponents = [
    () => import('../../../app/dashboard/hipaa/privacy/page'),
    () => import('../../../app/dashboard/hipaa/security/page'),
  ];

  routeComponents.forEach((importFunc) => {
    if (typeof requestIdleCallback !== 'undefined') {
      requestIdleCallback(() => importFunc());
    } else {
      setTimeout(() => importFunc(), 1000);
    }
  });
}

// ===== ERROR BOUNDARIES FOR LAZY COMPONENTS =====

interface LazyErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class LazyErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  LazyErrorBoundaryState
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): LazyErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // eslint-disable-next-line no-console
    console.error('Lazy component loading error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
            <p className="text-red-800 text-sm">
              Failed to load component. Please refresh the page.
            </p>
          </div>
        )
      );
    }

    return this.props.children;
  }
}

export { LazyErrorBoundary };

// ===== EXPORT ALL LAZY COMPONENTS =====

export default {
  LazyHipaaOverviewCard,
  LazyHipaaModuleCard,
  LazyComplianceMetrics,
  LazyRiskLevelIndicator,
  LazyScanStatusBadge,
  LazyComplianceBreadcrumb,
  LazyHipaaOverview,
  LazyHipaaModule,
  LazyPrivacyScansPage,
  LazyPrivacyScanDetailsPage,
  LazySecurityScansPage,
  LazySecurityScanDetailsPage,
  LazyTestPage,
  // loadChartLibrary, // TODO: Install chart.js dependencies
  // loadDateLibrary, // TODO: Install dayjs dependencies
  // loadPdfLibrary, // TODO: Install jspdf and html2canvas dependencies
  // loadCsvLibrary, // TODO: Install papaparse dependency
  preloadCriticalComponents,
  preloadOnHover,
  preloadRouteComponents,
  LazyErrorBoundary,
};
