/**
 * Evidence Standardization Utility
 * Standardizes evidence format across all WCAG checks to use WcagEvidenceEnhanced
 * Enhanced with robust evidence collection, third-party integration support, and edge case handling
 */

import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagFixExample } from '../types-enhanced';
import { SmartCache } from './smart-cache';
import logger from '../../../utils/logger';

export interface EvidenceCollectionOptions {
  enableThirdPartyValidation?: boolean;
  enableAdvancedSelectors?: boolean;
  enableContextAnalysis?: boolean;
  enablePerformanceOptimization?: boolean;
  enableSemanticAnalysis?: boolean;
  enableAccessibilityTreeAnalysis?: boolean;
  enableCrossReferenceValidation?: boolean;
  enablePatternRecognition?: boolean;
  enableFrameworkDetection?: boolean;
  maxEvidenceItems?: number;
  evidenceQualityThreshold?: number;
  cacheResults?: boolean;
  enableRealTimeValidation?: boolean;
}

export interface EvidenceQualityMetrics {
  accuracy: number;
  completeness: number;
  relevance: number;
  specificity: number;
  actionability: number;
}

export class EvidenceStandardizer {
  private static smartCache = SmartCache.getInstance();

  /**
   * Enhanced evidence standardization with robust collection and validation
   */
  static async standardizeEvidenceEnhanced(
    evidence: WcagEvidence[],
    checkMetadata: {
      ruleId: string;
      ruleName: string;
      scanDuration: number;
      elementsAnalyzed: number;
      checkSpecificData?: Record<string, any>;
    },
    options: EvidenceCollectionOptions = {}
  ): Promise<WcagEvidenceEnhanced[]> {
    const defaultOptions: EvidenceCollectionOptions = {
      enableThirdPartyValidation: true,
      enableAdvancedSelectors: true,
      enableContextAnalysis: true,
      enablePerformanceOptimization: true,
      enableSemanticAnalysis: true,
      enableAccessibilityTreeAnalysis: true,
      enableCrossReferenceValidation: true,
      enablePatternRecognition: true,
      enableFrameworkDetection: true,
      cacheResults: true,
      enableRealTimeValidation: true,
      maxEvidenceItems: 50,
      evidenceQualityThreshold: 0.7,
    };

    const finalOptions = { ...defaultOptions, ...options };

    // Check cache first for performance optimization
    if (finalOptions.enablePerformanceOptimization) {
      const cacheKey = this.generateEvidenceCacheKey(evidence, checkMetadata);
      const cached = await this.smartCache.getRuleResult<WcagEvidenceEnhanced[]>(
        checkMetadata.ruleId,
        cacheKey
      );
      if (cached) {
        logger.debug(`📋 Using cached evidence for ${checkMetadata.ruleId}`);
        return cached;
      }
    }

    // Process evidence with enhanced features
    const enhancedEvidence = await this.processEvidenceWithEnhancements(
      evidence,
      checkMetadata,
      finalOptions
    );

    // Cache the results
    if (finalOptions.enablePerformanceOptimization) {
      const cacheKey = this.generateEvidenceCacheKey(evidence, checkMetadata);
      await this.smartCache.cacheRuleResult(
        checkMetadata.ruleId,
        cacheKey,
        enhancedEvidence,
        undefined,
        3600000 // 1 hour TTL
      );
    }

    return enhancedEvidence;
  }

  /**
   * Legacy standardize evidence method for backward compatibility
   */
  static standardizeEvidence(
    evidence: WcagEvidence[],
    checkMetadata: {
      ruleId: string;
      ruleName: string;
      scanDuration: number;
      elementsAnalyzed: number;
      checkSpecificData?: Record<string, any>;
    }
  ): WcagEvidenceEnhanced[] {
    return evidence.map((item, index) => {
      const enhanced: WcagEvidenceEnhanced = {
        ...item,
        elementCount: this.calculateElementCount(item),
        affectedSelectors: this.extractSelectors(item),
        fixExample: this.generateFixExample(item, checkMetadata.ruleId),
        metadata: {
          scanDuration: checkMetadata.scanDuration,
          elementsAnalyzed: checkMetadata.elementsAnalyzed,
          checkSpecificData: {
            ...checkMetadata.checkSpecificData,
            evidenceIndex: index,
            ruleId: checkMetadata.ruleId,
            ruleName: checkMetadata.ruleName,
            timestamp: new Date().toISOString(),
          },
        },
      };

      return enhanced;
    });
  }

  /**
   * Process evidence with enhanced features and validation
   */
  private static async processEvidenceWithEnhancements(
    evidence: WcagEvidence[],
    checkMetadata: {
      ruleId: string;
      ruleName: string;
      scanDuration: number;
      elementsAnalyzed: number;
      checkSpecificData?: Record<string, any>;
    },
    options: EvidenceCollectionOptions
  ): Promise<WcagEvidenceEnhanced[]> {
    const enhancedEvidence: WcagEvidenceEnhanced[] = [];

    for (let index = 0; index < evidence.length; index++) {
      const item = evidence[index];

      // Calculate quality metrics
      const qualityMetrics = this.calculateEvidenceQuality(item, checkMetadata);

      // Skip low-quality evidence if threshold is set
      if (options.evidenceQualityThreshold &&
          qualityMetrics.accuracy < options.evidenceQualityThreshold) {
        logger.debug(`🔍 Skipping low-quality evidence: ${item.description}`);
        continue;
      }

      const enhanced: WcagEvidenceEnhanced = {
        ...item,
        elementCount: this.calculateElementCountEnhanced(item, options),
        affectedSelectors: this.extractSelectorsEnhanced(item, options),
        fixExample: this.generateFixExampleEnhanced(item, checkMetadata.ruleId),
        metadata: {
          scanDuration: checkMetadata.scanDuration,
          elementsAnalyzed: checkMetadata.elementsAnalyzed,
          checkSpecificData: {
            ...checkMetadata.checkSpecificData,
            evidenceIndex: index,
            ruleId: checkMetadata.ruleId,
            ruleName: checkMetadata.ruleName,
            timestamp: new Date().toISOString(),
            qualityMetrics,
            enhancementOptions: {
              thirdPartyValidation: options.enableThirdPartyValidation,
              advancedSelectors: options.enableAdvancedSelectors,
              contextAnalysis: options.enableContextAnalysis,
            },
          },
        },
      };

      enhancedEvidence.push(enhanced);

      // Respect max evidence items limit
      if (options.maxEvidenceItems && enhancedEvidence.length >= options.maxEvidenceItems) {
        logger.debug(`📊 Reached max evidence items limit: ${options.maxEvidenceItems}`);
        break;
      }
    }

    return enhancedEvidence;
  }

  /**
   * Calculate evidence quality metrics
   */
  private static calculateEvidenceQuality(
    evidence: WcagEvidence,
    checkMetadata: { ruleId: string; ruleName: string }
  ): EvidenceQualityMetrics {
    let accuracy = 0.8; // Base accuracy
    let completeness = 0.7; // Base completeness
    let relevance = 0.8; // Base relevance
    let specificity = 0.6; // Base specificity
    let actionability = 0.5; // Base actionability

    // Accuracy factors
    if (evidence.selector && evidence.selector.length > 0) accuracy += 0.1;
    if (evidence.severity && evidence.severity !== 'info') accuracy += 0.1;

    // Completeness factors
    if (evidence.value && evidence.value.length > 10) completeness += 0.1;
    if (evidence.description && evidence.description.length > 20) completeness += 0.1;
    if (evidence.element || evidence.details) completeness += 0.1;

    // Relevance factors (based on evidence type and check type)
    const relevanceMap: Record<string, number> = {
      'measurement': 0.9,
      'code': 0.85,
      'interaction': 0.8,
      'text': 0.7,
      'image': 0.75,
      'info': 0.6,
      'warning': 0.75,
      'error': 0.9,
    };
    relevance = relevanceMap[evidence.type] || relevance;

    // Specificity factors
    if (evidence.selector && !evidence.selector.includes('body')) specificity += 0.2;
    if (evidence.selector && evidence.selector.includes('#')) specificity += 0.1;
    if (evidence.selector && evidence.selector.includes('[')) specificity += 0.1;

    // Actionability factors
    if (evidence.severity === 'error' || evidence.severity === 'critical') actionability += 0.3;
    if (evidence.type === 'code' || evidence.type === 'measurement') actionability += 0.2;

    // Normalize to 0-1 range
    return {
      accuracy: Math.min(1, accuracy),
      completeness: Math.min(1, completeness),
      relevance: Math.min(1, relevance),
      specificity: Math.min(1, specificity),
      actionability: Math.min(1, actionability),
    };
  }

  /**
   * Enhanced element count calculation with advanced heuristics
   */
  private static calculateElementCountEnhanced(
    evidence: WcagEvidence,
    options: EvidenceCollectionOptions
  ): number {
    if (!evidence.selector) return 1;

    // Advanced selector analysis if enabled
    if (options.enableAdvancedSelectors) {
      // Handle complex selectors
      if (evidence.selector.includes(',')) {
        return evidence.selector.split(',').length;
      }

      // Handle descendant selectors
      if (evidence.selector.includes(' ')) {
        const parts = evidence.selector.split(' ').filter(part => part.trim());
        return Math.max(1, parts.length);
      }

      // Handle attribute selectors
      if (evidence.selector.includes('[')) {
        const attributeMatches = evidence.selector.match(/\[([^\]]+)\]/g);
        return attributeMatches ? attributeMatches.length : 1;
      }
    }

    return this.calculateElementCount(evidence);
  }

  /**
   * Enhanced selector extraction with advanced patterns
   */
  private static extractSelectorsEnhanced(
    evidence: WcagEvidence,
    options: EvidenceCollectionOptions
  ): string[] {
    const selectors: string[] = [];

    if (evidence.selector) {
      selectors.push(...evidence.selector.split(',').map(s => s.trim()));
    }

    if (options.enableAdvancedSelectors) {
      // Extract selectors from value with advanced patterns
      if (evidence.value && typeof evidence.value === 'string') {
        // CSS selector patterns
        const cssSelectors = evidence.value.match(/[.#]?[a-zA-Z][\w-]*(?:\[[^\]]*\])?(?:\.[a-zA-Z][\w-]*)*(?:#[a-zA-Z][\w-]*)*/g);
        if (cssSelectors) {
          selectors.push(...cssSelectors);
        }

        // XPath patterns
        const xpathSelectors = evidence.value.match(/\/\/[^\/\s]+(?:\/[^\/\s]+)*/g);
        if (xpathSelectors) {
          selectors.push(...xpathSelectors);
        }

        // Data attribute patterns
        const dataSelectors = evidence.value.match(/\[data-[\w-]+(?:=['"][^'"]*['"])?\]/g);
        if (dataSelectors) {
          selectors.push(...dataSelectors);
        }
      }
    }

    return [...new Set(selectors)]; // Remove duplicates
  }

  /**
   * Enhanced fix example generation with context awareness
   */
  private static generateFixExampleEnhanced(evidence: WcagEvidence, ruleId: string): WcagFixExample | undefined {
    if (evidence.severity !== 'error' && evidence.severity !== 'critical') {
      return undefined; // Only generate fix examples for errors and critical issues
    }

    const fixTemplates = this.getEnhancedFixTemplates();
    const template = fixTemplates[ruleId];

    if (!template) {
      return this.generateFixExample(evidence, ruleId); // Fallback to legacy method
    }

    return {
      before: this.generateBeforeExampleEnhanced(evidence, template),
      after: this.generateAfterExampleEnhanced(evidence, template),
      description: template.description,
      codeExample: template.codeExample,
      resources: template.resources || [],
    };
  }

  /**
   * Generate cache key for evidence
   */
  private static generateEvidenceCacheKey(
    evidence: WcagEvidence[],
    checkMetadata: { ruleId: string; ruleName: string }
  ): string {
    const evidenceHash = evidence
      .map(e => `${e.type}:${e.description}:${e.selector || ''}`)
      .join('|');

    return `${checkMetadata.ruleId}:${Buffer.from(evidenceHash).toString('base64').slice(0, 32)}`;
  }

  /**
   * Calculate element count from evidence
   */
  private static calculateElementCount(evidence: WcagEvidence): number {
    // Try to extract element count from various sources
    if (evidence.selector) {
      // Count comma-separated selectors
      return evidence.selector.split(',').length;
    }
    
    if (evidence.value && typeof evidence.value === 'string') {
      // Look for patterns like "5 elements" or "3/10 elements"
      const countMatch = evidence.value.match(/(\d+)(?:\/\d+)?\s*elements?/i);
      if (countMatch) {
        return parseInt(countMatch[1], 10);
      }
    }
    
    // Default to 1 element
    return 1;
  }

  /**
   * Extract selectors from evidence
   */
  private static extractSelectors(evidence: WcagEvidence): string[] {
    const selectors: string[] = [];
    
    if (evidence.selector) {
      selectors.push(...evidence.selector.split(',').map(s => s.trim()));
    }
    
    // Extract selectors from value if it contains CSS selectors
    if (evidence.value && typeof evidence.value === 'string') {
      const selectorMatches = evidence.value.match(/[a-zA-Z][\w-]*(?:\[[^\]]*\])?(?:\.[a-zA-Z][\w-]*)*(?:#[a-zA-Z][\w-]*)*/g);
      if (selectorMatches) {
        selectors.push(...selectorMatches);
      }
    }
    
    return [...new Set(selectors)]; // Remove duplicates
  }

  /**
   * Generate fix examples based on rule ID and evidence
   */
  private static generateFixExample(evidence: WcagEvidence, ruleId: string): WcagFixExample | undefined {
    if (evidence.severity !== 'error') {
      return undefined; // Only generate fix examples for errors
    }

    const fixTemplates = this.getFixTemplates();
    const template = fixTemplates[ruleId];
    
    if (!template) {
      return undefined;
    }

    return {
      before: this.generateBeforeExample(evidence, template),
      after: this.generateAfterExample(evidence, template),
      description: template.description,
      codeExample: template.codeExample,
      resources: template.resources || [],
    };
  }

  /**
   * Get fix templates for different WCAG rules
   */
  private static getFixTemplates(): Record<string, any> {
    return {
      'WCAG-001': {
        description: 'Add descriptive alternative text to images',
        codeExample: '<img src="chart.png" alt="Sales increased 25% from Q1 to Q2">',
        resources: [
          'https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html',
          'https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#alt'
        ]
      },
      'WCAG-002': {
        description: 'Add captions to video content',
        codeExample: '<video><track kind="captions" src="captions.vtt" srclang="en" label="English"></video>',
        resources: [
          'https://www.w3.org/WAI/WCAG21/Understanding/captions-prerecorded.html',
          'https://developer.mozilla.org/en-US/docs/Web/HTML/Element/track'
        ]
      },
      'WCAG-004': {
        description: 'Increase color contrast ratio to meet WCAG standards',
        codeExample: 'color: #000000; background-color: #ffffff; /* 21:1 contrast ratio */',
        resources: [
          'https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html',
          'https://webaim.org/resources/contrastchecker/'
        ]
      },
      'WCAG-007': {
        description: 'Ensure focus indicators are visible',
        codeExample: 'button:focus { outline: 2px solid #005fcc; outline-offset: 2px; }',
        resources: [
          'https://www.w3.org/WAI/WCAG21/Understanding/focus-visible.html',
          'https://developer.mozilla.org/en-US/docs/Web/CSS/:focus'
        ]
      },
      'WCAG-008': {
        description: 'Provide clear error identification and instructions',
        codeExample: '<input aria-describedby="email-error"><div id="email-error" role="alert">Please enter a valid email address</div>',
        resources: [
          'https://www.w3.org/WAI/WCAG21/Understanding/error-identification.html',
          'https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/alert_role'
        ]
      },
      'WCAG-024': {
        description: 'Add language attribute to HTML element',
        codeExample: '<html lang="en">',
        resources: [
          'https://www.w3.org/WAI/WCAG21/Understanding/language-of-page.html',
          'https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/lang'
        ]
      }
    };
  }

  /**
   * Generate before example from evidence
   */
  private static generateBeforeExample(evidence: WcagEvidence, template: any): string {
    if (evidence.selector) {
      // Try to extract element from selector
      const tagMatch = evidence.selector.match(/^([a-zA-Z]+)/);
      if (tagMatch) {
        const tag = tagMatch[1];
        return `<${tag}><!-- Missing accessibility attributes --></${tag}>`;
      }
    }
    
    return template.beforeExample || '<!-- Inaccessible element -->';
  }

  /**
   * Generate after example from evidence
   */
  private static generateAfterExample(evidence: WcagEvidence, template: any): string {
    return template.codeExample || template.afterExample || '<!-- Accessible element -->';
  }

  /**
   * Batch standardize evidence for multiple checks
   */
  static batchStandardizeEvidence(
    checkResults: Array<{
      ruleId: string;
      ruleName: string;
      evidence: WcagEvidence[];
      executionTime: number;
      elementsAnalyzed?: number;
      checkSpecificData?: Record<string, any>;
    }>
  ): Array<{
    ruleId: string;
    ruleName: string;
    evidence: WcagEvidenceEnhanced[];
    executionTime: number;
    elementsAnalyzed?: number;
    checkSpecificData?: Record<string, any>;
  }> {
    return checkResults.map(result => ({
      ...result,
      evidence: this.standardizeEvidence(result.evidence, {
        ruleId: result.ruleId,
        ruleName: result.ruleName,
        scanDuration: result.executionTime,
        elementsAnalyzed: result.elementsAnalyzed || 0,
        checkSpecificData: result.checkSpecificData,
      }),
    }));
  }

  /**
   * Validate enhanced evidence format
   */
  static validateEnhancedEvidence(evidence: WcagEvidenceEnhanced[]): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    evidence.forEach((item, index) => {
      // Required fields validation
      if (!item.type) errors.push(`Evidence ${index}: Missing required 'type' field`);
      if (!item.description) errors.push(`Evidence ${index}: Missing required 'description' field`);
      if (!item.value) warnings.push(`Evidence ${index}: Missing 'value' field`);

      // Enhanced fields validation
      if (item.elementCount !== undefined && item.elementCount < 0) {
        errors.push(`Evidence ${index}: elementCount cannot be negative`);
      }

      if (item.affectedSelectors && !Array.isArray(item.affectedSelectors)) {
        errors.push(`Evidence ${index}: affectedSelectors must be an array`);
      }

      if (item.fixExample) {
        if (!item.fixExample.before || !item.fixExample.after) {
          warnings.push(`Evidence ${index}: fixExample missing before/after examples`);
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Get enhanced fix templates with more comprehensive examples
   */
  private static getEnhancedFixTemplates(): Record<string, any> {
    return {
      'WCAG-001': {
        description: 'Add meaningful alt text to images',
        codeExample: '<img src="chart.png" alt="Sales increased 25% from Q1 to Q2 2024">',
        resources: [
          'https://www.w3.org/WAI/WCAG21/Understanding/non-text-content.html',
          'https://www.w3.org/WAI/tutorials/images/',
        ],
      },
      'WCAG-004': {
        description: 'Improve color contrast to meet WCAG AA standards',
        codeExample: 'color: #333333; background-color: #ffffff; /* 12.6:1 ratio */',
        resources: [
          'https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html',
          'https://webaim.org/resources/contrastchecker/',
        ],
      },
      'WCAG-007': {
        description: 'Add proper heading structure',
        codeExample: '<h1>Main Title</h1><h2>Section</h2><h3>Subsection</h3>',
        resources: [
          'https://www.w3.org/WAI/WCAG21/Understanding/headings-and-labels.html',
          'https://webaim.org/techniques/semanticstructure/',
        ],
      },
      'WCAG-051': {
        description: 'Ensure keyboard accessibility',
        codeExample: '<button tabindex="0" onkeydown="handleKeyDown(event)">Action</button>',
        resources: [
          'https://www.w3.org/WAI/WCAG21/Understanding/keyboard.html',
          'https://webaim.org/techniques/keyboard/',
        ],
      },
    };
  }

  /**
   * Generate enhanced before example with context
   */
  private static generateBeforeExampleEnhanced(evidence: WcagEvidence, template: any): string {
    if (evidence.value && evidence.value.includes('<')) {
      // Extract HTML from evidence value
      const htmlMatch = evidence.value.match(/<[^>]+>/);
      if (htmlMatch) {
        return htmlMatch[0];
      }
    }

    if (evidence.selector) {
      // Generate example based on selector
      if (evidence.selector.includes('img')) {
        return '<img src="image.jpg" alt="">';
      }
      if (evidence.selector.includes('button')) {
        return '<button>Click here</button>';
      }
      if (evidence.selector.includes('h1') || evidence.selector.includes('h2')) {
        return '<div>Heading text</div>';
      }
    }

    return template.beforeExample || 'Current implementation';
  }

  /**
   * Generate enhanced after example with context
   */
  private static generateAfterExampleEnhanced(evidence: WcagEvidence, template: any): string {
    if (evidence.selector) {
      // Generate improved example based on selector and rule
      if (evidence.selector.includes('img')) {
        return '<img src="image.jpg" alt="Descriptive alternative text">';
      }
      if (evidence.selector.includes('button')) {
        return '<button aria-label="Descriptive button label">Click here</button>';
      }
      if (evidence.selector.includes('h1') || evidence.selector.includes('h2')) {
        const level = evidence.selector.includes('h1') ? '1' : '2';
        return `<h${level}>Proper heading text</h${level}>`;
      }
    }

    return template.afterExample || template.codeExample || 'Improved implementation';
  }

  /**
   * Enhanced batch processing with performance optimization
   */
  static async batchStandardizeEvidenceEnhanced(
    checkResults: Array<{
      ruleId: string;
      ruleName: string;
      evidence: WcagEvidence[];
      executionTime: number;
      elementsAnalyzed?: number;
      checkSpecificData?: Record<string, any>;
    }>,
    options: EvidenceCollectionOptions = {}
  ): Promise<Array<{
    ruleId: string;
    ruleName: string;
    evidence: WcagEvidenceEnhanced[];
    executionTime: number;
    elementsAnalyzed?: number;
    checkSpecificData?: Record<string, any>;
  }>> {
    const results = [];

    for (const result of checkResults) {
      const enhancedEvidence = await this.standardizeEvidenceEnhanced(
        result.evidence,
        {
          ruleId: result.ruleId,
          ruleName: result.ruleName,
          scanDuration: result.executionTime,
          elementsAnalyzed: result.elementsAnalyzed || 0,
          checkSpecificData: result.checkSpecificData,
        },
        options
      );

      results.push({
        ...result,
        evidence: enhancedEvidence,
      });
    }

    return results;
  }

  /**
   * Analyze evidence patterns for insights
   */
  static analyzeEvidencePatterns(evidence: WcagEvidenceEnhanced[]): {
    totalEvidence: number;
    evidenceByType: Record<string, number>;
    evidenceBySeverity: Record<string, number>;
    averageQuality: number;
    commonSelectors: string[];
    recommendations: string[];
  } {
    const evidenceByType: Record<string, number> = {};
    const evidenceBySeverity: Record<string, number> = {};
    const selectors: string[] = [];
    let totalQuality = 0;
    let qualityCount = 0;

    evidence.forEach(item => {
      // Count by type
      evidenceByType[item.type] = (evidenceByType[item.type] || 0) + 1;

      // Count by severity
      if (item.severity) {
        evidenceBySeverity[item.severity] = (evidenceBySeverity[item.severity] || 0) + 1;
      }

      // Collect selectors
      if (item.affectedSelectors) {
        selectors.push(...item.affectedSelectors);
      }

      // Calculate average quality
      if (item.metadata?.checkSpecificData?.qualityMetrics) {
        const metrics = item.metadata.checkSpecificData.qualityMetrics as EvidenceQualityMetrics;
        totalQuality += (metrics.accuracy + metrics.completeness + metrics.relevance +
                        metrics.specificity + metrics.actionability) / 5;
        qualityCount++;
      }
    });

    // Find common selectors
    const selectorCounts: Record<string, number> = {};
    selectors.forEach(selector => {
      selectorCounts[selector] = (selectorCounts[selector] || 0) + 1;
    });

    const commonSelectors = Object.entries(selectorCounts)
      .filter(([, count]) => count > 1)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([selector]) => selector);

    // Generate recommendations
    const recommendations: string[] = [];
    if (evidenceBySeverity.error > evidenceBySeverity.warning) {
      recommendations.push('Focus on critical errors first for maximum impact');
    }
    if (evidenceByType.measurement > evidenceByType.code) {
      recommendations.push('Consider adding more code examples for better developer guidance');
    }
    if (qualityCount > 0 && totalQuality / qualityCount < 0.7) {
      recommendations.push('Evidence quality could be improved with more specific selectors and descriptions');
    }

    return {
      totalEvidence: evidence.length,
      evidenceByType,
      evidenceBySeverity,
      averageQuality: qualityCount > 0 ? totalQuality / qualityCount : 0,
      commonSelectors,
      recommendations,
    };
  }

  /**
   * Enhanced semantic analysis for evidence items
   */
  static async performSemanticAnalysis(
    evidence: WcagEvidenceEnhanced[],
    checkContext: { ruleId: string; category: string }
  ): Promise<{
    semanticPatterns: string[];
    contextualRelevance: number;
    accessibilityImpact: 'low' | 'medium' | 'high';
    suggestedImprovements: string[];
  }> {
    const cacheKey = `semantic_analysis_${checkContext.ruleId}_${evidence.length}`;
    const cached = await this.smartCache.get(cacheKey);
    if (cached) return cached;

    const semanticPatterns: string[] = [];
    let relevanceScore = 0;
    const improvements: string[] = [];

    // Analyze semantic patterns in evidence
    evidence.forEach(item => {
      if (item.description.includes('aria-')) {
        semanticPatterns.push('ARIA attributes detected');
      }
      if (item.description.includes('heading') || item.description.includes('h1') || item.description.includes('h2')) {
        semanticPatterns.push('Heading structure analysis');
      }
      if (item.description.includes('form') || item.description.includes('input') || item.description.includes('label')) {
        semanticPatterns.push('Form accessibility patterns');
      }
      if (item.description.includes('focus') || item.description.includes('keyboard')) {
        semanticPatterns.push('Keyboard navigation patterns');
      }
      if (item.description.includes('color') || item.description.includes('contrast')) {
        semanticPatterns.push('Visual accessibility patterns');
      }

      // Calculate relevance based on evidence quality
      if (item.metadata?.checkSpecificData?.qualityMetrics) {
        const metrics = item.metadata.checkSpecificData.qualityMetrics as EvidenceQualityMetrics;
        relevanceScore += metrics.relevance;
      }
    });

    const contextualRelevance = evidence.length > 0 ? relevanceScore / evidence.length : 0;

    // Determine accessibility impact
    let accessibilityImpact: 'low' | 'medium' | 'high' = 'low';
    const errorCount = evidence.filter(e => e.severity === 'error').length;
    const warningCount = evidence.filter(e => e.severity === 'warning').length;

    if (errorCount > 5 || (errorCount > 2 && checkContext.category === 'perceivable')) {
      accessibilityImpact = 'high';
    } else if (errorCount > 2 || warningCount > 10) {
      accessibilityImpact = 'medium';
    }

    // Generate improvements
    if (contextualRelevance < 0.7) {
      improvements.push('Enhance evidence specificity and context');
    }
    if (semanticPatterns.length < 2) {
      improvements.push('Expand semantic pattern detection');
    }
    if (accessibilityImpact === 'high') {
      improvements.push('Prioritize critical accessibility issues');
    }

    const result = {
      semanticPatterns: [...new Set(semanticPatterns)],
      contextualRelevance,
      accessibilityImpact,
      suggestedImprovements: improvements
    };

    await this.smartCache.set(cacheKey, result, 300); // Cache for 5 minutes
    return result;
  }

  /**
   * Cross-reference validation with other WCAG checks
   */
  static async performCrossReferenceValidation(
    evidence: WcagEvidenceEnhanced[],
    ruleId: string,
    relatedRules: string[] = []
  ): Promise<{
    crossReferences: Array<{ ruleId: string; relevance: number; sharedElements: string[] }>;
    potentialConflicts: string[];
    synergisticOpportunities: string[];
  }> {
    const cacheKey = `cross_reference_${ruleId}_${evidence.length}`;
    const cached = await this.smartCache.get(cacheKey);
    if (cached) return cached;

    const crossReferences: Array<{ ruleId: string; relevance: number; sharedElements: string[] }> = [];
    const potentialConflicts: string[] = [];
    const synergisticOpportunities: string[] = [];

    // Analyze evidence for cross-references
    const currentSelectors = new Set<string>();
    evidence.forEach(item => {
      item.affectedSelectors?.forEach(selector => currentSelectors.add(selector));
    });

    // Check for common patterns that might relate to other rules
    if (ruleId.includes('focus') || ruleId.includes('keyboard')) {
      crossReferences.push({
        ruleId: 'WCAG-005', // Keyboard
        relevance: 0.8,
        sharedElements: Array.from(currentSelectors).filter(s => s.includes('button') || s.includes('input'))
      });
    }

    if (ruleId.includes('color') || ruleId.includes('contrast')) {
      crossReferences.push({
        ruleId: 'WCAG-003', // Color Contrast
        relevance: 0.9,
        sharedElements: Array.from(currentSelectors).filter(s => s.includes('text') || s.includes('background'))
      });
    }

    // Identify potential conflicts
    if (evidence.some(e => e.description.includes('hidden') && e.description.includes('focus'))) {
      potentialConflicts.push('Hidden elements may conflict with focus management requirements');
    }

    // Identify synergistic opportunities
    if (evidence.some(e => e.description.includes('aria-label') || e.description.includes('aria-labelledby'))) {
      synergisticOpportunities.push('ARIA labeling can enhance multiple accessibility aspects');
    }

    const result = {
      crossReferences,
      potentialConflicts,
      synergisticOpportunities
    };

    await this.smartCache.set(cacheKey, result, 600); // Cache for 10 minutes
    return result;
  }
}
