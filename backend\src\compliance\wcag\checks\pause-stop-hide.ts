/**
 * WCAG-045: Pause, Stop, Hide Check
 * Success Criterion: 2.2.2 Pause, Stop, Hide (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckConfig, CheckFunction } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface PauseStopHideConfig extends EnhancedCheckConfig {
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
  enableMultimediaAccessibilityTesting?: boolean;
}

export class PauseStopHideCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();

  async performCheck(config: PauseStopHideConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: PauseStopHideConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
      enableMultimediaAccessibilityTesting: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-045',
      'Pause, Stop, Hide',
      'operable',
      0.0611,
      'A',
      enhancedConfig,
      this.executePauseStopHideCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with motion-specific analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-045',
        ruleName: 'Pause, Stop, Hide',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.75,
          checkType: 'motion-analysis',
          mediaElementsAnalyzed: true,
          animationDetection: true,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
          multimediaAccessibilityTesting: enhancedConfig.enableMultimediaAccessibilityTesting,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.75,
        maxEvidenceItems: 20,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executePauseStopHideCheck(
    page: Page,
    config: PauseStopHideConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Check for moving, blinking, or auto-updating content
    const movingContentAnalysis = await page.evaluate(() => {
      const movingElements: Array<{
        type: string;
        element: string;
        selector: string;
        hasControls: boolean;
        animationType?: string;
        description: string;
        duration?: number;
      }> = [];

      // Check for CSS animations and transitions
      const animatedElements = document.querySelectorAll('*');
      animatedElements.forEach((element, index) => {
        const computedStyle = window.getComputedStyle(element);
        const animationName = computedStyle.animationName;
        const animationDuration = parseFloat(computedStyle.animationDuration) || 0;
        const transitionDuration = parseFloat(computedStyle.transitionDuration) || 0;
        
        if (animationName !== 'none' && animationDuration > 5) {
          const hasControls = element.querySelector('button, input, [role="button"], .pause, .stop, .play') !== null ||
                             element.closest('[data-controls]') !== null;
          
          movingElements.push({
            type: 'css_animation',
            element: element.tagName.toLowerCase(),
            selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
            hasControls,
            animationType: animationName,
            duration: animationDuration,
            description: `CSS animation "${animationName}" running for ${animationDuration}s`,
          });
        }
      });

      // Check for marquee elements
      const marqueeElements = document.querySelectorAll('marquee');
      marqueeElements.forEach((element, index) => {
        const hasControls = element.querySelector('button, input, [role="button"]') !== null ||
                           element.hasAttribute('onmouseover') && element.hasAttribute('onmouseout');
        
        movingElements.push({
          type: 'marquee',
          element: 'marquee',
          selector: `marquee:nth-of-type(${index + 1})`,
          hasControls,
          description: 'Marquee scrolling text element',
        });
      });

      // Check for blinking elements
      const blinkingElements = document.querySelectorAll('blink, .blink, [style*="blink"]');
      blinkingElements.forEach((element, index) => {
        const hasControls = element.querySelector('button, input, [role="button"]') !== null;
        
        movingElements.push({
          type: 'blinking',
          element: element.tagName.toLowerCase(),
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          hasControls,
          description: 'Blinking text element',
        });
      });

      // Check for auto-updating content (common patterns)
      const autoUpdateElements = document.querySelectorAll(
        '[data-auto-update], [data-refresh], .auto-update, .live-update, .ticker, .news-ticker'
      );
      autoUpdateElements.forEach((element, index) => {
        const hasControls = element.querySelector('button, input, [role="button"], .pause, .stop') !== null;
        
        movingElements.push({
          type: 'auto_update',
          element: element.tagName.toLowerCase(),
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          hasControls,
          description: 'Auto-updating content element',
        });
      });

      // Check for carousel/slider elements
      const carouselElements = document.querySelectorAll(
        '.carousel, .slider, .slideshow, [data-carousel], [data-slider], .swiper'
      );
      carouselElements.forEach((element, index) => {
        const hasControls = element.querySelector(
          'button, input, [role="button"], .pause, .play, .stop, .prev, .next, .dots, .indicators'
        ) !== null;
        
        movingElements.push({
          type: 'carousel',
          element: element.tagName.toLowerCase(),
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          hasControls,
          description: 'Carousel or slider element',
        });
      });

      // Check for video/audio elements with autoplay
      const mediaElements = document.querySelectorAll('video[autoplay], audio[autoplay]');
      mediaElements.forEach((element, index) => {
        const hasControls = element.hasAttribute('controls') ||
                           element.querySelector('button, [role="button"]') !== null;
        
        movingElements.push({
          type: 'autoplay_media',
          element: element.tagName.toLowerCase(),
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          hasControls,
          description: `Auto-playing ${element.tagName.toLowerCase()} element`,
        });
      });

      // Check for JavaScript-based animations (common libraries)
      const scripts = document.querySelectorAll('script');
      let hasJSAnimation = false;
      scripts.forEach((script) => {
        const content = script.textContent || '';
        if (content.includes('setInterval') || content.includes('requestAnimationFrame') ||
            content.includes('jQuery.animate') || content.includes('gsap') ||
            content.includes('anime.js') || content.includes('velocity')) {
          hasJSAnimation = true;
        }
      });

      if (hasJSAnimation) {
        movingElements.push({
          type: 'javascript_animation',
          element: 'script',
          selector: 'script',
          hasControls: false,
          description: 'JavaScript-based animation detected',
        });
      }

      return {
        movingElements,
        totalElements: movingElements.length,
        elementsWithControls: movingElements.filter(el => el.hasControls).length,
      };
    });

    let score = 100;
    const elementCount = movingContentAnalysis.totalElements;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      const elementsWithoutControls = elementCount - movingContentAnalysis.elementsWithControls;
      
      if (elementsWithoutControls > 0) {
        score = Math.max(0, 100 - (elementsWithoutControls * 20)); // Deduct 20 points per element without controls
        issues.push(`${elementsWithoutControls} moving/auto-updating elements found without user controls`);
        
        movingContentAnalysis.movingElements
          .filter(el => !el.hasControls)
          .forEach((element) => {
            evidence.push({
              type: 'code',
              description: `Moving content without user controls: ${element.description}`,
              value: element.animationType ? 
                `Animation: ${element.animationType}` : 
                'Moving content detected',
              selector: element.selector,
              elementCount: 1,
              affectedSelectors: [element.selector],
              severity: 'error',
              fixExample: {
                before: this.getBeforeExample(element.type),
                after: this.getAfterExample(element.type),
                description: this.getFixDescription(element.type),
                codeExample: this.getCodeExample(element.type),
                resources: [
                  'https://www.w3.org/WAI/WCAG21/Understanding/pause-stop-hide.html',
                  'https://www.w3.org/WAI/WCAG21/Techniques/G4',
                  'https://www.w3.org/WAI/WCAG21/Techniques/G11'
                ]
              },
              metadata: {
                scanDuration,
                elementsAnalyzed: 1,
                checkSpecificData: {
                  contentType: element.type,
                  hasControls: element.hasControls,
                  animationType: element.animationType || 'unknown',
                  duration: element.duration || 0,
                },
              },
            });
          });
        
        recommendations.push('Provide pause, stop, or hide controls for moving content');
        recommendations.push('Allow users to control auto-updating content');
        recommendations.push('Ensure moving content can be paused on user request');
        recommendations.push('Consider reducing or eliminating unnecessary animations');
      }
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(type: string): string {
    switch (type) {
      case 'css_animation':
        return '<div class="animated-element">Moving content</div>';
      case 'marquee':
        return '<marquee>Scrolling text without controls</marquee>';
      case 'blinking':
        return '<span class="blink">Blinking text</span>';
      case 'auto_update':
        return '<div class="live-update">Auto-updating content</div>';
      case 'carousel':
        return '<div class="carousel">Automatic slideshow</div>';
      case 'autoplay_media':
        return '<video autoplay>Auto-playing video</video>';
      default:
        return 'Moving content without controls';
    }
  }

  private getAfterExample(type: string): string {
    switch (type) {
      case 'css_animation':
        return '<div class="animated-element">\n  Moving content\n  <button onclick="pauseAnimation()">Pause</button>\n</div>';
      case 'marquee':
        return '<div class="scrolling-text">\n  Scrolling text\n  <button onclick="pauseScroll()">Pause</button>\n</div>';
      case 'blinking':
        return '<span class="highlight">\n  Important text\n  <button onclick="stopBlink()">Stop Blinking</button>\n</span>';
      case 'auto_update':
        return '<div class="live-update">\n  Auto-updating content\n  <button onclick="pauseUpdates()">Pause Updates</button>\n</div>';
      case 'carousel':
        return '<div class="carousel">\n  Slideshow content\n  <button onclick="pauseSlideshow()">Pause</button>\n</div>';
      case 'autoplay_media':
        return '<video controls>Video with user controls</video>';
      default:
        return 'Moving content with user controls';
    }
  }

  private getFixDescription(type: string): string {
    switch (type) {
      case 'css_animation':
        return 'Add controls to pause or stop CSS animations';
      case 'marquee':
        return 'Replace marquee with static text or add pause controls';
      case 'blinking':
        return 'Remove blinking effect or add controls to stop it';
      case 'auto_update':
        return 'Provide controls to pause or stop auto-updates';
      case 'carousel':
        return 'Add pause/play controls to automatic carousels';
      case 'autoplay_media':
        return 'Add controls attribute or remove autoplay';
      default:
        return 'Provide user controls for moving content';
    }
  }

  private getCodeExample(type: string): string {
    switch (type) {
      case 'css_animation':
        return `
<!-- Before: No user control -->
<div class="animated-element">Moving content</div>
<style>
.animated-element { animation: slide 3s infinite; }
</style>

<!-- After: With user controls -->
<div class="animated-element" id="animatedContent">
  Moving content
  <button onclick="pauseAnimation()">Pause</button>
  <button onclick="resumeAnimation()">Resume</button>
</div>
<script>
function pauseAnimation() {
  document.getElementById('animatedContent').style.animationPlayState = 'paused';
}
function resumeAnimation() {
  document.getElementById('animatedContent').style.animationPlayState = 'running';
}
</script>
        `;
      case 'marquee':
        return `
<!-- Before: Marquee without controls -->
<marquee>Scrolling text without controls</marquee>

<!-- After: Controlled scrolling -->
<div class="scrolling-container">
  <div class="scrolling-text" id="scrollText">Scrolling text</div>
  <button onclick="pauseScroll()">Pause</button>
  <button onclick="resumeScroll()">Resume</button>
</div>
        `;
      case 'carousel':
        return `
<!-- Before: Auto-advancing carousel -->
<div class="carousel" data-auto-advance="true">
  <div class="slide">Slide 1</div>
  <div class="slide">Slide 2</div>
</div>

<!-- After: User-controlled carousel -->
<div class="carousel">
  <div class="slide">Slide 1</div>
  <div class="slide">Slide 2</div>
  <div class="controls">
    <button onclick="pauseCarousel()">Pause</button>
    <button onclick="resumeCarousel()">Resume</button>
    <button onclick="prevSlide()">Previous</button>
    <button onclick="nextSlide()">Next</button>
  </div>
</div>
        `;
      case 'autoplay_media':
        return `
<!-- Before: Auto-playing video -->
<video autoplay>
  <source src="video.mp4" type="video/mp4">
</video>

<!-- After: User-controlled video -->
<video controls>
  <source src="video.mp4" type="video/mp4">
</video>
        `;
      default:
        return 'Provide appropriate user controls for moving content';
    }
  }
}
