/**
 * WCAG Rule 1: Non-text Content - 1.1.1
 * 95% Automated - Minimal manual review for alt text accuracy
 */

import { Page } from 'puppeteer';
import {
  ManualReviewConfig,
  ManualReviewTemplate,
  ManualReviewItem,
} from '../utils/manual-review-template';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';

export interface NonTextContentConfig extends EnhancedCheckConfig {
  checkBackgroundImages?: boolean;
  analyzeSvgContent?: boolean;
  // Manual review options
  enableManualReview?: boolean;
  manualReviewThreshold?: number;
}

interface NonTextElementData {
  type: 'img' | 'svg' | 'canvas' | 'video' | 'audio' | 'object' | 'embed' | 'iframe';
  selector: string;
  src?: string;
  alt?: string;
  title?: string;
  ariaLabel?: string;
  ariaLabelledby?: string;
  ariaDescribedby?: string;
  role?: string;
  ariaHidden?: string;
  isDecorative: boolean;
  hasTextContent: boolean;
  context: string;
  isVisible: boolean;
}

export class NonTextContentCheck {
  private checkTemplate = new ManualReviewTemplate();
  private enhancedTemplate = new EnhancedCheckTemplate();

  /**
   * Perform non-text content check - 95% automated with enhanced utility integration
   */
  async performCheck(config: NonTextContentConfig) {
    // Use enhanced template with utility integration for better accuracy
    const enhancedConfig: EnhancedCheckConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enableSemanticValidation: true,
        enablePatternValidation: true,
        enableContentQualityAnalysis: true,
        enableFrameworkOptimization: true,
        enableComponentLibraryDetection: true,
        integrationStrategy: 'enhance',
      },
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-001',
      'Non-text Content',
      'perceivable',
      0.08,
      'A',
      enhancedConfig,
      this.executeNonTextContentCheck.bind(this),
      true, // requires browser
      config.enableManualReview || false
    );

    // Enhanced evidence standardization with image analysis specifics
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-001',
        ruleName: 'Non-text Content',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.95,
          checkType: 'image-alternative-analysis',
          manualReviewRequired: result.manualReviewItems?.length > 0,
          imageAnalysis: true,
          altTextValidation: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8, // High threshold for alt text accuracy
        maxEvidenceItems: 60, // More items for comprehensive image analysis
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  /**
   * Execute comprehensive non-text content analysis
   */
  private async executeNonTextContentCheck(page: Page, _config: NonTextContentConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Get all images and non-text content
    const nonTextElements = await page.evaluate(() => {
      const elements: Array<{
        type: 'img' | 'svg' | 'canvas' | 'object' | 'embed' | 'iframe' | 'video' | 'audio';
        selector: string;
        src?: string;
        alt?: string;
        title?: string;
        ariaLabel?: string;
        ariaLabelledby?: string;
        ariaDescribedby?: string;
        role?: string;
        isDecorative: boolean;
        hasTextContent: boolean;
        context: string;
        isVisible: boolean;
      }> = [];

      function generateSelector(element: Element, tagName: string, index: number): string {
        if (element.id) {
          return `#${element.id}`;
        }

        if (element.className) {
          const classes = element.className
            .toString()
            .split(' ')
            .filter((c) => c.length > 0);
          if (classes.length > 0) {
            return `${tagName}.${classes.join('.')}`;
          }
        }

        return `${tagName}:nth-of-type(${index + 1})`;
      }

      function isDecorativeImage(img: HTMLImageElement): boolean {
        // Check for decorative indicators
        if (
          img.getAttribute('role') === 'presentation' ||
          img.getAttribute('aria-hidden') === 'true' ||
          img.alt === ''
        ) {
          return true;
        }

        // Check filename patterns that suggest decorative use
        const src = img.src.toLowerCase();
        const decorativePatterns = [
          /spacer/i,
          /separator/i,
          /divider/i,
          /decoration/i,
          /ornament/i,
          /border/i,
          /background/i,
        ];

        return decorativePatterns.some((pattern) => pattern.test(src));
      }

      function getImageContext(img: HTMLImageElement): string {
        const parent = img.parentElement;
        if (!parent) return '';

        const context: string[] = [];

        // Check if in a figure with caption
        if (parent.tagName === 'FIGURE') {
          const caption = parent.querySelector('figcaption');
          if (caption) context.push('has caption');
        }

        // Check if in a link
        if (parent.tagName === 'A') {
          context.push('linked image');
        }

        // Check surrounding text
        const surroundingText = parent.textContent?.trim() || '';
        if (surroundingText.length > 50) {
          context.push('has surrounding text');
        }

        return context.join(', ');
      }

      // Analyze images
      const images = document.querySelectorAll('img');
      images.forEach((img, index) => {
        const computedStyle = window.getComputedStyle(img);
        const isVisible =
          computedStyle.display !== 'none' &&
          computedStyle.visibility !== 'hidden' &&
          computedStyle.opacity !== '0';

        if (isVisible) {
          elements.push({
            type: 'img',
            selector: generateSelector(img, 'img', index),
            src: img.src,
            alt: img.alt,
            title: img.title,
            ariaLabel: img.getAttribute('aria-label') || undefined,
            ariaLabelledby: img.getAttribute('aria-labelledby') || undefined,
            ariaDescribedby: img.getAttribute('aria-describedby') || undefined,
            role: img.getAttribute('role') || undefined,
            isDecorative: isDecorativeImage(img),
            hasTextContent: false,
            context: getImageContext(img),
            isVisible,
          });
        }
      });

      // Analyze SVGs
      const svgs = document.querySelectorAll('svg');
      svgs.forEach((svg, index) => {
        const computedStyle = window.getComputedStyle(svg);
        const isVisible =
          computedStyle.display !== 'none' &&
          computedStyle.visibility !== 'hidden' &&
          computedStyle.opacity !== '0';

        if (isVisible) {
          const hasTitle = svg.querySelector('title') !== null;
          const hasDesc = svg.querySelector('desc') !== null;

          elements.push({
            type: 'svg',
            selector: generateSelector(svg, 'svg', index),
            alt: hasTitle ? svg.querySelector('title')?.textContent || '' : '',
            ariaLabel: svg.getAttribute('aria-label') || undefined,
            ariaLabelledby: svg.getAttribute('aria-labelledby') || undefined,
            role: svg.getAttribute('role') || undefined,
            isDecorative:
              svg.getAttribute('aria-hidden') === 'true' ||
              svg.getAttribute('role') === 'presentation',
            hasTextContent: hasTitle || hasDesc,
            context: hasTitle ? 'has title' : hasDesc ? 'has description' : '',
            isVisible,
          });
        }
      });

      return elements;
    });

    let totalElements = 0;
    let passedElements = 0;
    let manualReviewCount = 0;

    // Analyze each non-text element
    for (const element of nonTextElements) {
      totalElements++;

      const analysis = this.analyzeNonTextElement(element);

      if (analysis.status === 'passed') {
        passedElements++;

        evidence.push({
          type: 'text',
          description: 'Non-text content has appropriate alternative',
          value: analysis.reason,
          selector: element.selector,
          severity: 'info',
        });
      } else if (analysis.status === 'failed') {
        issues.push(`Missing or inadequate alternative text for ${element.selector}`);

        evidence.push({
          type: 'text',
          description: 'Non-text content lacks appropriate alternative',
          value: analysis.reason,
          selector: element.selector,
          severity: 'error',
        });

        recommendations.push(`${element.selector}: ${analysis.recommendation}`);
      } else if (analysis.status === 'manual_review') {
        manualReviewCount++;

        manualReviewItems.push({
          selector: element.selector,
          description: 'Alt text accuracy verification needed',
          automatedFindings: analysis.reason,
          reviewRequired:
            'Verify that alternative text accurately describes the image content and purpose',
          priority: element.context.includes('important') ? 'high' : 'medium',
          estimatedTime: 2,
        });

        evidence.push({
          type: 'text',
          description: 'Non-text content requires manual review',
          value: analysis.reason,
          selector: element.selector,
          severity: 'warning',
        });
      }
    }

    // Calculate automated score (excluding manual review items)
    const automatedElements = totalElements - manualReviewCount;
    const automatedScore =
      automatedElements > 0 ? Math.round((passedElements / automatedElements) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Non-text content analysis summary',
      value: `${passedElements}/${automatedElements} elements pass automated checks, ${manualReviewCount} require manual review`,
      severity: automatedScore >= 90 ? 'info' : automatedScore >= 70 ? 'warning' : 'error',
    });

    if (automatedScore < 100) {
      recommendations.unshift('Add appropriate alternative text for all non-text content');
      recommendations.push('Use empty alt="" for decorative images');
      recommendations.push('Provide descriptive alternatives that convey the same information');
    }

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 0.95,
    };
  }

  /**
   * Analyze individual non-text element
   */
  private analyzeNonTextElement(element: NonTextElementData): {
    status: 'passed' | 'failed' | 'manual_review';
    reason: string;
    recommendation?: string;
  } {
    // Check if decorative
    if (element.isDecorative) {
      if (element.alt === '' || element.role === 'presentation' || element.ariaHidden === 'true') {
        return {
          status: 'passed',
          reason: 'Decorative element properly marked with empty alt or aria-hidden',
        };
      } else {
        return {
          status: 'failed',
          reason: 'Decorative element should have empty alt="" or aria-hidden="true"',
          recommendation: 'Add alt="" for decorative images',
        };
      }
    }

    // Check for any alternative text
    const hasAlt = element.alt && element.alt.trim().length > 0;
    const hasAriaLabel = element.ariaLabel && element.ariaLabel.trim().length > 0;
    const hasAriaLabelledby = element.ariaLabelledby;
    const hasTitle = element.title && element.title.trim().length > 0;
    const hasTextContent = element.hasTextContent;

    const hasAlternative =
      hasAlt || hasAriaLabel || hasAriaLabelledby || hasTitle || hasTextContent;

    if (!hasAlternative) {
      return {
        status: 'failed',
        reason: 'No alternative text provided',
        recommendation: 'Add descriptive alt text, aria-label, or other text alternative',
      };
    }

    // Check for placeholder alt text (automated detection)
    const altText = element.alt || element.ariaLabel || element.title || '';
    const placeholderPatterns = [
      /^image$/i,
      /^picture$/i,
      /^photo$/i,
      /^img\d*$/i,
      /^untitled/i,
      /^dsc\d+/i,
      /^\d+\.(jpg|jpeg|png|gif|svg)$/i,
      /^[a-f0-9]{8,}$/i, // Hash-like strings
    ];

    const isPlaceholder = placeholderPatterns.some((pattern) => pattern.test(altText.trim()));

    if (isPlaceholder) {
      return {
        status: 'failed',
        reason: `Placeholder alt text detected: "${altText}"`,
        recommendation: 'Replace with descriptive alternative text',
      };
    }

    // Check alt text length (very short might be inadequate)
    if (altText.trim().length < 3) {
      return {
        status: 'failed',
        reason: 'Alt text too short to be descriptive',
        recommendation: 'Provide more descriptive alternative text',
      };
    }

    // If alt text exists and passes basic checks, require manual review for accuracy
    if (altText.trim().length > 0) {
      return {
        status: 'manual_review',
        reason: `Alt text present: "${altText}" - accuracy verification needed`,
      };
    }

    return {
      status: 'passed',
      reason: 'Alternative text provided and passes automated checks',
    };
  }
}
