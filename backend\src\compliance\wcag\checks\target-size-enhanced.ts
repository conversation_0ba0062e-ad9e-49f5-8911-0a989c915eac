/**
 * WCAG-058: Target Size Enhanced Check (2.5.5 Level AAA)
 * 70% Automated - Enhanced target size requirements for AAA compliance
 */

import { Page } from 'puppeteer';
import { CheckConfig, CheckTemplate } from '../utils/check-template';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced, WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

export class TargetSizeEnhancedCheck {
  private checkTemplate = new CheckTemplate();

  // Enhanced target size requirements for AAA level
  private readonly ENHANCED_MIN_SIZE = 44; // 44x44 CSS pixels for AAA
  private readonly STANDARD_MIN_SIZE = 24; // 24x24 CSS pixels for AA

  async performCheck(config: CheckConfig): Promise<WcagCheckResultEnhanced> {
    const result = await this.checkTemplate.executeCheck(
      'WCAG-058',
      'Target Size Enhanced',
      'operable',
      0.0305,
      'AAA',
      config,
      this.executeTargetSizeEnhancedCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with enhanced target size analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-055',
        ruleName: 'Target Size Enhanced',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'enhanced-target-size-analysis',
          enhancedSizeRequirements: true,
          aaaLevelValidation: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.9,
        maxEvidenceItems: 40,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeTargetSizeEnhancedCheck(
    page: Page,
    config: CheckConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze target sizes with enhanced requirements
    const targetAnalysis = await page.evaluate((enhancedMinSize: number, standardMinSize: number) => {
      const interactiveElements = document.querySelectorAll(
        'button, a[href], input, textarea, select, [role="button"], [role="link"], [role="menuitem"], [role="tab"], [tabindex]:not([tabindex="-1"])'
      );

      const targetIssues: Array<{
        selector: string;
        tagName: string;
        width: number;
        height: number;
        area: number;
        meetsStandard: boolean;
        meetsEnhanced: boolean;
        isVisible: boolean;
        hasText: boolean;
        textContent: string;
        computedStyle: {
          padding: string;
          margin: string;
          border: string;
        };
      }> = [];

      interactiveElements.forEach((element, index) => {
        const rect = element.getBoundingClientRect();
        const computedStyle = window.getComputedStyle(element);
        
        // Check if element is visible
        const isVisible = rect.width > 0 && rect.height > 0 && 
                         computedStyle.visibility !== 'hidden' && 
                         computedStyle.display !== 'none';

        if (!isVisible) return;

        const width = Math.round(rect.width);
        const height = Math.round(rect.height);
        const area = width * height;
        const meetsStandard = width >= standardMinSize && height >= standardMinSize;
        const meetsEnhanced = width >= enhancedMinSize && height >= enhancedMinSize;

        // Only report elements that don't meet enhanced requirements
        if (!meetsEnhanced) {
          const textContent = element.textContent?.trim() || '';
          
          targetIssues.push({
            selector: generateSelector(element, index),
            tagName: element.tagName.toLowerCase(),
            width,
            height,
            area,
            meetsStandard,
            meetsEnhanced,
            isVisible,
            hasText: textContent.length > 0,
            textContent: textContent.substring(0, 50),
            computedStyle: {
              padding: computedStyle.padding,
              margin: computedStyle.margin,
              border: computedStyle.border,
            },
          });
        }
      });

      return {
        totalInteractiveElements: interactiveElements.length,
        targetIssues,
        enhancedMinSize,
        standardMinSize,
      };

      function generateSelector(element: Element, index: number): string {
        if (element.id) return `#${element.id}`;
        if (element.className) {
          const classes = element.className.split(' ').filter(c => c.trim());
          if (classes.length > 0) return `.${classes[0]}`;
        }
        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }
    }, this.ENHANCED_MIN_SIZE, this.STANDARD_MIN_SIZE);

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const elementCount = targetAnalysis.targetIssues.length;

    if (elementCount > 0) {
      // Calculate score based on severity of issues
      const criticalIssues = targetAnalysis.targetIssues.filter(issue => !issue.meetsStandard);
      const enhancedIssues = targetAnalysis.targetIssues.filter(issue => issue.meetsStandard && !issue.meetsEnhanced);

      // Critical penalty for elements that don't meet standard AA requirements
      if (criticalIssues.length > 0) {
        score -= Math.min(60, criticalIssues.length * 15);
        issues.push(`${criticalIssues.length} targets below standard 24x24px minimum`);
      }

      // Moderate penalty for elements that don't meet enhanced AAA requirements
      if (enhancedIssues.length > 0) {
        score -= Math.min(40, enhancedIssues.length * 8);
        issues.push(`${enhancedIssues.length} targets below enhanced 44x44px minimum`);
      }

      evidence.push({
        type: 'measurement',
        description: 'Interactive targets below enhanced size requirements',
        value: `Found ${elementCount} targets that don't meet AAA enhanced size requirements (44x44px)`,
        elementCount,
        affectedSelectors: targetAnalysis.targetIssues.map(issue => issue.selector),
        severity: 'error',
        fixExample: {
          before: 'button { padding: 4px 8px; }',
          after: 'button { padding: 12px 16px; min-width: 44px; min-height: 44px; }',
          description: 'Increase target size to meet enhanced AAA requirements',
          codeExample: `
/* Before: Small target */
.button {
  padding: 4px 8px;
  font-size: 12px;
}

/* After: Enhanced AAA target size */
.button {
  padding: 12px 16px;
  min-width: 44px;
  min-height: 44px;
  font-size: 14px;
  /* Ensure adequate spacing between targets */
  margin: 4px;
}

/* Alternative: Use touch-friendly spacing */
.touch-target {
  min-width: 44px;
  min-height: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
          `,
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/target-size.html',
            'https://developer.apple.com/design/human-interface-guidelines/ios/visual-design/adaptivity-and-layout/',
            'https://material.io/design/usability/accessibility.html#layout-and-typography'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: targetAnalysis.totalInteractiveElements,
          checkSpecificData: {
            enhancedMinSize: targetAnalysis.enhancedMinSize,
            standardMinSize: targetAnalysis.standardMinSize,
            criticalIssues: criticalIssues.length,
            enhancedIssues: enhancedIssues.length,
            totalTargets: targetAnalysis.totalInteractiveElements,
          },
        },
      });

      // Add specific examples for problematic targets
      targetAnalysis.targetIssues.slice(0, 10).forEach(issue => {
        const severity = !issue.meetsStandard ? 'critical' : 'error';
        const sizeDescription = !issue.meetsStandard 
          ? `${issue.width}x${issue.height}px (below 24x24px minimum)`
          : `${issue.width}x${issue.height}px (below 44x44px enhanced)`;

        evidence.push({
          type: 'measurement',
          description: `Small target: ${sizeDescription}`,
          value: `<${issue.tagName}>${issue.textContent || '[no text]'}</${issue.tagName}>`,
          selector: issue.selector,
          severity: severity as 'critical' | 'error',
          metadata: {
            checkSpecificData: {
              currentSize: `${issue.width}x${issue.height}`,
              area: issue.area,
              meetsStandard: issue.meetsStandard,
              meetsEnhanced: issue.meetsEnhanced,
              hasText: issue.hasText,
            },
          },
        });
      });

      recommendations.push('Increase target sizes to at least 44x44 CSS pixels for AAA compliance');
      recommendations.push('Ensure adequate spacing between interactive targets');
      recommendations.push('Consider touch-friendly design patterns for mobile devices');
      recommendations.push('Test target sizes with actual users, especially those with motor impairments');
      
      if (criticalIssues.length > 0) {
        recommendations.push('CRITICAL: Some targets are below the 24x24px AA minimum requirement');
      }
    } else {
      // All targets meet enhanced requirements
      evidence.push({
        type: 'info',
        description: 'All interactive targets meet enhanced size requirements',
        value: `All ${targetAnalysis.totalInteractiveElements} interactive targets meet AAA enhanced size requirements (44x44px)`,
        elementCount: targetAnalysis.totalInteractiveElements,
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: targetAnalysis.totalInteractiveElements,
          checkSpecificData: {
            enhancedMinSize: targetAnalysis.enhancedMinSize,
            allTargetsMeetEnhanced: true,
          },
        },
      });
    }

    // Ensure score doesn't go below 0
    score = Math.max(0, score);

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
