/**
 * Enhanced WCAG Scan Results Component
 * Displays scan results with enhanced evidence, performance metrics, and fix examples
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Clock, 
  Target, 
  Zap, 
  TrendingUp,
  BarChart3,
  FileText,
  Settings
} from 'lucide-react';
import { 
  WcagScanResultEnhanced, 
  WcagCheckEnhanced, 
  WcagEnhancedSummary,
  WcagPerformanceMetrics 
} from '@/types/wcag';
import { EnhancedEvidenceDisplay } from './EnhancedEvidenceDisplay';

interface EnhancedScanResultsProps {
  scanResult: WcagScanResultEnhanced;
  showEnhancedFeatures?: boolean;
}

const StatusIcon = ({ status }: { status: string }) => {
  switch (status) {
    case 'passed':
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    case 'failed':
      return <XCircle className="h-5 w-5 text-red-500" />;
    case 'not_applicable':
      return <AlertTriangle className="h-5 w-5 text-gray-500" />;
    default:
      return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
  }
};

const ScoreDisplay = ({ score, maxScore }: { score: number; maxScore: number }) => {
  const percentage = maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
  const variant = percentage >= 80 ? 'default' : percentage >= 60 ? 'secondary' : 'destructive';
  
  return (
    <div className="flex items-center gap-2">
      <Progress value={percentage} className="w-20" />
      <Badge variant={variant}>{percentage}%</Badge>
      <span className="text-sm text-gray-600">({score}/{maxScore})</span>
    </div>
  );
};

const EnhancedSummaryDisplay = ({ summary }: { summary: WcagEnhancedSummary }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Enhanced Performance Summary
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{summary.totalElementsScanned}</div>
            <div className="text-sm text-gray-600">Elements Scanned</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{summary.totalFailedElements}</div>
            <div className="text-sm text-gray-600">Failed Elements</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{summary.averageScanTime.toFixed(1)}ms</div>
            <div className="text-sm text-gray-600">Avg Scan Time</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{Math.round(summary.cacheHitRate * 100)}%</div>
            <div className="text-sm text-gray-600">Cache Hit Rate</div>
          </div>
        </div>
        
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium mb-2 flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Performance Metrics
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Total Duration:</span>
                <span className="font-medium">{summary.performanceMetrics.totalScanDuration}ms</span>
              </div>
              <div className="flex justify-between">
                <span>Average Check:</span>
                <span className="font-medium">{summary.performanceMetrics.averageCheckDuration.toFixed(1)}ms</span>
              </div>
              <div className="flex justify-between">
                <span>Slowest Check:</span>
                <span className="font-medium text-red-600">{summary.performanceMetrics.slowestCheck}</span>
              </div>
              <div className="flex justify-between">
                <span>Fastest Check:</span>
                <span className="font-medium text-green-600">{summary.performanceMetrics.fastestCheck}</span>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium mb-2 flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Success Rate Analysis
            </h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm">Element Success Rate:</span>
                <div className="flex items-center gap-2">
                  <Progress 
                    value={summary.totalElementsScanned > 0 
                      ? ((summary.totalElementsScanned - summary.totalFailedElements) / summary.totalElementsScanned) * 100 
                      : 0
                    } 
                    className="w-16" 
                  />
                  <span className="text-sm font-medium">
                    {summary.totalElementsScanned > 0 
                      ? Math.round(((summary.totalElementsScanned - summary.totalFailedElements) / summary.totalElementsScanned) * 100)
                      : 0
                    }%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const CheckResultCard = ({ check, showEnhanced = true }: { check: WcagCheckEnhanced; showEnhanced?: boolean }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="mb-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <StatusIcon status={check.status} />
            <div>
              <h3 className="font-medium">{check.ruleName}</h3>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline">{check.ruleId}</Badge>
                <Badge variant="secondary">{check.level}</Badge>
                <Badge variant="outline">{check.category}</Badge>
              </div>
            </div>
          </div>
          <div className="text-right">
            <ScoreDisplay score={check.score} maxScore={check.maxScore} />
            {showEnhanced && check.elementCounts && (
              <div className="mt-2 text-sm text-gray-600">
                {check.elementCounts.total} elements • {check.elementCounts.failed} failed
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      
      {check.evidence && check.evidence.length > 0 && (
        <CardContent>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="mb-4"
          >
            {isExpanded ? 'Hide' : 'Show'} Evidence ({check.evidence.length})
          </Button>
          
          {isExpanded && (
            <div className="space-y-4">
              <EnhancedEvidenceDisplay 
                evidence={check.evidence}
                showFixExamples={showEnhanced}
                showElementCounts={showEnhanced}
                showPerformanceMetrics={showEnhanced}
              />
              
              {showEnhanced && check.performance && (
                <Alert>
                  <Clock className="h-4 w-4" />
                  <AlertDescription>
                    <div className="flex items-center gap-4 text-sm">
                      <span>Scan Duration: {check.performance.scanDuration}ms</span>
                      <span>Elements Analyzed: {check.performance.elementsAnalyzed}</span>
                      {check.performance.cacheHitRate && (
                        <span>Cache Hit Rate: {Math.round(check.performance.cacheHitRate * 100)}%</span>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
              
              {showEnhanced && check.checkMetadata && (
                <Alert>
                  <Settings className="h-4 w-4" />
                  <AlertDescription>
                    <div className="text-sm">
                      <strong>Algorithm:</strong> {check.checkMetadata.algorithm} v{check.checkMetadata.version} 
                      <span className="ml-2">
                        <strong>Confidence:</strong> {Math.round(check.checkMetadata.confidence * 100)}%
                      </span>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};

export const EnhancedScanResults: React.FC<EnhancedScanResultsProps> = ({
  scanResult,
  showEnhancedFeatures = true,
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  
  const passedChecks = scanResult.checks.filter(check => check.status === 'passed');
  const failedChecks = scanResult.checks.filter(check => check.status === 'failed');
  const notApplicableChecks = scanResult.checks.filter(check => check.status === 'not_applicable');

  return (
    <div className="space-y-6">
      {/* Enhanced Summary */}
      {showEnhancedFeatures && scanResult.enhancedSummary && (
        <EnhancedSummaryDisplay summary={scanResult.enhancedSummary} />
      )}

      {/* Main Results */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="failed" className="text-red-600">
            Failed ({failedChecks.length})
          </TabsTrigger>
          <TabsTrigger value="passed" className="text-green-600">
            Passed ({passedChecks.length})
          </TabsTrigger>
          <TabsTrigger value="na" className="text-gray-600">
            N/A ({notApplicableChecks.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  Passed Checks
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{passedChecks.length}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <XCircle className="h-4 w-4 text-red-500" />
                  Failed Checks
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{failedChecks.length}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Target className="h-4 w-4 text-blue-500" />
                  Overall Score
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{scanResult.overallScore}%</div>
              </CardContent>
            </Card>
          </div>
          
          {failedChecks.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-4 text-red-600">Priority Issues</h3>
              {failedChecks.slice(0, 3).map((check, index) => (
                <CheckResultCard key={index} check={check} showEnhanced={showEnhancedFeatures} />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="failed" className="space-y-4">
          {failedChecks.map((check, index) => (
            <CheckResultCard key={index} check={check} showEnhanced={showEnhancedFeatures} />
          ))}
        </TabsContent>

        <TabsContent value="passed" className="space-y-4">
          {passedChecks.map((check, index) => (
            <CheckResultCard key={index} check={check} showEnhanced={showEnhancedFeatures} />
          ))}
        </TabsContent>

        <TabsContent value="na" className="space-y-4">
          {notApplicableChecks.map((check, index) => (
            <CheckResultCard key={index} check={check} showEnhanced={showEnhancedFeatures} />
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EnhancedScanResults;
