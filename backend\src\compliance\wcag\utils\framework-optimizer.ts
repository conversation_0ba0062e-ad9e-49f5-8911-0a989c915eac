/**
 * Framework-Specific Optimizer for WCAG Scanning
 * Specialized optimizations for React, Vue, Angular, Svelte, and other modern frameworks
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

export interface FrameworkDetection {
  framework: string;
  version?: string;
  confidence: number; // 0-1
  libraries: string[];
  buildTool?: string;
  stateManagement?: string[];
  routingLibrary?: string;
  uiLibrary?: string[];
}

export interface FrameworkOptimization {
  framework: string;
  optimizations: string[];
  accessibilityPatterns: string[];
  commonIssues: string[];
  recommendations: string[];
  testingStrategies: string[];
  toolingRecommendations: string[];
}

export interface FrameworkAnalysisResult {
  detectedFrameworks: FrameworkDetection[];
  primaryFramework: FrameworkDetection;
  optimizations: FrameworkOptimization[];
  overallScore: number;
  frameworkSpecificIssues: string[];
  enhancedRecommendations: string[];
}

export interface FrameworkOptimizationConfig {
  enableReactOptimization: boolean;
  enableVueOptimization: boolean;
  enableAngularOptimization: boolean;
  enableSvelteOptimization: boolean;
  enableNextJSOptimization: boolean;
  enableNuxtOptimization: boolean;
  enableGatsbyOptimization: boolean;
  enableCustomFrameworkDetection: boolean;
  analyzeStateManagement: boolean;
  analyzeRouting: boolean;
  analyzeUILibraries: boolean;
  deepAnalysis: boolean;
}

/**
 * Advanced framework-specific accessibility optimizer
 */
export class FrameworkOptimizer {
  private static instance: FrameworkOptimizer;
  private frameworkPatterns: Map<string, any> = new Map();

  private constructor() {
    this.initializeFrameworkPatterns();
  }

  static getInstance(): FrameworkOptimizer {
    if (!FrameworkOptimizer.instance) {
      FrameworkOptimizer.instance = new FrameworkOptimizer();
    }
    return FrameworkOptimizer.instance;
  }

  /**
   * Analyze framework-specific accessibility optimizations
   */
  async analyzeFrameworks(
    page: Page,
    config: Partial<FrameworkOptimizationConfig> = {},
  ): Promise<FrameworkAnalysisResult> {
    const fullConfig: FrameworkOptimizationConfig = {
      enableReactOptimization: config.enableReactOptimization ?? true,
      enableVueOptimization: config.enableVueOptimization ?? true,
      enableAngularOptimization: config.enableAngularOptimization ?? true,
      enableSvelteOptimization: config.enableSvelteOptimization ?? true,
      enableNextJSOptimization: config.enableNextJSOptimization ?? true,
      enableNuxtOptimization: config.enableNuxtOptimization ?? true,
      enableGatsbyOptimization: config.enableGatsbyOptimization ?? true,
      enableCustomFrameworkDetection: config.enableCustomFrameworkDetection ?? true,
      analyzeStateManagement: config.analyzeStateManagement ?? true,
      analyzeRouting: config.analyzeRouting ?? true,
      analyzeUILibraries: config.analyzeUILibraries ?? true,
      deepAnalysis: config.deepAnalysis ?? true,
    };

    logger.debug('⚛️ Starting framework-specific accessibility analysis');

    // Inject framework detection functions
    await this.injectFrameworkDetectionFunctions(page);

    // Detect frameworks
    const detectedFrameworks = await this.detectFrameworks(page, fullConfig);

    // Determine primary framework
    const primaryFramework = detectedFrameworks.reduce(
      (prev, current) => (current.confidence > prev.confidence ? current : prev),
      detectedFrameworks[0] || { framework: 'vanilla', confidence: 0, libraries: [] },
    );

    // Generate optimizations
    const optimizations = await this.generateFrameworkOptimizations(
      page,
      detectedFrameworks,
      fullConfig,
    );

    // Analyze framework-specific issues
    const frameworkSpecificIssues = await this.analyzeFrameworkSpecificIssues(
      page,
      primaryFramework,
    );

    // Calculate overall score and recommendations
    const result = this.calculateOverallResults(
      detectedFrameworks,
      primaryFramework,
      optimizations,
      frameworkSpecificIssues,
    );

    logger.info(
      `✅ Framework analysis completed: ${primaryFramework.framework} (confidence: ${Math.round(primaryFramework.confidence * 100)}%)`,
      {
        frameworks: detectedFrameworks.length,
        optimizations: optimizations.length,
        issues: frameworkSpecificIssues.length,
      },
    );

    return result;
  }

  /**
   * Initialize framework patterns
   */
  private initializeFrameworkPatterns(): void {
    // React patterns
    this.frameworkPatterns.set('react', {
      detectionPatterns: [
        'window.React',
        'window.__REACT_DEVTOOLS_GLOBAL_HOOK__',
        '[data-reactroot]',
        '[data-react-helmet]',
        'script[src*="react"]',
      ],
      accessibilityPatterns: [
        'Use semantic HTML elements',
        'Implement proper ARIA attributes',
        'Manage focus with useRef and useEffect',
        'Use React.Fragment to avoid wrapper divs',
        'Implement proper form validation',
      ],
      commonIssues: [
        'Missing key props in lists',
        'Improper focus management in modals',
        'Missing ARIA labels on custom components',
        'Inaccessible custom form controls',
        'Poor keyboard navigation in SPAs',
      ],
      libraries: ['react-router', 'redux', 'mobx', 'material-ui', 'ant-design', 'chakra-ui'],
      testingTools: ['@testing-library/react', 'jest-axe', 'react-axe'],
    });

    // Vue patterns
    this.frameworkPatterns.set('vue', {
      detectionPatterns: [
        'window.Vue',
        '[data-v-]',
        '.v-application',
        'script[src*="vue"]',
        '#app[data-v-app]',
      ],
      accessibilityPatterns: [
        'Use v-bind for dynamic ARIA attributes',
        'Implement proper focus management with $refs',
        'Use Vue transitions for accessible animations',
        'Leverage Vue directives for accessibility',
      ],
      commonIssues: [
        'Missing ARIA attributes in custom components',
        'Improper focus management in route changes',
        'Inaccessible custom form validation',
        'Poor keyboard navigation',
      ],
      libraries: ['vue-router', 'vuex', 'pinia', 'vuetify', 'quasar', 'element-ui'],
      testingTools: ['@vue/test-utils', 'jest-axe'],
    });

    // Angular patterns
    this.frameworkPatterns.set('angular', {
      detectionPatterns: [
        'window.ng',
        'window.angular',
        '[ng-app]',
        '[ng-controller]',
        'app-root',
        'script[src*="angular"]',
      ],
      accessibilityPatterns: [
        'Use Angular CDK a11y module',
        'Implement proper focus trapping',
        'Use Angular Material accessibility features',
        'Leverage Angular directives for ARIA',
      ],
      commonIssues: [
        'Missing focus management in route changes',
        'Inaccessible custom components',
        'Poor form accessibility',
        'Missing live regions for dynamic content',
      ],
      libraries: ['@angular/router', '@angular/material', 'ngx-bootstrap', 'primeng'],
      testingTools: ['@angular/testing', 'jest-axe', 'protractor'],
    });

    // Add more framework patterns...
    this.addAdditionalFrameworkPatterns();
  }

  /**
   * Add additional framework patterns
   */
  private addAdditionalFrameworkPatterns(): void {
    // Svelte patterns
    this.frameworkPatterns.set('svelte', {
      detectionPatterns: ['[class*="svelte-"]', 'script[src*="svelte"]', 'window.__SVELTE__'],
      accessibilityPatterns: [
        'Use Svelte accessibility warnings',
        'Implement proper focus management',
        'Use Svelte stores for state management',
      ],
      commonIssues: [
        'Missing ARIA attributes',
        'Poor keyboard navigation',
        'Inaccessible animations',
      ],
      libraries: ['svelte-routing', 'sapper', 'sveltekit'],
      testingTools: ['@testing-library/svelte', 'jest'],
    });

    // Next.js patterns
    this.frameworkPatterns.set('nextjs', {
      detectionPatterns: ['window.__NEXT_DATA__', '#__next', 'script[src*="_next"]'],
      accessibilityPatterns: [
        'Use Next.js Image component for optimized images',
        'Implement proper meta tags for SEO and accessibility',
        'Use Next.js routing for accessible navigation',
      ],
      commonIssues: [
        'Missing alt text on Next.js Image components',
        'Poor focus management in client-side routing',
        'Missing page titles and meta descriptions',
      ],
      libraries: ['next-router', 'next-auth'],
      testingTools: ['@testing-library/react', 'jest-axe'],
    });

    // Nuxt.js patterns
    this.frameworkPatterns.set('nuxtjs', {
      detectionPatterns: ['window.__NUXT__', '#__nuxt', 'script[src*="_nuxt"]'],
      accessibilityPatterns: [
        'Use Nuxt.js head() for proper meta tags',
        'Implement proper page transitions',
        'Use Nuxt.js modules for accessibility',
      ],
      commonIssues: [
        'Missing page titles in SPA mode',
        'Poor focus management in transitions',
        'Inaccessible loading states',
      ],
      libraries: ['@nuxtjs/router', '@nuxtjs/auth'],
      testingTools: ['@vue/test-utils', 'jest-axe'],
    });
  }

  /**
   * Inject framework detection functions
   */
  private async injectFrameworkDetectionFunctions(page: Page): Promise<void> {
    await page.evaluateOnNewDocument(() => {
      (window as any).wcagFrameworkDetection = {
        /**
         * Detect all frameworks
         */
        detectAllFrameworks() {
          const frameworks = [
            this.detectReact(),
            this.detectVue(),
            this.detectAngular(),
            this.detectSvelte(),
            this.detectNextJS(),
            this.detectNuxtJS(),
            this.detectGatsby(),
          ].filter((framework) => framework.confidence > 0);

          return frameworks;
        },

        /**
         * Detect React
         */
        detectReact() {
          let confidence = 0;
          const libraries: string[] = [];
          let version = null;

          // Check for React global
          if ((window as any).React) {
            confidence += 0.4;
            version = (window as any).React.version;
          }

          // Check for React DevTools
          if ((window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__) {
            confidence += 0.2;
          }

          // Check for React DOM elements
          if (
            document.querySelector('[data-reactroot]') ||
            document.querySelector('[data-react-helmet]')
          ) {
            confidence += 0.3;
          }

          // Check for React scripts
          if (document.querySelector('script[src*="react"]')) {
            confidence += 0.1;
          }

          // Detect React libraries
          if ((window as any).ReactRouter) libraries.push('react-router');
          if ((window as any).__REDUX_DEVTOOLS_EXTENSION__) libraries.push('redux');
          if (document.querySelector('[class*="MuiBox"]')) libraries.push('material-ui');
          if (document.querySelector('[class*="ant-"]')) libraries.push('ant-design');

          return {
            framework: 'react',
            confidence: Math.min(confidence, 1),
            version,
            libraries,
          };
        },

        /**
         * Detect Vue
         */
        detectVue() {
          let confidence = 0;
          const libraries: string[] = [];
          let version = null;

          // Check for Vue global
          if ((window as any).Vue) {
            confidence += 0.4;
            version = (window as any).Vue.version;
          }

          // Check for Vue-specific attributes
          if (document.querySelector('[data-v-]')) {
            confidence += 0.3;
          }

          // Check for Vuetify
          if (document.querySelector('.v-application')) {
            confidence += 0.2;
            libraries.push('vuetify');
          }

          // Check for Vue scripts
          if (document.querySelector('script[src*="vue"]')) {
            confidence += 0.1;
          }

          // Detect Vue libraries
          if ((window as any).$router) libraries.push('vue-router');
          if ((window as any).$store) libraries.push('vuex');

          return {
            framework: 'vue',
            confidence: Math.min(confidence, 1),
            version,
            libraries,
          };
        },

        /**
         * Detect Angular
         */
        detectAngular() {
          let confidence = 0;
          const libraries: string[] = [];
          const version = null;

          // Check for Angular globals
          if ((window as any).ng || (window as any).angular) {
            confidence += 0.4;
          }

          // Check for Angular elements
          if (
            document.querySelector('[ng-app]') ||
            document.querySelector('[ng-controller]') ||
            document.querySelector('app-root')
          ) {
            confidence += 0.3;
          }

          // Check for Angular scripts
          if (document.querySelector('script[src*="angular"]')) {
            confidence += 0.2;
          }

          // Check for Angular Material
          if (document.querySelector('[class*="mat-"]')) {
            libraries.push('@angular/material');
          }

          return {
            framework: 'angular',
            confidence: Math.min(confidence, 1),
            version,
            libraries,
          };
        },

        /**
         * Detect Svelte
         */
        detectSvelte() {
          let confidence = 0;
          const libraries: string[] = [];

          // Check for Svelte-specific classes
          if (document.querySelector('[class*="svelte-"]')) {
            confidence += 0.4;
          }

          // Check for Svelte scripts
          if (document.querySelector('script[src*="svelte"]')) {
            confidence += 0.3;
          }

          // Check for Svelte global
          if ((window as any).__SVELTE__) {
            confidence += 0.3;
          }

          return {
            framework: 'svelte',
            confidence: Math.min(confidence, 1),
            version: null,
            libraries,
          };
        },

        /**
         * Detect Next.js
         */
        detectNextJS() {
          let confidence = 0;
          const libraries: string[] = [];

          // Check for Next.js data
          if ((window as any).__NEXT_DATA__) {
            confidence += 0.5;
          }

          // Check for Next.js root element
          if (document.querySelector('#__next')) {
            confidence += 0.3;
          }

          // Check for Next.js scripts
          if (document.querySelector('script[src*="_next"]')) {
            confidence += 0.2;
          }

          return {
            framework: 'nextjs',
            confidence: Math.min(confidence, 1),
            version: null,
            libraries,
          };
        },

        /**
         * Detect Nuxt.js
         */
        detectNuxtJS() {
          let confidence = 0;
          const libraries: string[] = [];

          // Check for Nuxt.js data
          if ((window as any).__NUXT__) {
            confidence += 0.5;
          }

          // Check for Nuxt.js root element
          if (document.querySelector('#__nuxt')) {
            confidence += 0.3;
          }

          // Check for Nuxt.js scripts
          if (document.querySelector('script[src*="_nuxt"]')) {
            confidence += 0.2;
          }

          return {
            framework: 'nuxtjs',
            confidence: Math.min(confidence, 1),
            version: null,
            libraries,
          };
        },

        /**
         * Detect Gatsby
         */
        detectGatsby() {
          let confidence = 0;
          const libraries: string[] = [];

          // Check for Gatsby globals
          if ((window as any).___gatsby) {
            confidence += 0.4;
          }

          // Check for Gatsby root element
          if (document.querySelector('#___gatsby')) {
            confidence += 0.3;
          }

          // Check for Gatsby-specific elements
          if (document.querySelector('[data-gatsby-head]')) {
            confidence += 0.3;
          }

          return {
            framework: 'gatsby',
            confidence: Math.min(confidence, 1),
            version: null,
            libraries,
          };
        },
      };
    });
  }

  /**
   * Detect frameworks
   */
  private async detectFrameworks(
    page: Page,
    config: FrameworkOptimizationConfig,
  ): Promise<FrameworkDetection[]> {
    return await page.evaluate(() => {
      return (window as any).wcagFrameworkDetection.detectAllFrameworks();
    });
  }

  /**
   * Generate framework-specific optimizations
   */
  private async generateFrameworkOptimizations(
    page: Page,
    frameworks: FrameworkDetection[],
    config: FrameworkOptimizationConfig,
  ): Promise<FrameworkOptimization[]> {
    const optimizations: FrameworkOptimization[] = [];

    for (const framework of frameworks) {
      const pattern = this.frameworkPatterns.get(framework.framework);
      if (pattern) {
        optimizations.push({
          framework: framework.framework,
          optimizations: pattern.accessibilityPatterns || [],
          accessibilityPatterns: pattern.accessibilityPatterns || [],
          commonIssues: pattern.commonIssues || [],
          recommendations: this.generateFrameworkRecommendations(framework, pattern),
          testingStrategies: pattern.testingTools || [],
          toolingRecommendations: this.generateToolingRecommendations(framework, pattern),
        });
      }
    }

    return optimizations;
  }

  /**
   * Generate framework-specific recommendations
   */
  private generateFrameworkRecommendations(framework: FrameworkDetection, pattern: any): string[] {
    const recommendations: string[] = [];

    switch (framework.framework) {
      case 'react':
        recommendations.push('Install and configure react-axe for development');
        recommendations.push('Use @testing-library/react for accessibility testing');
        recommendations.push('Implement proper focus management with useRef');
        recommendations.push('Use semantic HTML elements and avoid div soup');
        break;
      case 'vue':
        recommendations.push('Use Vue accessibility plugins and directives');
        recommendations.push('Implement proper focus management with $refs');
        recommendations.push('Use Vue transitions for accessible animations');
        break;
      case 'angular':
        recommendations.push('Use Angular CDK a11y module for accessibility features');
        recommendations.push('Implement proper focus trapping and management');
        recommendations.push('Use Angular Material accessibility features');
        break;
      case 'svelte':
        recommendations.push('Enable Svelte accessibility warnings in development');
        recommendations.push('Use Svelte stores for accessible state management');
        break;
    }

    return recommendations;
  }

  /**
   * Generate tooling recommendations
   */
  private generateToolingRecommendations(framework: FrameworkDetection, pattern: any): string[] {
    const tools: string[] = [];

    // Add framework-specific tools
    if (pattern.testingTools) {
      tools.push(
        ...pattern.testingTools.map((tool: string) => `Install ${tool} for accessibility testing`),
      );
    }

    // Add general accessibility tools
    tools.push('Use axe-core browser extension for manual testing');
    tools.push('Integrate automated accessibility testing in CI/CD pipeline');
    tools.push('Use Lighthouse accessibility audits');

    return tools;
  }

  /**
   * Analyze framework-specific issues
   */
  private async analyzeFrameworkSpecificIssues(
    page: Page,
    primaryFramework: FrameworkDetection,
  ): Promise<string[]> {
    return await page.evaluate((framework) => {
      const issues: string[] = [];

      switch (framework.framework) {
        case 'react':
          // Check for common React accessibility issues
          if (document.querySelector('[data-reactroot] div > div > div')) {
            issues.push('Excessive div nesting detected - consider semantic HTML');
          }
          if (!document.querySelector('[role="main"]') && !document.querySelector('main')) {
            issues.push('Missing main landmark in React application');
          }
          break;
        case 'vue':
          // Check for Vue-specific issues
          if (document.querySelector('[data-v-] [data-v-]') && !document.querySelector('[role]')) {
            issues.push('Vue components missing ARIA roles');
          }
          break;
        case 'angular':
          // Check for Angular-specific issues
          if (document.querySelector('app-root') && !document.querySelector('[role="main"]')) {
            issues.push('Angular app missing main content landmark');
          }
          break;
      }

      return issues;
    }, primaryFramework);
  }

  /**
   * Calculate overall results
   */
  private calculateOverallResults(
    detectedFrameworks: FrameworkDetection[],
    primaryFramework: FrameworkDetection,
    optimizations: FrameworkOptimization[],
    frameworkSpecificIssues: string[],
  ): FrameworkAnalysisResult {
    // Calculate score based on framework detection confidence and issues
    let overallScore = 100;

    // Deduct points for framework-specific issues
    overallScore -= frameworkSpecificIssues.length * 10;

    // Adjust score based on framework maturity and accessibility support
    if (primaryFramework.framework === 'react' || primaryFramework.framework === 'angular') {
      overallScore += 5; // Bonus for mature accessibility ecosystems
    }

    overallScore = Math.max(0, Math.min(100, overallScore));

    // Generate enhanced recommendations
    const enhancedRecommendations: string[] = [
      `Optimize for ${primaryFramework.framework} accessibility best practices`,
      'Implement framework-specific accessibility testing strategies',
      'Use framework-specific accessibility libraries and tools',
      'Follow framework accessibility documentation and guidelines',
    ];

    return {
      detectedFrameworks,
      primaryFramework,
      optimizations,
      overallScore,
      frameworkSpecificIssues,
      enhancedRecommendations,
    };
  }
}

export default FrameworkOptimizer;
