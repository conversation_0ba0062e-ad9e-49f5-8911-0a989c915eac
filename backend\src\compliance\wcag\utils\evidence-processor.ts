/**
 * Evidence Processing Utilities
 * Handles backward-compatible evidence transformation
 * DEPRECATED: Use EvidenceStandardizer for new implementations
 * This class now delegates to EvidenceStandardizer for consistency
 */

import { WcagEvidence } from '../types';
import {
  WcagEvidenceEnhanced,
  WcagFixExample,
  WcagRuleIdEnhanced,
  FixExampleTemplate,
  EvidenceProcessingOptions
} from '../types-enhanced';
import { EvidenceStandardizer } from './evidence-standardizer';

export class EvidenceProcessor {
  /**
   * Process evidence with backward compatibility
   * DEPRECATED: Delegates to EvidenceStandardizer.standardizeEvidence()
   * @deprecated Use EvidenceStandardizer.standardizeEvidence() directly
   */
  static processEvidence(
    evidence: WcagEvidence[],
    options: Partial<EvidenceProcessingOptions> = {}
  ): WcagEvidenceEnhanced[] {
    // Delegate to EvidenceStandardizer for consistency
    return EvidenceStandardizer.standardizeEvidence(evidence, {
      ruleId: 'LEGACY-PROCESSOR',
      ruleName: 'Legacy Evidence Processing',
      scanDuration: 0,
      elementsAnalyzed: evidence.length,
      checkSpecificData: {
        legacyProcessing: true,
        processingOptions: options,
      },
    });
  }

  /**
   * Enhanced processing method that uses EvidenceStandardizer
   * Recommended for new implementations
   */
  static async processEvidenceEnhanced(
    evidence: WcagEvidence[],
    checkMetadata: {
      ruleId: string;
      ruleName: string;
      scanDuration: number;
      elementsAnalyzed: number;
      checkSpecificData?: Record<string, any>;
    },
    options: Partial<EvidenceProcessingOptions> = {}
  ): Promise<WcagEvidenceEnhanced[]> {
    // Convert EvidenceProcessingOptions to EvidenceCollectionOptions
    const collectionOptions = {
      enableAdvancedSelectors: options.includeElementCounts,
      enableContextAnalysis: options.includeMetadata,
      enablePerformanceOptimization: options.enablePerformanceMetrics,
      maxEvidenceItems: 50,
      evidenceQualityThreshold: 0.6,
    };

    return EvidenceStandardizer.standardizeEvidenceEnhanced(
      evidence,
      checkMetadata,
      collectionOptions
    );
  }

  /**
   * Legacy method maintained for backward compatibility
   * @deprecated Use EvidenceStandardizer methods directly
   */
  static processEvidenceLegacy(
    evidence: WcagEvidence[],
    options: Partial<EvidenceProcessingOptions> = {}
  ): WcagEvidenceEnhanced[] {
    const defaultOptions: EvidenceProcessingOptions = {
      includeElementCounts: true,
      generateFixExamples: true,
      enablePerformanceMetrics: true,
      includeMetadata: true,
    };

    const processingOptions = { ...defaultOptions, ...options };

    return evidence.map(item => {
      // Start with existing evidence (type-safe)
      const enhanced: WcagEvidenceEnhanced = { ...item };

      // Add enhanced fields only if they exist (type-safe casting)
      const anyItem = item as any;

      if (processingOptions.includeElementCounts && typeof anyItem.elementCount === 'number') {
        enhanced.elementCount = anyItem.elementCount;
      }

      if (Array.isArray(anyItem.affectedSelectors)) {
        enhanced.affectedSelectors = anyItem.affectedSelectors;
      }

      if (processingOptions.generateFixExamples && anyItem.fixExample && typeof anyItem.fixExample === 'object') {
        enhanced.fixExample = anyItem.fixExample as WcagFixExample;
      }
      
      if (processingOptions.includeMetadata && anyItem.metadata && typeof anyItem.metadata === 'object') {
        enhanced.metadata = anyItem.metadata;
      }
      
      return enhanced;
    });
  }

  /**
   * Convert enhanced evidence back to legacy format for export
   */
  static toLegacyFormat(evidence: WcagEvidenceEnhanced[]): WcagEvidence[] {
    return evidence.map(item => ({
      type: item.type,
      description: item.description,
      value: item.value,
      selector: item.selector,
      screenshot: item.screenshot,
      severity: item.severity,
      message: item.message,
      element: item.element,
      details: item.details,
    }));
  }

  /**
   * Generate fix examples for evidence
   */
  static generateFixExamples(
    evidence: WcagEvidenceEnhanced[],
    ruleId: WcagRuleIdEnhanced
  ): WcagFixExample[] {
    const fixTemplates = this.getFixTemplates();
    const template = fixTemplates[ruleId];
    
    if (!template) return [];
    
    return evidence
      .filter(item => item.severity === 'error')
      .map(item => ({
        before: this.extractBeforeExample(item, template),
        after: this.generateAfterExample(item, template),
        description: template.description,
        codeExample: template.codeExample,
        resources: template.resources || [],
      }));
  }

  /**
   * Enhance evidence with element counts and metadata
   */
  static enhanceWithMetadata(
    evidence: WcagEvidenceEnhanced[],
    scanDuration: number,
    elementsAnalyzed: number
  ): WcagEvidenceEnhanced[] {
    return evidence.map(item => ({
      ...item,
      elementCount: item.elementCount || 1,
      metadata: {
        ...item.metadata,
        scanDuration,
        elementsAnalyzed,
        checkSpecificData: {
          ...item.metadata?.checkSpecificData,
          enhancedProcessing: true,
        },
      },
    }));
  }

  /**
   * Validate evidence structure for enhanced features
   */
  static validateEnhancedEvidence(evidence: WcagEvidenceEnhanced[]): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    evidence.forEach((item, index) => {
      // Required fields validation
      if (!item.type || !item.description || !item.value) {
        errors.push(`Evidence item ${index}: Missing required fields`);
      }

      // Enhanced fields validation
      if (item.elementCount !== undefined && item.elementCount < 0) {
        errors.push(`Evidence item ${index}: elementCount cannot be negative`);
      }

      if (item.affectedSelectors && !Array.isArray(item.affectedSelectors)) {
        errors.push(`Evidence item ${index}: affectedSelectors must be an array`);
      }

      if (item.fixExample) {
        if (!item.fixExample.before || !item.fixExample.after || !item.fixExample.description) {
          warnings.push(`Evidence item ${index}: Incomplete fix example`);
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Fix templates for different WCAG rules
   */
  private static getFixTemplates(): Record<string, FixExampleTemplate> {
    return {
      'WCAG-024': {
        ruleId: 'WCAG-024',
        description: 'Add lang attribute to html element',
        beforePattern: '<html>',
        afterPattern: '<html lang="en">',
        codeExample: `
<!-- Before -->
<html>
  <head><title>Page Title</title></head>
  <body>Content</body>
</html>

<!-- After -->
<html lang="en">
  <head><title>Page Title</title></head>
  <body>Content</body>
</html>
        `,
        resources: [
          'https://www.w3.org/WAI/WCAG21/Understanding/language-of-page.html',
          'https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/lang'
        ]
      },
      'WCAG-025': {
        ruleId: 'WCAG-025',
        description: 'Wrap content in appropriate landmark elements',
        beforePattern: '<div>Content without landmarks</div>',
        afterPattern: '<main><div>Content within landmark</div></main>',
        codeExample: `
<!-- Before -->
<body>
  <div>Navigation content</div>
  <div>Main content</div>
  <div>Sidebar content</div>
</body>

<!-- After -->
<body>
  <nav>Navigation content</nav>
  <main>Main content</main>
  <aside>Sidebar content</aside>
</body>
        `,
        resources: [
          'https://www.w3.org/WAI/ARIA/apg/practices/landmark-regions/',
          'https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/landmark_role'
        ]
      },
      'WCAG-026': {
        ruleId: 'WCAG-026',
        description: 'Use descriptive link text that explains the purpose',
        beforePattern: '<a href="/page">Click here</a>',
        afterPattern: '<a href="/page">Read our accessibility policy</a>',
        codeExample: `
<!-- Before -->
<a href="/contact">Click here</a>
<a href="/about">More</a>
<a href="/services">Read more</a>

<!-- After -->
<a href="/contact">Contact us</a>
<a href="/about">About our company</a>
<a href="/services">View our services</a>
        `,
        resources: [
          'https://www.w3.org/WAI/WCAG21/Understanding/link-purpose-in-context.html',
          'https://webaim.org/techniques/hypertext/link_text'
        ]
      }
    };
  }

  private static extractBeforeExample(evidence: WcagEvidenceEnhanced, template: FixExampleTemplate): string {
    if (evidence.value && evidence.value.includes('<')) {
      return evidence.value;
    }
    return template.beforePattern || 'No example available';
  }

  private static generateAfterExample(evidence: WcagEvidenceEnhanced, template: FixExampleTemplate): string {
    if (evidence.fixExample?.after) {
      return evidence.fixExample.after;
    }
    return template.afterPattern || 'Fix example not available';
  }

  /**
   * Calculate performance metrics for evidence processing
   */
  static calculatePerformanceMetrics(
    evidence: WcagEvidenceEnhanced[],
    processingStartTime: number
  ): {
    totalProcessingTime: number;
    averageProcessingTime: number;
    enhancedItemsCount: number;
    enhancementRate: number;
  } {
    const processingTime = Date.now() - processingStartTime;
    const enhancedItems = evidence.filter(item => 
      item.elementCount !== undefined || 
      item.affectedSelectors !== undefined || 
      item.fixExample !== undefined || 
      item.metadata !== undefined
    );

    return {
      totalProcessingTime: processingTime,
      averageProcessingTime: evidence.length > 0 ? processingTime / evidence.length : 0,
      enhancedItemsCount: enhancedItems.length,
      enhancementRate: evidence.length > 0 ? enhancedItems.length / evidence.length : 0,
    };
  }
}
