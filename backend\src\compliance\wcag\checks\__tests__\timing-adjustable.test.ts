/**
 * WCAG-044: Timing Adjustable Check Tests
 * Success Criterion: 2.2.1 Timing Adjustable (Level A)
 */

import { TimingAdjustableCheck } from '../timing-adjustable';
import { CheckConfig } from '../../utils/check-template';
import { Page } from 'puppeteer';

// Mock dependencies
jest.mock('../../utils/check-template');
jest.mock('../../utils/evidence-processor');

describe('TimingAdjustableCheck', () => {
  let check: TimingAdjustableCheck;
  let mockPage: jest.Mocked<Page>;
  let mockConfig: CheckConfig;

  beforeEach(() => {
    check = new TimingAdjustableCheck();
    mockPage = {
      evaluate: jest.fn(),
    } as any;
    mockConfig = {
      targetUrl: 'https://example.com',
      userId: 'test-user',
      requestId: 'test-request',
    };
  });

  describe('executeTimingAdjustableCheck', () => {
    it('should pass when no timing elements are found', async () => {
      mockPage.evaluate.mockResolvedValue({
        timingElements: [],
        totalElements: 0,
        elementsWithControls: 0,
      });

      const result = await (check as any).executeTimingAdjustableCheck(mockPage, mockConfig);

      expect(result.score).toBe(100);
      expect(result.evidence).toHaveLength(0);
      expect(result.issues).toHaveLength(0);
    });

    it('should fail when meta refresh without controls is found', async () => {
      mockPage.evaluate.mockResolvedValue({
        timingElements: [
          {
            type: 'meta_refresh',
            element: 'meta',
            selector: 'meta[http-equiv="refresh"]:nth-of-type(1)',
            hasControls: false,
            timeValue: '30',
            description: 'Auto-refresh meta tag with 30 second delay',
          },
        ],
        totalElements: 1,
        elementsWithControls: 0,
      });

      const result = await (check as any).executeTimingAdjustableCheck(mockPage, mockConfig);

      expect(result.score).toBe(75); // 100 - (1 * 25)
      expect(result.evidence).toHaveLength(1);
      expect(result.evidence[0].severity).toBe('error');
      expect(result.evidence[0].description).toContain('Auto-refresh meta tag');
      expect(result.issues).toContain('1 timing elements found without user controls');
    });

    it('should fail when session timeout without controls is found', async () => {
      mockPage.evaluate.mockResolvedValue({
        timingElements: [
          {
            type: 'session_timeout',
            element: 'div',
            selector: 'div:nth-of-type(1)',
            hasControls: false,
            description: 'Session timeout element detected',
          },
        ],
        totalElements: 1,
        elementsWithControls: 0,
      });

      const result = await (check as any).executeTimingAdjustableCheck(mockPage, mockConfig);

      expect(result.score).toBe(75);
      expect(result.evidence).toHaveLength(1);
      expect(result.evidence[0].fixExample).toBeDefined();
      expect(result.evidence[0].fixExample?.before).toContain('Session will expire');
      expect(result.evidence[0].fixExample?.after).toContain('extendSession()');
    });

    it('should pass when timing elements have user controls', async () => {
      mockPage.evaluate.mockResolvedValue({
        timingElements: [
          {
            type: 'countdown_timer',
            element: 'div',
            selector: 'div:nth-of-type(1)',
            hasControls: true,
            description: 'Countdown timer element detected',
          },
        ],
        totalElements: 1,
        elementsWithControls: 1,
      });

      const result = await (check as any).executeTimingAdjustableCheck(mockPage, mockConfig);

      expect(result.score).toBe(100);
      expect(result.evidence).toHaveLength(0);
      expect(result.issues).toHaveLength(0);
    });

    it('should handle multiple timing elements correctly', async () => {
      mockPage.evaluate.mockResolvedValue({
        timingElements: [
          {
            type: 'meta_refresh',
            element: 'meta',
            selector: 'meta[http-equiv="refresh"]:nth-of-type(1)',
            hasControls: false,
            timeValue: '30',
            description: 'Auto-refresh meta tag with 30 second delay',
          },
          {
            type: 'session_timeout',
            element: 'div',
            selector: 'div:nth-of-type(1)',
            hasControls: true,
            description: 'Session timeout element detected',
          },
          {
            type: 'countdown_timer',
            element: 'div',
            selector: 'div:nth-of-type(2)',
            hasControls: false,
            description: 'Countdown timer element detected',
          },
        ],
        totalElements: 3,
        elementsWithControls: 1,
      });

      const result = await (check as any).executeTimingAdjustableCheck(mockPage, mockConfig);

      expect(result.score).toBe(50); // 100 - (2 * 25)
      expect(result.evidence).toHaveLength(2); // Only elements without controls
      expect(result.issues).toContain('2 timing elements found without user controls');
    });

    it('should provide appropriate recommendations', async () => {
      mockPage.evaluate.mockResolvedValue({
        timingElements: [
          {
            type: 'form_timeout',
            element: 'div',
            selector: 'form div:nth-of-type(1)',
            hasControls: false,
            description: 'Form timeout warning detected',
          },
        ],
        totalElements: 1,
        elementsWithControls: 0,
      });

      const result = await (check as any).executeTimingAdjustableCheck(mockPage, mockConfig);

      expect(result.recommendations).toContain('Provide user controls to turn off, adjust, or extend time limits');
      expect(result.recommendations).toContain('Allow users to extend time limits by at least 10 times the default');
      expect(result.recommendations).toContain('Warn users before time expires and provide at least 20 seconds to extend');
    });
  });

  describe('helper methods', () => {
    it('should provide correct before examples for different timing types', () => {
      expect((check as any).getBeforeExample('meta_refresh')).toContain('<meta http-equiv="refresh"');
      expect((check as any).getBeforeExample('session_timeout')).toContain('Session will expire');
      expect((check as any).getBeforeExample('countdown_timer')).toContain('Time remaining');
      expect((check as any).getBeforeExample('form_timeout')).toContain('Form will timeout');
    });

    it('should provide correct after examples for different timing types', () => {
      expect((check as any).getAfterExample('meta_refresh')).toContain('refreshPage()');
      expect((check as any).getAfterExample('session_timeout')).toContain('extendSession()');
      expect((check as any).getAfterExample('countdown_timer')).toContain('pauseTimer()');
      expect((check as any).getAfterExample('form_timeout')).toContain('extendTimeout()');
    });

    it('should provide correct fix descriptions for different timing types', () => {
      expect((check as any).getFixDescription('meta_refresh')).toContain('Remove automatic refresh');
      expect((check as any).getFixDescription('session_timeout')).toContain('Add controls to extend');
      expect((check as any).getFixDescription('countdown_timer')).toContain('Provide controls to pause');
      expect((check as any).getFixDescription('form_timeout')).toContain('Add controls to extend');
    });

    it('should provide code examples for different timing types', () => {
      expect((check as any).getCodeExample('meta_refresh')).toContain('refreshPage()');
      expect((check as any).getCodeExample('session_timeout')).toContain('extendSession()');
      expect((check as any).getCodeExample('countdown_timer')).toContain('pauseTimer()');
      expect((check as any).getCodeExample('form_timeout')).toContain('extendTimeout()');
    });
  });
});
