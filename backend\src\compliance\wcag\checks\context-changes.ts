/**
 * WCAG-064: Context Changes Check (3.2.5 Level AAA)
 * 70% Automated - Detects unexpected context changes
 */

import { Page } from 'puppeteer';
import { CheckConfig, CheckTemplate } from '../utils/check-template';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced, WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export class ContextChangesCheck {
  private checkTemplate = new CheckTemplate();
  private smartCache = SmartCache.getInstance();

  async performCheck(config: CheckConfig): Promise<WcagCheckResultEnhanced> {
    const result = await this.checkTemplate.executeCheck(
      'WCAG-064',
      'Change on Request',
      'predictable',
      0.0305,
      'AAA',
      config,
      this.executeContextChangesCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with context change analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-064',
        ruleName: 'Change on Request',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.7,
          checkType: 'context-change-analysis',
          behaviorAnalysis: true,
          userInitiatedChanges: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.7,
        maxEvidenceItems: 20,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeContextChangesCheck(
    page: Page,
    config: CheckConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze elements that might cause unexpected context changes
    const contextAnalysis = await page.evaluate(() => {
      const problematicElements: Array<{
        selector: string;
        tagName: string;
        changeType: string;
        hasWarning: boolean;
        hasUserControl: boolean;
        isAutomatic: boolean;
        triggerEvent: string;
        attributes: Record<string, string>;
        description: string;
        severity: 'error' | 'warning' | 'info';
      }> = [];

      // Check for elements that might cause context changes
      const allElements = document.querySelectorAll('*');
      
      allElements.forEach((element, index) => {
        const issues: Array<{
          changeType: string;
          hasWarning: boolean;
          hasUserControl: boolean;
          isAutomatic: boolean;
          triggerEvent: string;
          description: string;
          severity: 'error' | 'warning' | 'info';
        }> = [];

        // Check for automatic redirects
        if (element.tagName.toLowerCase() === 'meta') {
          const httpEquiv = element.getAttribute('http-equiv');
          const content = element.getAttribute('content');
          if (httpEquiv === 'refresh' && content) {
            const hasDelay = /^\d+/.test(content);
            const delay = hasDelay ? parseInt(content.match(/^\d+/)?.[0] || '0') : 0;
            
            issues.push({
              changeType: 'meta-refresh',
              hasWarning: false,
              hasUserControl: false,
              isAutomatic: true,
              triggerEvent: 'automatic',
              description: `Meta refresh with ${delay} second delay`,
              severity: delay < 5 ? 'error' : 'warning',
            });
          }
        }

        // Check for JavaScript that might cause context changes
        const onclickHandler = element.getAttribute('onclick');
        if (onclickHandler) {
          // Check for window operations
          if (/window\.(?:open|close|location|history)/i.test(onclickHandler)) {
            issues.push({
              changeType: 'javascript-navigation',
              hasWarning: false,
              hasUserControl: true,
              isAutomatic: false,
              triggerEvent: 'onclick',
              description: 'JavaScript navigation in onclick handler',
              severity: 'warning',
            });
          }

          // Check for form submission
          if (/submit|form/i.test(onclickHandler)) {
            issues.push({
              changeType: 'javascript-form-submit',
              hasWarning: false,
              hasUserControl: true,
              isAutomatic: false,
              triggerEvent: 'onclick',
              description: 'JavaScript form submission',
              severity: 'info',
            });
          }
        }

        // Check for automatic form submission
        if (element.tagName.toLowerCase() === 'form') {
          const hasAutoSubmit = element.hasAttribute('data-auto-submit') ||
                               element.querySelector('[onchange*="submit"], [onblur*="submit"]') !== null;
          
          if (hasAutoSubmit) {
            issues.push({
              changeType: 'auto-form-submit',
              hasWarning: false,
              hasUserControl: false,
              isAutomatic: true,
              triggerEvent: 'change/blur',
              description: 'Form with automatic submission',
              severity: 'error',
            });
          }
        }

        // Check for select elements that might auto-navigate
        if (element.tagName.toLowerCase() === 'select') {
          const onchangeHandler = element.getAttribute('onchange');
          if (onchangeHandler && /location|window|submit|navigate/i.test(onchangeHandler)) {
            issues.push({
              changeType: 'select-auto-navigate',
              hasWarning: false,
              hasUserControl: false,
              isAutomatic: true,
              triggerEvent: 'onchange',
              description: 'Select element with automatic navigation',
              severity: 'error',
            });
          }
        }

        // Check for links that open in new windows without warning
        if (element.tagName.toLowerCase() === 'a') {
          const target = element.getAttribute('target');
          const href = element.getAttribute('href');
          
          if (target === '_blank' && href) {
            const hasWarning = element.textContent?.includes('new window') ||
                              element.textContent?.includes('new tab') ||
                              element.hasAttribute('aria-label') && 
                              (element.getAttribute('aria-label') || '').includes('new window') ||
                              element.querySelector('[aria-hidden="false"]') !== null;
            
            issues.push({
              changeType: 'new-window-link',
              hasWarning,
              hasUserControl: true,
              isAutomatic: false,
              triggerEvent: 'click',
              description: hasWarning ? 'Link opens new window with warning' : 'Link opens new window without warning',
              severity: hasWarning ? 'info' : 'warning',
            });
          }

          // Check for JavaScript links
          if (href && href.startsWith('javascript:')) {
            issues.push({
              changeType: 'javascript-link',
              hasWarning: false,
              hasUserControl: true,
              isAutomatic: false,
              triggerEvent: 'click',
              description: 'JavaScript link that may cause context change',
              severity: 'warning',
            });
          }
        }

        // Check for elements with automatic focus changes
        const hasFocusEvents = element.hasAttribute('onfocus') || element.hasAttribute('onblur');
        if (hasFocusEvents) {
          const onfocusHandler = element.getAttribute('onfocus') || '';
          const onblurHandler = element.getAttribute('onblur') || '';
          
          if (/location|window|submit|navigate/i.test(onfocusHandler + onblurHandler)) {
            issues.push({
              changeType: 'focus-context-change',
              hasWarning: false,
              hasUserControl: false,
              isAutomatic: true,
              triggerEvent: 'focus/blur',
              description: 'Element causes context change on focus/blur',
              severity: 'error',
            });
          }
        }

        // Check for popup/modal triggers
        const hasPopupTrigger = element.hasAttribute('data-toggle') ||
                               element.hasAttribute('data-target') ||
                               element.classList.contains('modal-trigger') ||
                               element.classList.contains('popup-trigger');
        
        if (hasPopupTrigger) {
          const hasWarning = element.textContent?.includes('popup') ||
                            element.textContent?.includes('modal') ||
                            element.hasAttribute('aria-label') &&
                            (element.getAttribute('aria-label') || '').includes('popup');
          
          issues.push({
            changeType: 'popup-modal',
            hasWarning,
            hasUserControl: true,
            isAutomatic: false,
            triggerEvent: 'click',
            description: hasWarning ? 'Popup/modal trigger with warning' : 'Popup/modal trigger without warning',
            severity: hasWarning ? 'info' : 'warning',
          });
        }

        // Add issues to problematic elements
        issues.forEach(issue => {
          problematicElements.push({
            selector: generateSelector(element, index),
            tagName: element.tagName.toLowerCase(),
            changeType: issue.changeType,
            hasWarning: issue.hasWarning,
            hasUserControl: issue.hasUserControl,
            isAutomatic: issue.isAutomatic,
            triggerEvent: issue.triggerEvent,
            attributes: getRelevantAttributes(element),
            description: issue.description,
            severity: issue.severity,
          });
        });
      });

      return {
        problematicElements,
        totalElements: allElements.length,
      };

      function generateSelector(element: Element, index: number): string {
        if (element.id) return `#${element.id}`;
        if (element.className) {
          const classes = element.className.split(' ').filter(c => c.trim());
          if (classes.length > 0) return `.${classes[0]}`;
        }
        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function getRelevantAttributes(element: Element): Record<string, string> {
        const attrs: Record<string, string> = {};
        const relevantAttrs = ['id', 'class', 'href', 'target', 'onclick', 'onchange', 'onfocus', 'onblur', 'data-toggle', 'data-target'];
        
        relevantAttrs.forEach(attr => {
          const value = element.getAttribute(attr);
          if (value) attrs[attr] = value.substring(0, 100); // Limit length
        });

        return attrs;
      }
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const elementCount = contextAnalysis.problematicElements.length;

    if (elementCount > 0) {
      // Categorize issues by severity
      const errorElements = contextAnalysis.problematicElements.filter(el => el.severity === 'error');
      const warningElements = contextAnalysis.problematicElements.filter(el => el.severity === 'warning');
      const infoElements = contextAnalysis.problematicElements.filter(el => el.severity === 'info');

      // Calculate score penalties
      if (errorElements.length > 0) {
        score -= Math.min(50, errorElements.length * 15);
        issues.push(`${errorElements.length} elements cause unexpected context changes`);
      }

      if (warningElements.length > 0) {
        score -= Math.min(30, warningElements.length * 8);
        issues.push(`${warningElements.length} elements may cause unexpected context changes`);
      }

      if (infoElements.length > 0) {
        issues.push(`${infoElements.length} elements cause context changes with appropriate warnings`);
      }

      evidence.push({
        type: 'interaction',
        description: 'Elements that may cause context changes',
        value: `Found ${elementCount} elements that may cause context changes`,
        elementCount,
        affectedSelectors: contextAnalysis.problematicElements.map(el => el.selector),
        severity: errorElements.length > 0 ? 'error' : warningElements.length > 0 ? 'warning' : 'info',
        fixExample: {
          before: '<select onchange="window.location=this.value">',
          after: '<select onchange="if(confirm(\'Navigate to \' + this.options[this.selectedIndex].text + \'?\')) window.location=this.value">',
          description: 'Provide user control and warnings for context changes',
          codeExample: `
<!-- Before: Unexpected context changes -->
<meta http-equiv="refresh" content="5;url=newpage.html">
<select onchange="window.location=this.value">
  <option value="page1.html">Page 1</option>
  <option value="page2.html">Page 2</option>
</select>
<a href="document.pdf" target="_blank">Download PDF</a>

<!-- After: User-controlled context changes -->
<p>This page will redirect in 5 seconds. <a href="newpage.html">Continue now</a> or <button onclick="clearTimeout(redirectTimer)">Cancel</button></p>

<label for="navigation-select">Choose a page to navigate to:</label>
<select id="navigation-select">
  <option value="">Select a page...</option>
  <option value="page1.html">Page 1</option>
  <option value="page2.html">Page 2</option>
</select>
<button onclick="navigateToSelected()">Go to Selected Page</button>

<a href="document.pdf" target="_blank">
  Download PDF <span aria-label="opens in new window">(new window)</span>
</a>
          `,
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/change-on-request.html',
            'https://www.w3.org/WAI/WCAG21/Techniques/general/G200',
            'https://www.w3.org/WAI/WCAG21/Techniques/general/G201'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: contextAnalysis.totalElements,
          checkSpecificData: {
            totalProblematicElements: elementCount,
            errorElements: errorElements.length,
            warningElements: warningElements.length,
            infoElements: infoElements.length,
            changeTypes: [...new Set(contextAnalysis.problematicElements.map(el => el.changeType))],
            automaticChanges: contextAnalysis.problematicElements.filter(el => el.isAutomatic).length,
            userControlledChanges: contextAnalysis.problematicElements.filter(el => el.hasUserControl).length,
          },
        },
      });

      // Add specific examples for problematic elements
      contextAnalysis.problematicElements.slice(0, 10).forEach(element => {
        evidence.push({
          type: 'interaction',
          description: `Context change: ${element.description}`,
          value: `<${element.tagName} ${Object.entries(element.attributes)
            .map(([key, value]) => `${key}="${value}"`)
            .join(' ')}>`,
          selector: element.selector,
          severity: element.severity,
          metadata: {
            checkSpecificData: {
              changeType: element.changeType,
              hasWarning: element.hasWarning,
              hasUserControl: element.hasUserControl,
              isAutomatic: element.isAutomatic,
              triggerEvent: element.triggerEvent,
            },
          },
        });
      });

      recommendations.push('Ensure context changes are initiated only by user request');
      recommendations.push('Provide clear warnings before context changes occur');
      recommendations.push('Allow users to control timing of automatic redirects');
      recommendations.push('Use confirmation dialogs for navigation triggered by form controls');
      
      if (errorElements.length > 0) {
        recommendations.push('CRITICAL: Remove automatic context changes that occur without user control');
      }
      
      if (warningElements.length > 0) {
        recommendations.push('Add warnings for links that open in new windows');
        recommendations.push('Provide user control for navigation triggered by form elements');
      }
    } else {
      // No problematic context changes found
      evidence.push({
        type: 'info',
        description: 'No unexpected context changes detected',
        value: 'Page does not contain elements that cause unexpected context changes',
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: contextAnalysis.totalElements,
          checkSpecificData: {
            noContextChanges: true,
          },
        },
      });
    }

    // Ensure score doesn't go below 0
    score = Math.max(0, score);

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
