/**
 * Storage Optimizer for VPS Environments
 * Efficient storage utilization with compression and cleanup strategies
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import * as zlib from 'zlib';
import { promisify } from 'util';
import logger from '../../../utils/logger';

const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);

export interface StorageStats {
  totalSpace: number; // bytes
  usedSpace: number; // bytes
  freeSpace: number; // bytes
  usagePercent: number;
  files: {
    total: number;
    compressed: number;
    temporary: number;
    logs: number;
    cache: number;
  };
  directories: {
    temp: string;
    logs: string;
    cache: string;
    uploads: string;
  };
}

export interface StorageOptimizationConfig {
  enableCompression: boolean;
  enableAutoCleanup: boolean;
  enableTempFileCleanup: boolean;
  enableLogRotation: boolean;
  enableCacheOptimization: boolean;
  compressionLevel: number; // 1-9
  maxTempFileAge: number; // ms
  maxLogFileAge: number; // ms
  maxCacheSize: number; // bytes
  cleanupInterval: number; // ms
  compressionThreshold: number; // bytes
}

export interface FileInfo {
  path: string;
  size: number;
  created: number;
  modified: number;
  accessed: number;
  compressed: boolean;
  temporary: boolean;
  type: 'log' | 'cache' | 'temp' | 'upload' | 'other';
}

export interface CleanupResult {
  filesRemoved: number;
  bytesFreed: number;
  filesCompressed: number;
  compressionSaved: number;
  operations: string[];
}

/**
 * Advanced storage optimizer for VPS environments
 */
export class StorageOptimizer {
  private static instance: StorageOptimizer;
  private config: StorageOptimizationConfig;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private storageHistory: StorageStats[] = [];
  private baseDirectory: string;

  private constructor() {
    this.config = this.getDefaultConfig();
    this.baseDirectory = process.cwd();
    this.startStorageMonitoring();
    this.startAutoCleanup();
  }

  static getInstance(): StorageOptimizer {
    if (!StorageOptimizer.instance) {
      StorageOptimizer.instance = new StorageOptimizer();
    }
    return StorageOptimizer.instance;
  }

  /**
   * Get current storage statistics
   */
  async getCurrentStorageStats(): Promise<StorageStats> {
    try {
      const directories = {
        temp: path.join(this.baseDirectory, 'temp'),
        logs: path.join(this.baseDirectory, 'logs'),
        cache: path.join(this.baseDirectory, 'cache'),
        uploads: path.join(this.baseDirectory, 'uploads'),
      };

      // Ensure directories exist
      for (const dir of Object.values(directories)) {
        try {
          await fs.mkdir(dir, { recursive: true });
        } catch (error) {
          // Directory might already exist
        }
      }

      // Get disk usage (simplified - in production, use statvfs or similar)
      const totalSpace = 50 * 1024 * 1024 * 1024; // 50GB estimate
      const usedSpace = await this.calculateDirectorySize(this.baseDirectory);
      const freeSpace = totalSpace - usedSpace;
      const usagePercent = (usedSpace / totalSpace) * 100;

      // Count files by type
      const fileStats = await this.analyzeFiles(directories);

      const stats: StorageStats = {
        totalSpace,
        usedSpace,
        freeSpace,
        usagePercent,
        files: fileStats,
        directories,
      };

      // Store in history (keep last 24 entries = 24 hours at 1-hour intervals)
      this.storageHistory.push(stats);
      if (this.storageHistory.length > 24) {
        this.storageHistory.shift();
      }

      return stats;
    } catch (error) {
      logger.error('Error getting storage stats', { error });
      throw error;
    }
  }

  /**
   * Compress a file
   */
  async compressFile(
    filePath: string,
  ): Promise<{ originalSize: number; compressedSize: number; saved: number }> {
    try {
      const stats = await fs.stat(filePath);
      const originalSize = stats.size;

      // Skip if file is too small or already compressed
      if (originalSize < this.config.compressionThreshold || filePath.endsWith('.gz')) {
        return { originalSize, compressedSize: originalSize, saved: 0 };
      }

      const data = await fs.readFile(filePath);
      const compressed = await gzip(data, { level: this.config.compressionLevel });

      const compressedPath = `${filePath}.gz`;
      await fs.writeFile(compressedPath, compressed);

      // Remove original file
      await fs.unlink(filePath);

      const compressedSize = compressed.length;
      const saved = originalSize - compressedSize;

      logger.debug(`File compressed: ${filePath}`, {
        originalSize,
        compressedSize,
        saved,
        compressionRatio: ((saved / originalSize) * 100).toFixed(1) + '%',
      });

      return { originalSize, compressedSize, saved };
    } catch (error) {
      logger.error(`Error compressing file: ${filePath}`, { error });
      throw error;
    }
  }

  /**
   * Decompress a file
   */
  async decompressFile(compressedPath: string): Promise<string> {
    try {
      if (!compressedPath.endsWith('.gz')) {
        throw new Error('File is not compressed');
      }

      const compressed = await fs.readFile(compressedPath);
      const decompressed = await gunzip(compressed);

      const originalPath = compressedPath.slice(0, -3); // Remove .gz extension
      await fs.writeFile(originalPath, decompressed);

      logger.debug(`File decompressed: ${compressedPath} -> ${originalPath}`);

      return originalPath;
    } catch (error) {
      logger.error(`Error decompressing file: ${compressedPath}`, { error });
      throw error;
    }
  }

  /**
   * Clean up storage
   */
  async cleanupStorage(): Promise<CleanupResult> {
    const result: CleanupResult = {
      filesRemoved: 0,
      bytesFreed: 0,
      filesCompressed: 0,
      compressionSaved: 0,
      operations: [],
    };

    try {
      const stats = await this.getCurrentStorageStats();

      // Clean up temporary files
      if (this.config.enableTempFileCleanup) {
        const tempCleanup = await this.cleanupTempFiles(stats.directories.temp);
        result.filesRemoved += tempCleanup.filesRemoved;
        result.bytesFreed += tempCleanup.bytesFreed;
        result.operations.push(`Cleaned ${tempCleanup.filesRemoved} temp files`);
      }

      // Clean up old log files
      if (this.config.enableLogRotation) {
        const logCleanup = await this.cleanupLogFiles(stats.directories.logs);
        result.filesRemoved += logCleanup.filesRemoved;
        result.bytesFreed += logCleanup.bytesFreed;
        result.operations.push(`Cleaned ${logCleanup.filesRemoved} log files`);
      }

      // Optimize cache
      if (this.config.enableCacheOptimization) {
        const cacheCleanup = await this.optimizeCache(stats.directories.cache);
        result.filesRemoved += cacheCleanup.filesRemoved;
        result.bytesFreed += cacheCleanup.bytesFreed;
        result.operations.push(`Optimized cache: ${cacheCleanup.filesRemoved} files removed`);
      }

      // Compress large files
      if (this.config.enableCompression) {
        const compressionResult = await this.compressLargeFiles();
        result.filesCompressed += compressionResult.filesCompressed;
        result.compressionSaved += compressionResult.saved;
        result.operations.push(`Compressed ${compressionResult.filesCompressed} files`);
      }

      logger.info('Storage cleanup completed', {
        filesRemoved: result.filesRemoved,
        bytesFreed: result.bytesFreed,
        filesCompressed: result.filesCompressed,
        compressionSaved: result.compressionSaved,
      });

      return result;
    } catch (error) {
      logger.error('Error during storage cleanup', { error });
      throw error;
    }
  }

  /**
   * Get storage optimization recommendations
   */
  getStorageOptimizationRecommendations(): string[] {
    const recommendations: string[] = [];

    if (this.storageHistory.length === 0) {
      return ['Insufficient storage data for recommendations'];
    }

    const currentStats = this.storageHistory[this.storageHistory.length - 1];

    // High storage usage
    if (currentStats.usagePercent > 90) {
      recommendations.push('Critical storage usage - immediate cleanup required');
    } else if (currentStats.usagePercent > 80) {
      recommendations.push('High storage usage - consider cleanup and optimization');
    } else if (currentStats.usagePercent > 70) {
      recommendations.push('Moderate storage usage - monitor and plan cleanup');
    }

    // Many temporary files
    if (currentStats.files.temporary > 100) {
      recommendations.push(
        `Many temporary files (${currentStats.files.temporary}) - enable auto cleanup`,
      );
    }

    // Large cache
    const cacheSize = currentStats.usedSpace * 0.1; // Estimate 10% for cache
    if (cacheSize > this.config.maxCacheSize) {
      recommendations.push('Cache size exceeds limit - optimize cache storage');
    }

    // Compression opportunities
    const compressionRatio =
      currentStats.files.total > 0
        ? (currentStats.files.compressed / currentStats.files.total) * 100
        : 0;

    if (compressionRatio < 30 && !this.config.enableCompression) {
      recommendations.push('Low compression ratio - enable file compression');
    }

    // Log file accumulation
    if (currentStats.files.logs > 50) {
      recommendations.push(`Many log files (${currentStats.files.logs}) - enable log rotation`);
    }

    return recommendations;
  }

  /**
   * Get storage health score (0-100)
   */
  getStorageHealthScore(): number {
    if (this.storageHistory.length === 0) return 50;

    const currentStats = this.storageHistory[this.storageHistory.length - 1];
    let score = 100;

    // Deduct for high storage usage
    if (currentStats.usagePercent > 95) {
      score -= 50;
    } else if (currentStats.usagePercent > 90) {
      score -= 35;
    } else if (currentStats.usagePercent > 80) {
      score -= 25;
    } else if (currentStats.usagePercent > 70) {
      score -= 15;
    }

    // Deduct for many temporary files
    if (currentStats.files.temporary > 200) {
      score -= 20;
    } else if (currentStats.files.temporary > 100) {
      score -= 10;
    }

    // Deduct for many log files
    if (currentStats.files.logs > 100) {
      score -= 15;
    } else if (currentStats.files.logs > 50) {
      score -= 10;
    }

    // Bonus for good compression ratio
    const compressionRatio =
      currentStats.files.total > 0
        ? (currentStats.files.compressed / currentStats.files.total) * 100
        : 0;

    if (compressionRatio > 50) {
      score += 5;
    }

    return Math.max(0, score);
  }

  /**
   * Configure storage optimization settings
   */
  configure(config: Partial<StorageOptimizationConfig>): void {
    this.config = { ...this.config, ...config };

    // Restart monitoring with new config
    this.stopOptimization();
    this.startStorageMonitoring();
    this.startAutoCleanup();

    logger.info('Storage optimizer configuration updated', { config: this.config });
  }

  /**
   * Get default configuration
   */
  private getDefaultConfig(): StorageOptimizationConfig {
    return {
      enableCompression: process.env.WCAG_ENABLE_COMPRESSION !== 'false',
      enableAutoCleanup: process.env.WCAG_ENABLE_AUTO_CLEANUP !== 'false',
      enableTempFileCleanup: true,
      enableLogRotation: true,
      enableCacheOptimization: true,
      compressionLevel: parseInt(process.env.WCAG_COMPRESSION_LEVEL || '6'),
      maxTempFileAge: parseInt(process.env.WCAG_MAX_TEMP_FILE_AGE || '86400000'), // 24 hours
      maxLogFileAge: parseInt(process.env.WCAG_MAX_LOG_FILE_AGE || '604800000'), // 7 days
      maxCacheSize: parseInt(process.env.WCAG_MAX_CACHE_SIZE || '1073741824'), // 1GB
      cleanupInterval: parseInt(process.env.WCAG_CLEANUP_INTERVAL || '3600000'), // 1 hour
      compressionThreshold: parseInt(process.env.WCAG_COMPRESSION_THRESHOLD || '1024'), // 1KB
    };
  }

  /**
   * Calculate directory size
   */
  private async calculateDirectorySize(dirPath: string): Promise<number> {
    try {
      let totalSize = 0;
      const items = await fs.readdir(dirPath, { withFileTypes: true });

      for (const item of items) {
        const itemPath = path.join(dirPath, item.name);

        if (item.isDirectory()) {
          totalSize += await this.calculateDirectorySize(itemPath);
        } else if (item.isFile()) {
          const stats = await fs.stat(itemPath);
          totalSize += stats.size;
        }
      }

      return totalSize;
    } catch (error) {
      // Directory might not exist or be accessible
      return 0;
    }
  }

  /**
   * Analyze files in directories
   */
  private async analyzeFiles(directories: Record<string, string>): Promise<any> {
    const fileStats = {
      total: 0,
      compressed: 0,
      temporary: 0,
      logs: 0,
      cache: 0,
    };

    for (const [type, dirPath] of Object.entries(directories)) {
      try {
        const files = await this.getFilesRecursively(dirPath);

        for (const file of files) {
          fileStats.total++;

          if (file.endsWith('.gz')) {
            fileStats.compressed++;
          }

          switch (type) {
            case 'temp':
              fileStats.temporary++;
              break;
            case 'logs':
              fileStats.logs++;
              break;
            case 'cache':
              fileStats.cache++;
              break;
          }
        }
      } catch (error) {
        // Directory might not exist
      }
    }

    return fileStats;
  }

  /**
   * Get files recursively
   */
  private async getFilesRecursively(dirPath: string): Promise<string[]> {
    try {
      const files: string[] = [];
      const items = await fs.readdir(dirPath, { withFileTypes: true });

      for (const item of items) {
        const itemPath = path.join(dirPath, item.name);

        if (item.isDirectory()) {
          const subFiles = await this.getFilesRecursively(itemPath);
          files.push(...subFiles);
        } else if (item.isFile()) {
          files.push(itemPath);
        }
      }

      return files;
    } catch (error) {
      return [];
    }
  }

  /**
   * Clean up temporary files
   */
  private async cleanupTempFiles(
    tempDir: string,
  ): Promise<{ filesRemoved: number; bytesFreed: number }> {
    let filesRemoved = 0;
    let bytesFreed = 0;

    try {
      const files = await this.getFilesRecursively(tempDir);
      const now = Date.now();

      for (const file of files) {
        try {
          const stats = await fs.stat(file);
          const age = now - stats.mtime.getTime();

          if (age > this.config.maxTempFileAge) {
            bytesFreed += stats.size;
            await fs.unlink(file);
            filesRemoved++;
          }
        } catch (error) {
          // File might have been deleted already
        }
      }
    } catch (error) {
      logger.error('Error cleaning temp files', { error });
    }

    return { filesRemoved, bytesFreed };
  }

  /**
   * Clean up log files
   */
  private async cleanupLogFiles(
    logDir: string,
  ): Promise<{ filesRemoved: number; bytesFreed: number }> {
    let filesRemoved = 0;
    let bytesFreed = 0;

    try {
      const files = await this.getFilesRecursively(logDir);
      const now = Date.now();

      for (const file of files) {
        try {
          const stats = await fs.stat(file);
          const age = now - stats.mtime.getTime();

          if (age > this.config.maxLogFileAge) {
            bytesFreed += stats.size;
            await fs.unlink(file);
            filesRemoved++;
          }
        } catch (error) {
          // File might have been deleted already
        }
      }
    } catch (error) {
      logger.error('Error cleaning log files', { error });
    }

    return { filesRemoved, bytesFreed };
  }

  /**
   * Optimize cache
   */
  private async optimizeCache(
    cacheDir: string,
  ): Promise<{ filesRemoved: number; bytesFreed: number }> {
    let filesRemoved = 0;
    let bytesFreed = 0;

    try {
      const files = await this.getFilesRecursively(cacheDir);
      let totalCacheSize = 0;

      // Calculate total cache size
      for (const file of files) {
        try {
          const stats = await fs.stat(file);
          totalCacheSize += stats.size;
        } catch (error) {
          // File might not exist
        }
      }

      // If cache exceeds limit, remove oldest files
      if (totalCacheSize > this.config.maxCacheSize) {
        const fileStats = await Promise.all(
          files.map(async (file) => {
            try {
              const stats = await fs.stat(file);
              return { file, size: stats.size, mtime: stats.mtime.getTime() };
            } catch (error) {
              return null;
            }
          }),
        );

        const validFiles = fileStats.filter(Boolean) as any[];
        validFiles.sort((a, b) => a.mtime - b.mtime); // Oldest first

        let currentSize = totalCacheSize;
        for (const fileInfo of validFiles) {
          if (currentSize <= this.config.maxCacheSize) break;

          try {
            await fs.unlink(fileInfo.file);
            currentSize -= fileInfo.size;
            bytesFreed += fileInfo.size;
            filesRemoved++;
          } catch (error) {
            // File might have been deleted already
          }
        }
      }
    } catch (error) {
      logger.error('Error optimizing cache', { error });
    }

    return { filesRemoved, bytesFreed };
  }

  /**
   * Compress large files
   */
  private async compressLargeFiles(): Promise<{ filesCompressed: number; saved: number }> {
    let filesCompressed = 0;
    let saved = 0;

    try {
      const directories = [
        path.join(this.baseDirectory, 'logs'),
        path.join(this.baseDirectory, 'temp'),
      ];

      for (const dir of directories) {
        try {
          const files = await this.getFilesRecursively(dir);

          for (const file of files) {
            try {
              if (file.endsWith('.gz')) continue; // Already compressed

              const stats = await fs.stat(file);
              if (stats.size > this.config.compressionThreshold) {
                const result = await this.compressFile(file);
                if (result.saved > 0) {
                  filesCompressed++;
                  saved += result.saved;
                }
              }
            } catch (error) {
              // File might not exist or be accessible
            }
          }
        } catch (error) {
          // Directory might not exist
        }
      }
    } catch (error) {
      logger.error('Error compressing files', { error });
    }

    return { filesCompressed, saved };
  }

  /**
   * Start storage monitoring
   */
  private startStorageMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = setInterval(async () => {
      try {
        const stats = await this.getCurrentStorageStats();

        // Log warnings for high usage
        if (stats.usagePercent > 90) {
          logger.error('Critical storage usage', {
            usagePercent: stats.usagePercent,
            freeSpace: Math.round(stats.freeSpace / 1024 / 1024) + 'MB',
          });
        } else if (stats.usagePercent > 80) {
          logger.warn('High storage usage', {
            usagePercent: stats.usagePercent,
            freeSpace: Math.round(stats.freeSpace / 1024 / 1024) + 'MB',
          });
        }
      } catch (error) {
        logger.error('Error during storage monitoring', { error });
      }
    }, 3600000); // Monitor every hour
  }

  /**
   * Start auto cleanup
   */
  private startAutoCleanup(): void {
    if (!this.config.enableAutoCleanup) return;

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.cleanupInterval = setInterval(async () => {
      try {
        const result = await this.cleanupStorage();

        if (result.filesRemoved > 0 || result.filesCompressed > 0) {
          logger.info('Auto cleanup completed', {
            filesRemoved: result.filesRemoved,
            bytesFreed: result.bytesFreed,
            filesCompressed: result.filesCompressed,
          });
        }
      } catch (error) {
        logger.error('Error during auto cleanup', { error });
      }
    }, this.config.cleanupInterval);
  }

  /**
   * Stop storage optimization
   */
  stopOptimization(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * Get comprehensive storage report
   */
  async getStorageReport(): Promise<{
    currentStats: StorageStats;
    healthScore: number;
    recommendations: string[];
    storageHistory: StorageStats[];
    config: StorageOptimizationConfig;
  }> {
    const currentStats = await this.getCurrentStorageStats();

    return {
      currentStats,
      healthScore: this.getStorageHealthScore(),
      recommendations: this.getStorageOptimizationRecommendations(),
      storageHistory: this.storageHistory.slice(),
      config: this.config,
    };
  }
}

export default StorageOptimizer;
