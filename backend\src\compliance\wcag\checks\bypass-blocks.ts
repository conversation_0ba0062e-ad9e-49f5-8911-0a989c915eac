/**
 * WCAG-028: Bypass Blocks Check
 * Success Criterion: 2.4.1 Bypass Blocks (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig } from '../utils/check-template';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface BypassMechanism {
  type: 'skip-link' | 'landmark' | 'heading' | 'aria-label';
  element: string;
  selector: string;
  text?: string;
  target?: string;
  isVisible: boolean;
  isAccessible: boolean;
  position: number; // Position in tab order
}

interface BypassBlocksAnalysis {
  skipLinks: BypassMechanism[];
  landmarks: BypassMechanism[];
  headingStructure: BypassMechanism[];
  navigationBlocks: Array<{
    selector: string;
    linkCount: number;
    isRepeated: boolean;
    hasSkipMechanism: boolean;
  }>;
  mainContentStart: number; // Position where main content starts
  totalFocusableElements: number;
  elementsBeforeMain: number;
}

export class BypassBlocksCheck {
  private checkTemplate = new CheckTemplate();
  private smartCache = SmartCache.getInstance();

  async performCheck(config: CheckConfig): Promise<WcagCheckResultEnhanced> {
    const result = await this.checkTemplate.executeCheck(
      'WCAG-028',
      'Bypass Blocks',
      'operable',
      0.0815,
      'A',
      config,
      this.executeBypassBlocksCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with bypass mechanism analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-020',
        ruleName: 'Bypass Blocks',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'bypass-mechanism-analysis',
          skipLinkAnalysis: true,
          landmarkAnalysis: true,
          navigationAnalysis: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85,
        maxEvidenceItems: 30,
      }
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter(ev => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'bypass-blocks-detection',
        confidence: 0.90,
        additionalData: {
          checkType: 'navigation-structure',
          automationLevel: 'very-high',
        },
      },
    };
  }

  private async executeBypassBlocksCheck(
    page: Page,
    config: CheckConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze bypass mechanisms
    const bypassAnalysis = await page.evaluate((): BypassBlocksAnalysis => {
      const focusableSelectors = [
        'a[href]', 'button', 'input:not([type="hidden"])', 'select', 'textarea',
        '[tabindex]:not([tabindex="-1"])', '[role="button"]', '[role="link"]'
      ];

      const focusableElements = Array.from(document.querySelectorAll(focusableSelectors.join(', ')));
      
      // Find skip links
      const skipLinks: BypassMechanism[] = [];
      const skipLinkSelectors = [
        'a[href^="#"]:first-child',
        'a[href^="#"][class*="skip"]',
        'a[href^="#"][id*="skip"]',
        '.skip-link a',
        '.skip-to-content a',
        '.sr-only a[href^="#"]'
      ];

      skipLinkSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element, index) => {
          const computedStyle = window.getComputedStyle(element);
          const isVisible = computedStyle.display !== 'none' && 
                           computedStyle.visibility !== 'hidden' &&
                           !(computedStyle.position === 'absolute' && 
                             computedStyle.left === '-9999px');

          const href = element.getAttribute('href');
          const target = href?.substring(1); // Remove #
          const targetElement = target ? document.getElementById(target) : null;

          skipLinks.push({
            type: 'skip-link',
            element: element.tagName.toLowerCase(),
            selector: `${selector}:nth-of-type(${index + 1})`,
            text: element.textContent?.trim(),
            target,
            isVisible,
            isAccessible: !!targetElement,
            position: focusableElements.indexOf(element),
          });
        });
      });

      // Find landmarks
      const landmarks: BypassMechanism[] = [];
      const landmarkSelectors = ['main', 'nav', 'header', 'footer', 'aside', '[role="main"]', '[role="navigation"]'];
      
      landmarkSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element, index) => {
          landmarks.push({
            type: 'landmark',
            element: element.tagName.toLowerCase(),
            selector: `${selector}:nth-of-type(${index + 1})`,
            text: element.getAttribute('aria-label') || element.getAttribute('aria-labelledby'),
            isVisible: true,
            isAccessible: true,
            position: focusableElements.indexOf(element),
          });
        });
      });

      // Analyze heading structure
      const headingStructure: BypassMechanism[] = [];
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      headings.forEach((heading, index) => {
        headingStructure.push({
          type: 'heading',
          element: heading.tagName.toLowerCase(),
          selector: `${heading.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          text: heading.textContent?.trim(),
          isVisible: true,
          isAccessible: true,
          position: index,
        });
      });

      // Analyze navigation blocks
      const navigationBlocks: BypassBlocksAnalysis['navigationBlocks'] = [];
      const navElements = document.querySelectorAll('nav, [role="navigation"], .navigation, .nav-menu');
      
      navElements.forEach((nav, index) => {
        const links = nav.querySelectorAll('a[href]');
        const hasSkipMechanism = nav.querySelector('a[href^="#"][class*="skip"]') !== null ||
                                nav.previousElementSibling?.querySelector('a[href^="#"][class*="skip"]') !== null;

        navigationBlocks.push({
          selector: `nav:nth-of-type(${index + 1})`,
          linkCount: links.length,
          isRepeated: links.length > 3, // Assume repeated if more than 3 links
          hasSkipMechanism,
        });
      });

      // Find main content start
      const mainElement = document.querySelector('main, [role="main"]');
      const mainContentStart = mainElement ? focusableElements.indexOf(mainElement) : -1;

      // Count elements before main content
      const elementsBeforeMain = mainContentStart > 0 ? mainContentStart : 
                                 focusableElements.findIndex(el => 
                                   el.closest('main, [role="main"]') !== null
                                 );

      return {
        skipLinks,
        landmarks,
        headingStructure,
        navigationBlocks,
        mainContentStart,
        totalFocusableElements: focusableElements.length,
        elementsBeforeMain: Math.max(0, elementsBeforeMain),
      };
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const totalElements = bypassAnalysis.totalFocusableElements;

    // Check if bypass mechanisms are needed (more than 3 elements before main content)
    const needsBypassMechanism = bypassAnalysis.elementsBeforeMain > 3;

    if (needsBypassMechanism) {
      let hasValidBypassMechanism = false;

      // Check for skip links
      const validSkipLinks = bypassAnalysis.skipLinks.filter(link => 
        link.isAccessible && link.position < 3 // Should be among first 3 focusable elements
      );

      if (validSkipLinks.length > 0) {
        hasValidBypassMechanism = true;
        
        evidence.push({
          type: 'info',
          description: 'Valid skip link found',
          value: `Skip link: "${validSkipLinks[0].text}" targeting ${validSkipLinks[0].target}`,
          selector: validSkipLinks[0].selector,
          elementCount: validSkipLinks.length,
          affectedSelectors: validSkipLinks.map(link => link.selector),
          severity: 'info',
          metadata: {
            scanDuration,
            elementsAnalyzed: validSkipLinks.length,
            checkSpecificData: {
              skipLinkText: validSkipLinks[0].text,
              skipLinkTarget: validSkipLinks[0].target,
              position: validSkipLinks[0].position,
            },
          },
        });
      } else {
        // Check for landmarks as bypass mechanism
        const hasMainLandmark = bypassAnalysis.landmarks.some(landmark => 
          landmark.element === 'main' || landmark.selector.includes('[role="main"]')
        );

        if (hasMainLandmark) {
          hasValidBypassMechanism = true;
          
          evidence.push({
            type: 'info',
            description: 'Main landmark provides bypass mechanism',
            value: 'Main landmark allows screen reader users to skip to main content',
            selector: 'main, [role="main"]',
            elementCount: 1,
            affectedSelectors: ['main', '[role="main"]'],
            severity: 'info',
            metadata: {
              scanDuration,
              elementsAnalyzed: 1,
              checkSpecificData: {
                landmarkType: 'main',
                bypassMethod: 'landmark-navigation',
              },
            },
          });
        }
      }

      // If no valid bypass mechanism found
      if (!hasValidBypassMechanism) {
        score = 0;
        issues.push(`Page has ${bypassAnalysis.elementsBeforeMain} focusable elements before main content without bypass mechanism`);
        
        evidence.push({
          type: 'error',
          description: 'Missing bypass mechanism for repetitive content',
          value: `${bypassAnalysis.elementsBeforeMain} elements before main content without skip link`,
          selector: 'body',
          elementCount: bypassAnalysis.elementsBeforeMain,
          affectedSelectors: ['body'],
          severity: 'error',
          fixExample: {
            before: `<body>
  <nav>
    <a href="/home">Home</a>
    <a href="/about">About</a>
    <a href="/contact">Contact</a>
  </nav>
  <main>Main content</main>
</body>`,
            after: `<body>
  <a href="#main" class="skip-link">Skip to main content</a>
  <nav>
    <a href="/home">Home</a>
    <a href="/about">About</a>
    <a href="/contact">Contact</a>
  </nav>
  <main id="main">Main content</main>
</body>`,
            description: 'Add skip link to bypass repetitive navigation',
            codeExample: `
/* CSS for skip link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}
            `,
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/bypass-blocks.html',
              'https://webaim.org/techniques/skipnav/',
              'https://www.w3.org/WAI/ARIA/apg/practices/landmark-regions/'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: bypassAnalysis.elementsBeforeMain,
            checkSpecificData: {
              elementsBeforeMain: bypassAnalysis.elementsBeforeMain,
              hasSkipLinks: bypassAnalysis.skipLinks.length,
              hasLandmarks: bypassAnalysis.landmarks.length,
              navigationBlocks: bypassAnalysis.navigationBlocks.length,
            },
          },
        });
      }

      // Check for invalid skip links
      const invalidSkipLinks = bypassAnalysis.skipLinks.filter(link => !link.isAccessible);
      if (invalidSkipLinks.length > 0) {
        score = Math.max(score - 20, 0);
        issues.push(`Found ${invalidSkipLinks.length} skip links with invalid targets`);
        
        evidence.push({
          type: 'warning',
          description: 'Skip links with invalid targets',
          value: `Skip links targeting non-existent elements: ${invalidSkipLinks.map(link => link.target).join(', ')}`,
          selector: invalidSkipLinks[0].selector,
          elementCount: invalidSkipLinks.length,
          affectedSelectors: invalidSkipLinks.map(link => link.selector),
          severity: 'warning',
          fixExample: {
            before: `<a href="#nonexistent">Skip to content</a>`,
            after: `<a href="#main">Skip to content</a>
<main id="main">Content here</main>`,
            description: 'Ensure skip link targets exist and are properly identified',
            resources: [
              'https://webaim.org/techniques/skipnav/'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: invalidSkipLinks.length,
            checkSpecificData: {
              invalidTargets: invalidSkipLinks.map(link => link.target),
            },
          },
        });
      }
    } else {
      // Page doesn't need bypass mechanism
      evidence.push({
        type: 'info',
        description: 'Page does not require bypass mechanism',
        value: `Only ${bypassAnalysis.elementsBeforeMain} focusable elements before main content`,
        selector: 'body',
        elementCount: bypassAnalysis.elementsBeforeMain,
        affectedSelectors: ['body'],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: totalElements,
          checkSpecificData: {
            elementsBeforeMain: bypassAnalysis.elementsBeforeMain,
            totalElements,
            bypassNotRequired: true,
          },
        },
      });
    }

    // Generate recommendations
    if (score < 100) {
      recommendations.push('Add skip links to bypass repetitive navigation content');
      recommendations.push('Ensure skip links are among the first focusable elements');
      recommendations.push('Use proper landmarks (main, nav, header) to aid navigation');
      recommendations.push('Test skip links with keyboard navigation');
    } else {
      recommendations.push('Continue using proper heading structure and landmarks');
      recommendations.push('Test bypass mechanisms with screen readers');
    }

    if (bypassAnalysis.navigationBlocks.length > 1) {
      recommendations.push('Consider consolidating multiple navigation blocks');
    }

    if (bypassAnalysis.headingStructure.length === 0) {
      recommendations.push('Use proper heading structure to aid navigation');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
