/**
 * WCAG Rule 21: Pronunciation & Meaning - 3.1.6
 * 60% Automated - Manual review for context and pronunciation accuracy
 */

import { Page } from 'puppeteer';
import {
  ManualReviewConfig,
  ManualReviewTemplate,
  ManualReviewItem,
} from '../utils/manual-review-template';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

// const natural = require('natural');
// const compromise = require('compromise');

// interface PronunciationIssue {
//   word: string;
//   context: string;
//   type: 'abbreviation' | 'acronym' | 'homophone' | 'technical' | 'foreign';
//   hasDefinition: boolean;
//   hasPronunciation: boolean;
// }

export class PronunciationMeaningCheck {
  private checkTemplate = new ManualReviewTemplate();
  private smartCache = SmartCache.getInstance();

  /**
   * Perform pronunciation and meaning check - 60% automated with enhanced evidence
   */
  async performCheck(config: ManualReviewConfig) {
    const result = await this.checkTemplate.executeManualReviewCheck(
      'WCAG-065',
      'Pronunciation & Meaning',
      'understandable',
      0.03,
      'AAA',
      0.6, // 60% automation rate
      config,
      this.executePronunciationMeaningCheck.bind(this),
    );

    // Enhanced evidence standardization with pronunciation and meaning analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-065',
        ruleName: 'Pronunciation & Meaning',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.6,
          checkType: 'pronunciation-meaning-analysis',
          manualReviewRequired: result.manualReviewItems?.length > 0,
          contextualMeaningAnalysis: true,
          pronunciationAccuracyValidation: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.7,
        maxEvidenceItems: 30,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  /**
   * Execute comprehensive pronunciation and meaning analysis
   */
  private async executePronunciationMeaningCheck(page: Page, _config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Analyze abbreviations and acronyms
    const abbreviationAnalysis = await this.analyzeAbbreviations(page);

    // Analyze technical terms
    const technicalTermsAnalysis = await this.analyzeTechnicalTerms(page);

    // Analyze foreign words
    const foreignWordsAnalysis = await this.analyzeForeignWords(page);

    // Analyze pronunciation guides
    const pronunciationAnalysis = await this.analyzePronunciationGuides(page);

    // Combine all analyses
    const allAnalyses = [
      abbreviationAnalysis,
      technicalTermsAnalysis,
      foreignWordsAnalysis,
      pronunciationAnalysis,
    ];

    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedScore = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 60, // 60% automation as specified for Part 5
    };
  }

  /**
   * Analyze abbreviations and acronyms
   */
  private async analyzeAbbreviations(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const abbreviationData = await page.evaluate(() => {
        const text = document.body.textContent || '';

        // Find abbreviations and acronyms
        const abbreviationRegex = /\b[A-Z]{2,}\b/g;
        const abbreviations = text.match(abbreviationRegex) || [];

        // Find abbr elements
        const abbrElements = Array.from(document.querySelectorAll('abbr')).map((abbr) => ({
          text: abbr.textContent?.trim() || '',
          title: abbr.getAttribute('title') || '',
          hasTitle: !!abbr.getAttribute('title'),
        }));

        // Find acronym elements (deprecated but still used)
        const acronymElements = Array.from(document.querySelectorAll('acronym')).map((acronym) => ({
          text: acronym.textContent?.trim() || '',
          title: acronym.getAttribute('title') || '',
          hasTitle: !!acronym.getAttribute('title'),
        }));

        return {
          foundAbbreviations: Array.from(new Set(abbreviations)),
          abbrElements,
          acronymElements,
        };
      });

      let totalChecks =
        abbreviationData.abbrElements.length + abbreviationData.acronymElements.length;
      let passedChecks = 0;

      // Check abbr elements
      abbreviationData.abbrElements.forEach((abbr, _index) => {
        if (abbr.hasTitle) {
          passedChecks++;
          evidence.push({
            type: 'info',
            description: `Abbreviation "${abbr.text}" has definition`,
            value: `Abbreviation: ${abbr.text}, Definition: ${abbr.title}, Has definition: true`,
            element: 'abbr',
          });
        } else {
          issues.push(`Abbreviation "${abbr.text}" lacks definition`);
          evidence.push({
            type: 'warning',
            description: `Abbreviation "${abbr.text}" needs definition`,
            value: `Abbreviation: ${abbr.text}, Has definition: false`,
            element: 'abbr',
          });
          recommendations.push(`Add title attribute to abbreviation "${abbr.text}"`);
        }
      });

      // Check acronym elements
      abbreviationData.acronymElements.forEach((acronym, _index) => {
        if (acronym.hasTitle) {
          passedChecks++;
          evidence.push({
            type: 'info',
            description: `Acronym "${acronym.text}" has definition`,
            value: `Acronym: ${acronym.text}, Definition: ${acronym.title}, Has definition: true`,
            element: 'acronym',
          });
        } else {
          issues.push(`Acronym "${acronym.text}" lacks definition`);
          evidence.push({
            type: 'warning',
            description: `Acronym "${acronym.text}" needs definition`,
            value: `Acronym: ${acronym.text}, Has definition: false`,
            element: 'acronym',
          });
          recommendations.push(`Add title attribute to acronym "${acronym.text}"`);
        }
      });

      // Analyze found abbreviations in text
      if (abbreviationData.foundAbbreviations.length > 0) {
        const unmarkedAbbreviations = abbreviationData.foundAbbreviations.filter(
          (abbr) =>
            !abbreviationData.abbrElements.some((el) => el.text === abbr) &&
            !abbreviationData.acronymElements.some((el) => el.text === abbr),
        );

        if (unmarkedAbbreviations.length > 0) {
          manualReviewItems.push({
            selector: 'text content',
            description: 'Review unmarked abbreviations for definition needs',
            automatedFindings: `Found ${unmarkedAbbreviations.length} potential unmarked abbreviations: ${unmarkedAbbreviations.slice(0, 10).join(', ')}`,
            reviewRequired:
              'Review abbreviations to determine if they need definitions or expansions',
            priority: 'medium',
            estimatedTime: 6,
            type: 'unmarked_abbreviations',
            element: 'text content',
            context: `Unmarked abbreviations: ${unmarkedAbbreviations.slice(0, 10).join(', ')}, Total: ${unmarkedAbbreviations.length}`,
          });

          evidence.push({
            type: 'warning',
            description: `Found ${unmarkedAbbreviations.length} potential unmarked abbreviations`,
            value: `Unmarked count: ${unmarkedAbbreviations.length}, Examples: ${unmarkedAbbreviations.slice(0, 5).join(', ')}`,
            element: 'text content',
          });
        }
      }

      if (totalChecks === 0) {
        totalChecks = 1;
        passedChecks = 1;
        evidence.push({
          type: 'info',
          description: 'No marked abbreviations or acronyms found',
          value: 'Marked abbreviations found: false',
          element: 'page',
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing abbreviations',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'abbr, acronym',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze abbreviations'],
        recommendations: ['Check abbreviations manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze technical terms
   */
  private async analyzeTechnicalTerms(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const textContent = await page.evaluate(() => document.body.textContent || '');

      // Common technical term patterns
      const technicalPatterns = [
        /\b\w+(?:API|SDK|URL|HTTP|HTTPS|JSON|XML|CSS|HTML|SQL)\b/gi,
        /\b(?:algorithm|database|server|client|framework|library|protocol)\b/gi,
        /\b\w*(?:tion|sion|ment|ness|ity|ism)\b/gi, // Complex suffixes
      ];

      const technicalTerms: string[] = [];
      technicalPatterns.forEach((pattern) => {
        const matches = textContent.match(pattern) || [];
        technicalTerms.push(...matches);
      });

      const uniqueTerms = Array.from(new Set(technicalTerms.map((term) => term.toLowerCase())));

      const totalChecks = 1;
      let passedChecks = 0;

      if (uniqueTerms.length === 0) {
        passedChecks++;
        evidence.push({
          type: 'info',
          description: 'No technical terms detected',
          value: 'Technical terms found: false',
          element: 'text content',
        });
      } else {
        evidence.push({
          type: 'warning',
          description: `Found ${uniqueTerms.length} potential technical terms`,
          value: `Technical terms count: ${uniqueTerms.length}, Examples: ${uniqueTerms.slice(0, 10).join(', ')}`,
          element: 'text content',
        });

        manualReviewItems.push({
          selector: 'text content',
          description: 'Review technical terms for definition and pronunciation needs',
          automatedFindings: `Found ${uniqueTerms.length} technical terms: ${uniqueTerms.slice(0, 20).join(', ')}`,
          reviewRequired:
            'Review technical terms to determine if they need definitions or pronunciation guidance',
          priority: 'medium',
          estimatedTime: 8,
          type: 'technical_terms_review',
          element: 'text content',
          context: `Technical terms count: ${uniqueTerms.length}, Examples: ${uniqueTerms.slice(0, 20).join(', ')}`,
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing technical terms',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'text content',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze technical terms'],
        recommendations: ['Check technical terms manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze foreign words and phrases
   */
  private async analyzeForeignWords(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const foreignWordData = await page.evaluate(() => {
        // Look for lang attributes
        const elementsWithLang = Array.from(document.querySelectorAll('[lang]')).map((el) => ({
          tagName: el.tagName.toLowerCase(),
          lang: el.getAttribute('lang') || '',
          text: el.textContent?.trim().substring(0, 100) || '',
        }));

        // Common foreign phrases that might appear in English text
        const text = document.body.textContent || '';
        const foreignPhrases = [
          'et al',
          'e.g.',
          'i.e.',
          'etc.',
          'vs.',
          'circa',
          'per se',
          'ad hoc',
          'de facto',
          'status quo',
          'vice versa',
          'pro bono',
          'quid pro quo',
        ];

        const foundForeignPhrases = foreignPhrases.filter((phrase) =>
          text.toLowerCase().includes(phrase.toLowerCase()),
        );

        return {
          elementsWithLang,
          foundForeignPhrases,
        };
      });

      let totalChecks = foreignWordData.elementsWithLang.length;
      let passedChecks = foreignWordData.elementsWithLang.length; // Assume marked foreign text is correct

      foreignWordData.elementsWithLang.forEach((element, _index) => {
        evidence.push({
          type: 'info',
          description: `Foreign language content marked with lang="${element.lang}"`,
          value: `Language: ${element.lang}, Text: ${element.text}, Is marked: true`,
          element: element.tagName,
        });
      });

      if (foreignWordData.foundForeignPhrases.length > 0) {
        manualReviewItems.push({
          selector: 'text content',
          description: 'Review foreign phrases for pronunciation guidance needs',
          automatedFindings: `Found ${foreignWordData.foundForeignPhrases.length} foreign phrases: ${foreignWordData.foundForeignPhrases.join(', ')}`,
          reviewRequired: 'Review foreign phrases to determine if they need pronunciation guidance',
          priority: 'low',
          estimatedTime: 4,
          type: 'foreign_phrases_review',
          element: 'text content',
          context: `Foreign phrases count: ${foreignWordData.foundForeignPhrases.length}, Phrases: ${foreignWordData.foundForeignPhrases.join(', ')}`,
        });

        evidence.push({
          type: 'info',
          description: `Found ${foreignWordData.foundForeignPhrases.length} common foreign phrases`,
          value: `Foreign phrases count: ${foreignWordData.foundForeignPhrases.length}, Phrases: ${foreignWordData.foundForeignPhrases.join(', ')}`,
          element: 'text content',
        });
      }

      if (totalChecks === 0) {
        totalChecks = 1;
        passedChecks = 1;
        evidence.push({
          type: 'info',
          description: 'No foreign language content detected',
          value: 'Foreign content found: false',
          element: 'page',
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing foreign words',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: '[lang]',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze foreign words'],
        recommendations: ['Check foreign words manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze pronunciation guides
   */
  private async analyzePronunciationGuides(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const pronunciationData = await page.evaluate(() => {
        // Look for pronunciation guides
        const pronunciationElements = Array.from(
          document.querySelectorAll('[data-pronunciation], .pronunciation, .phonetic'),
        ).map((el) => ({
          tagName: el.tagName.toLowerCase(),
          text: el.textContent?.trim() || '',
          pronunciation: el.getAttribute('data-pronunciation') || '',
        }));

        // Look for phonetic notation patterns
        const text = document.body.textContent || '';
        const phoneticPatterns = [
          /\/[^/]+\//g, // IPA notation
          /\[[^\]]+\]/g, // Bracket notation
          /\([^)]*pronunciation[^)]*\)/gi, // Pronunciation in parentheses
        ];

        const phoneticNotations: string[] = [];
        phoneticPatterns.forEach((pattern) => {
          const matches = text.match(pattern) || [];
          phoneticNotations.push(...matches);
        });

        return {
          pronunciationElements,
          phoneticNotations: Array.from(new Set(phoneticNotations)),
        };
      });

      const totalChecks = 1;
      let passedChecks = 0;

      const hasPronunciationGuides =
        pronunciationData.pronunciationElements.length > 0 ||
        pronunciationData.phoneticNotations.length > 0;

      if (hasPronunciationGuides) {
        passedChecks++;
        evidence.push({
          type: 'info',
          description: 'Pronunciation guides found',
          value: `Pronunciation elements: ${pronunciationData.pronunciationElements.length}, Phonetic notations: ${pronunciationData.phoneticNotations.length}`,
          element: 'pronunciation elements',
        });

        // Add manual review for pronunciation accuracy
        manualReviewItems.push({
          selector: 'pronunciation elements',
          description: 'Verify pronunciation guides are accurate and helpful',
          automatedFindings: `Found ${pronunciationData.pronunciationElements.length} pronunciation elements and ${pronunciationData.phoneticNotations.length} phonetic notations`,
          reviewRequired: 'Verify that pronunciation guides are accurate and helpful for users',
          priority: 'medium',
          estimatedTime: 5,
          type: 'pronunciation_accuracy',
          element: 'pronunciation elements',
          context: `Pronunciation elements: ${pronunciationData.pronunciationElements.length}, Phonetic notations: ${pronunciationData.phoneticNotations.slice(0, 10).join(', ')}`,
        });
      } else {
        evidence.push({
          type: 'info',
          description: 'No pronunciation guides found',
          value: 'Pronunciation guides found: false',
          element: 'page',
        });

        // This is not necessarily a failure - only needed when pronunciation is ambiguous
        passedChecks++;
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing pronunciation guides',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'pronunciation elements',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze pronunciation guides'],
        recommendations: ['Check pronunciation guides manually'],
        manualReviewItems,
      };
    }
  }
}
