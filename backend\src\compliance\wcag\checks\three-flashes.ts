/**
 * WCAG-046: Three Flashes Check
 * Success Criterion: 2.3.1 Three Flashes or Below Threshold (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig } from '../utils/check-template';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartC<PERSON> from '../utils/smart-cache';

export class ThreeFlashesCheck {
  private checkTemplate = new CheckTemplate();
  private smartCache = SmartCache.getInstance();

  async performCheck(config: CheckConfig): Promise<WcagCheckResultEnhanced> {
    const result = await this.checkTemplate.executeCheck(
      'WCAG-046',
      'Three Flashes or Below Threshold',
      'operable',
      0.0458,
      'A',
      config,
      this.executeThreeFlashesCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with flash detection analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-030',
        ruleName: 'Three Flashes or Below Threshold',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.8,
          checkType: 'flash-detection-analysis',
          animationAnalysis: true,
          flashThresholdValidation: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 20,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeThreeFlashesCheck(
    page: Page,
    config: CheckConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Check for flashing content
    const flashingAnalysis = await page.evaluate(() => {
      const flashingElements: Array<{
        type: string;
        element: string;
        selector: string;
        flashRate?: number;
        animationName?: string;
        description: string;
        severity: 'high' | 'medium' | 'low';
      }> = [];

      // Check for CSS animations that might cause flashing
      const animatedElements = document.querySelectorAll('*');
      animatedElements.forEach((element, index) => {
        const computedStyle = window.getComputedStyle(element);
        const animationName = computedStyle.animationName;
        const animationDuration = parseFloat(computedStyle.animationDuration) || 0;
        const animationIterationCount = computedStyle.animationIterationCount;
        
        if (animationName !== 'none' && animationDuration > 0) {
          // Calculate potential flash rate
          const flashRate = animationIterationCount === 'infinite' ? 
            (1 / animationDuration) : 0;
          
          // Check for potentially problematic animations
          if (flashRate > 3 || 
              animationName.toLowerCase().includes('flash') ||
              animationName.toLowerCase().includes('blink') ||
              animationName.toLowerCase().includes('strobe')) {
            
            flashingElements.push({
              type: 'css_animation',
              element: element.tagName.toLowerCase(),
              selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
              flashRate,
              animationName,
              description: `CSS animation "${animationName}" with potential flash rate: ${flashRate.toFixed(2)}/sec`,
              severity: flashRate > 3 ? 'high' : 'medium',
            });
          }
        }
      });

      // Check for blink elements
      const blinkElements = document.querySelectorAll('blink, .blink');
      blinkElements.forEach((element, index) => {
        flashingElements.push({
          type: 'blink_element',
          element: element.tagName.toLowerCase(),
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          description: 'Blinking element detected',
          severity: 'high',
        });
      });

      // Check for elements with flashing CSS classes or styles
      const flashingSelectors = [
        '[style*="blink"]',
        '.flash', '.flashing', '.strobe', '.pulse',
        '[class*="flash"]', '[class*="blink"]', '[class*="strobe"]'
      ];
      
      flashingSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element, index) => {
          flashingElements.push({
            type: 'flashing_style',
            element: element.tagName.toLowerCase(),
            selector: `${selector}:nth-of-type(${index + 1})`,
            description: `Element with flashing style: ${selector}`,
            severity: 'medium',
          });
        });
      });

      // Check for video elements that might contain flashing content
      const videoElements = document.querySelectorAll('video');
      videoElements.forEach((element, index) => {
        // Note: We can't analyze video content directly, but we can flag for manual review
        flashingElements.push({
          type: 'video_content',
          element: 'video',
          selector: `video:nth-of-type(${index + 1})`,
          description: 'Video element detected - requires manual review for flashing content',
          severity: 'low',
        });
      });

      // Check for canvas elements that might contain flashing animations
      const canvasElements = document.querySelectorAll('canvas');
      canvasElements.forEach((element, index) => {
        flashingElements.push({
          type: 'canvas_content',
          element: 'canvas',
          selector: `canvas:nth-of-type(${index + 1})`,
          description: 'Canvas element detected - requires manual review for flashing content',
          severity: 'low',
        });
      });

      // Check for JavaScript-based flashing (common patterns)
      const scripts = document.querySelectorAll('script');
      let hasFlashingJS = false;
      scripts.forEach((script) => {
        const content = script.textContent || '';
        if (content.includes('setInterval') && 
            (content.includes('flash') || content.includes('blink') || 
             content.includes('strobe') || content.includes('opacity'))) {
          hasFlashingJS = true;
        }
      });

      if (hasFlashingJS) {
        flashingElements.push({
          type: 'javascript_flashing',
          element: 'script',
          selector: 'script',
          description: 'JavaScript code detected that may cause flashing',
          severity: 'medium',
        });
      }

      return {
        flashingElements,
        totalElements: flashingElements.length,
        highSeverityCount: flashingElements.filter(el => el.severity === 'high').length,
        mediumSeverityCount: flashingElements.filter(el => el.severity === 'medium').length,
        lowSeverityCount: flashingElements.filter(el => el.severity === 'low').length,
      };
    });

    let score = 100;
    const elementCount = flashingAnalysis.totalElements;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      // High severity issues (definite violations) - major score reduction
      if (flashingAnalysis.highSeverityCount > 0) {
        score = Math.max(0, score - (flashingAnalysis.highSeverityCount * 40));
        issues.push(`${flashingAnalysis.highSeverityCount} elements with high-risk flashing detected`);
      }
      
      // Medium severity issues (potential violations) - moderate score reduction
      if (flashingAnalysis.mediumSeverityCount > 0) {
        score = Math.max(0, score - (flashingAnalysis.mediumSeverityCount * 20));
        issues.push(`${flashingAnalysis.mediumSeverityCount} elements with potential flashing detected`);
      }
      
      // Low severity issues (requires manual review) - minor score reduction
      if (flashingAnalysis.lowSeverityCount > 0) {
        score = Math.max(0, score - (flashingAnalysis.lowSeverityCount * 5));
        issues.push(`${flashingAnalysis.lowSeverityCount} elements require manual review for flashing content`);
      }

      flashingAnalysis.flashingElements.forEach((element) => {
        evidence.push({
          type: 'code',
          description: `Potential flashing content: ${element.description}`,
          value: element.flashRate ? 
            `Flash rate: ${element.flashRate.toFixed(2)} flashes/second` : 
            'Flashing content detected',
          selector: element.selector,
          elementCount: 1,
          affectedSelectors: [element.selector],
          severity: element.severity === 'high' ? 'error' : 
                   element.severity === 'medium' ? 'warning' : 'info',
          fixExample: {
            before: this.getBeforeExample(element.type),
            after: this.getAfterExample(element.type),
            description: this.getFixDescription(element.type),
            codeExample: this.getCodeExample(element.type),
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/three-flashes-or-below-threshold.html',
              'https://www.w3.org/WAI/WCAG21/Techniques/G19',
              'https://www.w3.org/WAI/WCAG21/Techniques/G176'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              flashingType: element.type,
              severity: element.severity,
              flashRate: element.flashRate || 0,
              animationName: element.animationName || 'unknown',
            },
          },
        });
      });

      recommendations.push('Remove or modify content that flashes more than 3 times per second');
      recommendations.push('Use fade transitions instead of abrupt flashing');
      recommendations.push('Provide user controls to disable animations');
      recommendations.push('Test video and canvas content manually for flashing');
      recommendations.push('Consider users with photosensitive epilepsy');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(type: string): string {
    switch (type) {
      case 'css_animation':
        return '@keyframes flash { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }';
      case 'blink_element':
        return '<blink>Flashing text</blink>';
      case 'flashing_style':
        return '<div class="flash">Flashing content</div>';
      case 'javascript_flashing':
        return 'setInterval(() => { element.style.opacity = element.style.opacity === "0" ? "1" : "0"; }, 100);';
      default:
        return 'Flashing content';
    }
  }

  private getAfterExample(type: string): string {
    switch (type) {
      case 'css_animation':
        return '@keyframes gentle-fade { 0% { opacity: 0.8; } 100% { opacity: 1; } }';
      case 'blink_element':
        return '<span class="highlight">Important text</span>';
      case 'flashing_style':
        return '<div class="gentle-highlight">Non-flashing content</div>';
      case 'javascript_flashing':
        return '// Use gentle transitions instead of rapid flashing\nelement.style.transition = "opacity 1s ease";';
      default:
        return 'Non-flashing content';
    }
  }

  private getFixDescription(type: string): string {
    switch (type) {
      case 'css_animation':
        return 'Modify animation to flash 3 times per second or less';
      case 'blink_element':
        return 'Replace blink element with static highlighting';
      case 'flashing_style':
        return 'Use gentle transitions instead of rapid flashing';
      case 'javascript_flashing':
        return 'Reduce flash frequency to 3 times per second or less';
      case 'video_content':
        return 'Review video content for flashing sequences';
      case 'canvas_content':
        return 'Review canvas animations for flashing content';
      default:
        return 'Eliminate or reduce flashing to safe levels';
    }
  }

  private getCodeExample(type: string): string {
    switch (type) {
      case 'css_animation':
        return `
/* Before: Rapid flashing animation */
@keyframes flash {
  0% { opacity: 1; }
  50% { opacity: 0; }
  100% { opacity: 1; }
}
.flashing { animation: flash 0.2s infinite; }

/* After: Gentle, safe animation */
@keyframes gentle-pulse {
  0% { opacity: 0.8; }
  50% { opacity: 1; }
  100% { opacity: 0.8; }
}
.gentle { animation: gentle-pulse 2s ease-in-out infinite; }
        `;
      case 'blink_element':
        return `
<!-- Before: Blinking element -->
<blink>Important message</blink>

<!-- After: Static highlighting -->
<span class="highlight" style="background-color: yellow; font-weight: bold;">
  Important message
</span>
        `;
      case 'javascript_flashing':
        return `
// Before: Rapid flashing
setInterval(() => {
  element.style.opacity = element.style.opacity === "0" ? "1" : "0";
}, 100); // 10 flashes per second - DANGEROUS

// After: Safe, gentle transition
element.style.transition = "opacity 1s ease-in-out";
setInterval(() => {
  element.style.opacity = element.style.opacity === "0.5" ? "1" : "0.5";
}, 2000); // 0.5 flashes per second - SAFE
        `;
      default:
        return 'Use gentle transitions and avoid rapid flashing';
    }
  }
}
