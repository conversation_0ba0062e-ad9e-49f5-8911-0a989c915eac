/**
 * WCAG-038: Language of Parts Check
 * Success Criterion: 3.1.2 Language of Parts (Level AA)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface LanguageElement {
  selector: string;
  tagName: string;
  text: string;
  detectedLanguage?: string;
  declaredLanguage?: string;
  hasLangAttribute: boolean;
  isCorrectLanguage: boolean;
  confidence: number;
  textLength: number;
  issues: string[];
}

interface LanguagePartsAnalysis {
  pageLanguage: string;
  elementsWithDifferentLanguage: LanguageElement[];
  elementsWithLangAttribute: LanguageElement[];
  elementsWithoutLangAttribute: LanguageElement[];
  totalElementsAnalyzed: number;
  elementsNeedingLangAttribute: number;
  elementsWithCorrectLang: number;
  elementsWithIncorrectLang: number;
}

export interface LanguagePartsConfig extends EnhancedCheckConfig {
  enableAILanguageDetection?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableAccessibilityPatterns?: boolean;
}

export class LanguagePartsCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();

  // Common language patterns for basic detection
  private readonly languagePatterns = {
    'en': [
      /\b(the|and|or|but|in|on|at|to|for|of|with|by)\b/gi,
      /\b(this|that|these|those|what|where|when|why|how)\b/gi,
    ],
    'es': [
      /\b(el|la|los|las|y|o|pero|en|de|con|por|para)\b/gi,
      /\b(este|esta|estos|estas|que|donde|cuando|por que|como)\b/gi,
    ],
    'fr': [
      /\b(le|la|les|et|ou|mais|dans|de|avec|par|pour)\b/gi,
      /\b(ce|cette|ces|que|où|quand|pourquoi|comment)\b/gi,
    ],
    'de': [
      /\b(der|die|das|und|oder|aber|in|von|mit|für)\b/gi,
      /\b(dieser|diese|dieses|was|wo|wann|warum|wie)\b/gi,
    ],
    'it': [
      /\b(il|la|i|le|e|o|ma|in|di|con|per)\b/gi,
      /\b(questo|questa|questi|che|dove|quando|perché|come)\b/gi,
    ],
  };

  // Valid language codes (ISO 639-1 and some common extensions)
  private readonly validLanguageCodes = [
    'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh', 'ar', 'hi',
    'en-US', 'en-GB', 'es-ES', 'es-MX', 'fr-FR', 'fr-CA', 'de-DE', 'it-IT',
    'pt-BR', 'pt-PT', 'zh-CN', 'zh-TW', 'ar-SA', 'ja-JP', 'ko-KR'
  ];

  async performCheck(config: LanguagePartsConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: LanguagePartsConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAILanguageDetection: true,
      enableContentQualityAnalysis: true,
      enableAccessibilityPatterns: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-038',
      'Language of Parts',
      'understandable',
      0.0815,
      'AA',
      enhancedConfig,
      this.executeLanguagePartsCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with language detection analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-038',
        ruleName: 'Language of Parts',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.8,
          checkType: 'language-detection-analysis',
          languagePatternAnalysis: true,
          langAttributeValidation: true,
          aiLanguageDetection: enhancedConfig.enableAILanguageDetection,
          contentQualityAnalysis: enhancedConfig.enableContentQualityAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 35,
      }
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter(ev => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'language-parts-analysis',
        confidence: 0.65,
        additionalData: {
          checkType: 'language-accessibility',
          automationLevel: 'medium',
        },
      },
    };
  }

  private async executeLanguagePartsCheck(
    page: Page,
    config: LanguagePartsConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze language of parts within the page
    const languageAnalysis = await page.evaluate(() => {
      const languagePatterns = {
        'en': [
          /\b(the|and|or|but|in|on|at|to|for|of|with|by)\b/gi,
          /\b(this|that|these|those|what|where|when|why|how)\b/gi,
        ],
        'es': [
          /\b(el|la|los|las|y|o|pero|en|de|con|por|para)\b/gi,
          /\b(este|esta|estos|estas|que|donde|cuando|por que|como)\b/gi,
        ],
        'fr': [
          /\b(le|la|les|et|ou|mais|dans|de|avec|par|pour)\b/gi,
          /\b(ce|cette|ces|que|où|quand|pourquoi|comment)\b/gi,
        ],
        'de': [
          /\b(der|die|das|und|oder|aber|in|von|mit|für)\b/gi,
          /\b(dieser|diese|dieses|was|wo|wann|warum|wie)\b/gi,
        ],
        'it': [
          /\b(il|la|i|le|e|o|ma|in|di|con|per)\b/gi,
          /\b(questo|questa|questi|che|dove|quando|perché|come)\b/gi,
        ],
      };

      const validLanguageCodes = [
        'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh', 'ar', 'hi',
        'en-US', 'en-GB', 'es-ES', 'es-MX', 'fr-FR', 'fr-CA', 'de-DE', 'it-IT',
        'pt-BR', 'pt-PT', 'zh-CN', 'zh-TW', 'ar-SA', 'ja-JP', 'ko-KR'
      ];

      // Get page language
      const htmlElement = document.documentElement;
      const pageLanguage = htmlElement.getAttribute('lang') || 'en';

      // Helper function to detect language
      function detectLanguage(text: string): { language: string; confidence: number } {
        const cleanText = text.trim().toLowerCase();
        if (cleanText.length < 10) {
          return { language: 'unknown', confidence: 0 };
        }

        const scores: Record<string, number> = {};
        
        for (const [lang, patterns] of Object.entries(languagePatterns)) {
          let score = 0;
          for (const pattern of patterns) {
            const matches = cleanText.match(pattern) || [];
            score += matches.length;
          }
          scores[lang] = score / cleanText.split(/\s+/).length; // Normalize by word count
        }

        const bestMatch = Object.entries(scores).reduce((a, b) => 
          scores[a[0]] > scores[b[0]] ? a : b
        );

        return {
          language: bestMatch[1] > 0.1 ? bestMatch[0] : 'unknown',
          confidence: Math.min(bestMatch[1] * 10, 1) // Scale to 0-1
        };
      }

      // Find elements with lang attributes
      const elementsWithLang = document.querySelectorAll('[lang]');
      
      // Find elements with significant text content that might need lang attributes
      const textElements = document.querySelectorAll('p, div, span, blockquote, q, cite, article, section');
      
      const elementsWithDifferentLanguage: LanguageElement[] = [];
      const elementsWithLangAttribute: LanguageElement[] = [];
      const elementsWithoutLangAttribute: LanguageElement[] = [];

      // Analyze elements with lang attributes
      elementsWithLang.forEach((element, index) => {
        const text = element.textContent?.trim() || '';
        const declaredLanguage = element.getAttribute('lang') || '';
        
        if (text.length > 10) {
          const detection = detectLanguage(text);
          const isCorrectLanguage = detection.language === 'unknown' || 
                                   detection.language === declaredLanguage.split('-')[0] ||
                                   detection.confidence < 0.5;

          const issues: string[] = [];
          
          if (!validLanguageCodes.includes(declaredLanguage)) {
            issues.push('Invalid language code');
          }
          
          if (!isCorrectLanguage && detection.confidence > 0.5) {
            issues.push(`Declared as ${declaredLanguage} but appears to be ${detection.language}`);
          }

          const languageElement: LanguageElement = {
            selector: `[lang="${declaredLanguage}"]:nth-of-type(${index + 1})`,
            tagName: element.tagName.toLowerCase(),
            text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
            detectedLanguage: detection.language,
            declaredLanguage,
            hasLangAttribute: true,
            isCorrectLanguage,
            confidence: detection.confidence,
            textLength: text.length,
            issues,
          };

          elementsWithLangAttribute.push(languageElement);
          
          if (declaredLanguage !== pageLanguage) {
            elementsWithDifferentLanguage.push(languageElement);
          }
        }
      });

      // Analyze text elements without lang attributes for potential language differences
      textElements.forEach((element, index) => {
        // Skip if element already has lang attribute or is inside an element with lang
        if (element.hasAttribute('lang') || element.closest('[lang]')) {
          return;
        }

        const text = element.textContent?.trim() || '';
        
        if (text.length > 30) { // Only analyze substantial text
          const detection = detectLanguage(text);
          
          // If detected language differs significantly from page language and confidence is high
          if (detection.language !== 'unknown' && 
              detection.language !== pageLanguage.split('-')[0] && 
              detection.confidence > 0.6) {
            
            const issues: string[] = [];
            issues.push(`Text appears to be in ${detection.language} but no lang attribute specified`);

            const languageElement: LanguageElement = {
              selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
              tagName: element.tagName.toLowerCase(),
              text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
              detectedLanguage: detection.language,
              declaredLanguage: undefined,
              hasLangAttribute: false,
              isCorrectLanguage: false,
              confidence: detection.confidence,
              textLength: text.length,
              issues,
            };

            elementsWithoutLangAttribute.push(languageElement);
            elementsWithDifferentLanguage.push(languageElement);
          }
        }
      });

      const totalElementsAnalyzed = elementsWithLangAttribute.length + elementsWithoutLangAttribute.length;
      const elementsNeedingLangAttribute = elementsWithoutLangAttribute.length;
      const elementsWithCorrectLang = elementsWithLangAttribute.filter(el => el.isCorrectLanguage).length;
      const elementsWithIncorrectLang = elementsWithLangAttribute.filter(el => !el.isCorrectLanguage).length;

      return {
        pageLanguage,
        elementsWithDifferentLanguage,
        elementsWithLangAttribute,
        elementsWithoutLangAttribute,
        totalElementsAnalyzed,
        elementsNeedingLangAttribute,
        elementsWithCorrectLang,
        elementsWithIncorrectLang,
      };
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const totalElements = languageAnalysis.totalElementsAnalyzed;

    if (totalElements === 0) {
      // No elements with different languages found
      evidence.push({
        type: 'info',
        description: 'No content in different languages detected',
        value: `Page appears to be entirely in ${languageAnalysis.pageLanguage}`,
        selector: 'html',
        elementCount: 0,
        affectedSelectors: [],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: 0,
          checkSpecificData: {
            pageLanguage: languageAnalysis.pageLanguage,
            totalElementsAnalyzed: 0,
          },
        },
      });

      return {
        score: 100,
        maxScore: 100,
        evidence,
        issues,
        recommendations: ['Ensure any future content in different languages includes lang attributes'],
      };
    }

    // Analyze elements without lang attributes that need them
    if (languageAnalysis.elementsNeedingLangAttribute > 0) {
      const failureRate = languageAnalysis.elementsNeedingLangAttribute / totalElements;
      score = Math.max(0, Math.round(100 * (1 - failureRate)));

      languageAnalysis.elementsWithoutLangAttribute.forEach((element) => {
        issues.push(`${element.tagName} element with ${element.detectedLanguage} content missing lang attribute`);
        
        evidence.push({
          type: 'error',
          description: 'Content in different language missing lang attribute',
          value: `${element.tagName} contains ${element.detectedLanguage} text but has no lang attribute`,
          selector: element.selector,
          elementCount: 1,
          affectedSelectors: [element.selector],
          severity: 'error',
          fixExample: {
            before: `<${element.tagName}>${element.text}</${element.tagName}>`,
            after: `<${element.tagName} lang="${element.detectedLanguage}">${element.text}</${element.tagName}>`,
            description: 'Add lang attribute to content in different languages',
            codeExample: `
<!-- Example: Mixed language content -->
<p>Welcome to our site. <span lang="es">Bienvenidos a nuestro sitio</span></p>

<!-- Example: Quote in different language -->
<blockquote lang="fr">
  <p>Bonjour le monde! Comment allez-vous?</p>
</blockquote>

<!-- Example: Article with different language sections -->
<article>
  <h2>International Greetings</h2>
  <p>Here are greetings in different languages:</p>
  <ul>
    <li lang="es">Hola mundo</li>
    <li lang="fr">Bonjour le monde</li>
    <li lang="de">Hallo Welt</li>
    <li lang="it">Ciao mondo</li>
  </ul>
</article>

<!-- Example: Inline foreign phrases -->
<p>The French phrase <span lang="fr">c'est la vie</span> means "that's life".</p>
            `,
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/language-of-parts.html',
              'https://webaim.org/techniques/language/',
              'https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/lang'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              tagName: element.tagName,
              detectedLanguage: element.detectedLanguage,
              confidence: element.confidence,
              textLength: element.textLength,
              pageLanguage: languageAnalysis.pageLanguage,
              issues: element.issues,
            },
          },
        });
      });
    }

    // Analyze elements with incorrect lang attributes
    if (languageAnalysis.elementsWithIncorrectLang > 0) {
      score = Math.max(score - (languageAnalysis.elementsWithIncorrectLang * 10), 0);
      
      languageAnalysis.elementsWithLangAttribute
        .filter(element => !element.isCorrectLanguage)
        .forEach((element) => {
          issues.push(`${element.tagName} element has incorrect lang attribute`);
          
          evidence.push({
            type: 'warning',
            description: 'Element with incorrect lang attribute',
            value: `${element.tagName} declared as ${element.declaredLanguage} but appears to be ${element.detectedLanguage}`,
            selector: element.selector,
            elementCount: 1,
            affectedSelectors: [element.selector],
            severity: 'warning',
            fixExample: {
              before: `<${element.tagName} lang="${element.declaredLanguage}">${element.text}</${element.tagName}>`,
              after: `<${element.tagName} lang="${element.detectedLanguage}">${element.text}</${element.tagName}>`,
              description: 'Correct the lang attribute to match the actual language',
              resources: [
                'https://www.w3.org/WAI/WCAG21/Understanding/language-of-parts.html'
              ]
            },
            metadata: {
              scanDuration,
              elementsAnalyzed: 1,
              checkSpecificData: {
                tagName: element.tagName,
                declaredLanguage: element.declaredLanguage,
                detectedLanguage: element.detectedLanguage,
                confidence: element.confidence,
                issues: element.issues,
              },
            },
          });
        });
    }

    // Add positive evidence for correct lang attributes
    if (languageAnalysis.elementsWithCorrectLang > 0) {
      evidence.push({
        type: 'info',
        description: 'Elements with correct lang attributes found',
        value: `${languageAnalysis.elementsWithCorrectLang} elements have appropriate lang attributes`,
        selector: '[lang]',
        elementCount: languageAnalysis.elementsWithCorrectLang,
        affectedSelectors: ['[lang]'],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: languageAnalysis.elementsWithCorrectLang,
          checkSpecificData: {
            elementsWithCorrectLang: languageAnalysis.elementsWithCorrectLang,
          },
        },
      });
    }

    // Add summary evidence
    evidence.push({
      type: score >= 80 ? 'info' : 'warning',
      description: 'Language of parts analysis summary',
      value: `${totalElements} elements analyzed: ${languageAnalysis.elementsWithLangAttribute.length} with lang attributes, ${languageAnalysis.elementsNeedingLangAttribute} missing lang attributes`,
      selector: '[lang], p, div, span, blockquote',
      elementCount: totalElements,
      affectedSelectors: ['[lang]', 'p', 'div', 'span', 'blockquote'],
      severity: score >= 80 ? 'info' : 'warning',
      metadata: {
        scanDuration,
        elementsAnalyzed: totalElements,
        checkSpecificData: {
          pageLanguage: languageAnalysis.pageLanguage,
          totalElementsAnalyzed: languageAnalysis.totalElementsAnalyzed,
          elementsWithLangAttribute: languageAnalysis.elementsWithLangAttribute.length,
          elementsNeedingLangAttribute: languageAnalysis.elementsNeedingLangAttribute,
          elementsWithCorrectLang: languageAnalysis.elementsWithCorrectLang,
          elementsWithIncorrectLang: languageAnalysis.elementsWithIncorrectLang,
          complianceRate: totalElements > 0 
            ? (languageAnalysis.elementsWithCorrectLang / totalElements * 100).toFixed(1)
            : '100',
        },
      },
    });

    // Generate recommendations
    if (languageAnalysis.elementsNeedingLangAttribute > 0) {
      recommendations.push('Add lang attributes to content in languages different from the page language');
      recommendations.push('Use valid ISO 639-1 language codes (e.g., "en", "es", "fr")');
      recommendations.push('Include language region codes when appropriate (e.g., "en-US", "es-MX")');
      recommendations.push('Test with screen readers to ensure proper pronunciation');
    } else {
      recommendations.push('Continue using lang attributes for content in different languages');
      recommendations.push('Verify lang attributes are accurate for the content language');
    }

    if (languageAnalysis.elementsWithIncorrectLang > 0) {
      recommendations.push('Review and correct inaccurate lang attributes');
      recommendations.push('Ensure lang attributes match the actual language of the content');
    }

    if (languageAnalysis.elementsWithDifferentLanguage.length > 0) {
      recommendations.push('Consider providing translations or explanations for foreign language content');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
