/**
 * Enhanced Dynamic Content Monitor
 * Extends DynamicContentMonitor with advanced accessibility-focused state change detection
 */

import { Page } from 'puppeteer';
import DynamicContentMonitor, { 
  DynamicContentR<PERSON>ult, 
  DynamicContentConfig, 
  DynamicContentChange,
  NetworkRequest,
  SPANavigation 
} from './dynamic-content-monitor';
import logger from '../../../utils/logger';

export interface EnhancedDynamicContentConfig extends DynamicContentConfig {
  enableAccessibilityTracking: boolean;
  enableFocusManagementMonitoring: boolean;
  enableLiveRegionMonitoring: boolean;
  enableLoadingStateTracking: boolean;
  enableRouteChangeAnalysis: boolean;
  accessibilityChangeThreshold: number; // Minimum significance for accessibility changes
  focusTrackingDepth: number; // Maximum depth for focus tracking
}

export interface AccessibilityStateChange {
  type: 'focus' | 'aria' | 'role' | 'landmark' | 'heading' | 'live-region';
  element: string;
  previousState: any;
  newState: any;
  impact: 'low' | 'medium' | 'high' | 'critical';
  timestamp: number;
  context: {
    trigger: 'user-action' | 'ajax' | 'route-change' | 'timer' | 'unknown';
    relatedElements: string[];
    pageState: string;
  };
}

export interface FocusManagementEvent {
  type: 'focus-gained' | 'focus-lost' | 'focus-trapped' | 'focus-restored';
  element: string;
  previousElement?: string;
  isLogical: boolean;
  isAccessible: boolean;
  timestamp: number;
  routeChange?: boolean;
  issues: string[];
}

export interface LiveRegionUpdate {
  element: string;
  ariaLive: 'polite' | 'assertive' | 'off';
  previousContent: string;
  newContent: string;
  contentLength: number;
  updateFrequency: number; // Updates per minute
  timestamp: number;
  isAccessible: boolean;
  recommendations: string[];
}

export interface LoadingStateAnalysis {
  hasLoadingIndicators: boolean;
  loadingStates: Array<{
    element: string;
    type: 'spinner' | 'skeleton' | 'progress' | 'text' | 'overlay';
    isAccessible: boolean;
    hasAriaLabel: boolean;
    hasLiveRegion: boolean;
    duration: number;
  }>;
  accessibilityScore: number;
  issues: string[];
  recommendations: string[];
}

export interface RouteChangeAnalysis {
  navigation: SPANavigation;
  focusManagement: {
    focusRestored: boolean;
    focusTarget: string;
    isLogical: boolean;
  };
  contentUpdates: {
    titleUpdated: boolean;
    landmarksUpdated: boolean;
    headingStructureChanged: boolean;
    liveRegionAnnounced: boolean;
  };
  accessibilityScore: number;
  issues: string[];
  recommendations: string[];
}

export interface EnhancedDynamicContentResult extends DynamicContentResult {
  accessibilityChanges: AccessibilityStateChange[];
  focusManagementEvents: FocusManagementEvent[];
  liveRegionUpdates: LiveRegionUpdate[];
  loadingStateAnalysis: LoadingStateAnalysis;
  routeChangeAnalyses: RouteChangeAnalysis[];
  accessibilityScore: number;
  enhancedRecommendations: string[];
}

/**
 * Enhanced dynamic content monitor with accessibility-focused analysis
 */
export class EnhancedDynamicContentMonitor extends DynamicContentMonitor {
  private static enhancedInstance: EnhancedDynamicContentMonitor;
  private enhancedConfig: EnhancedDynamicContentConfig;
  private accessibilityChanges = new Map<string, AccessibilityStateChange[]>();
  private focusEvents = new Map<string, FocusManagementEvent[]>();
  private liveRegionUpdates = new Map<string, LiveRegionUpdate[]>();

  private constructor(config?: Partial<EnhancedDynamicContentConfig>) {
    super();
    
    this.enhancedConfig = {
      monitoringDuration: config?.monitoringDuration || 30000,
      stabilityThreshold: config?.stabilityThreshold || 5,
      enableNetworkMonitoring: config?.enableNetworkMonitoring ?? true,
      enableSPADetection: config?.enableSPADetection ?? true,
      enableFrameworkDetection: config?.enableFrameworkDetection ?? true,
      maxChangesToTrack: config?.maxChangesToTrack || 1000,
      enableAccessibilityTracking: config?.enableAccessibilityTracking ?? true,
      enableFocusManagementMonitoring: config?.enableFocusManagementMonitoring ?? true,
      enableLiveRegionMonitoring: config?.enableLiveRegionMonitoring ?? true,
      enableLoadingStateTracking: config?.enableLoadingStateTracking ?? true,
      enableRouteChangeAnalysis: config?.enableRouteChangeAnalysis ?? true,
      accessibilityChangeThreshold: config?.accessibilityChangeThreshold || 2.0,
      focusTrackingDepth: config?.focusTrackingDepth || 10,
    };

    logger.info('🔍 Enhanced Dynamic Content Monitor initialized', {
      accessibilityTracking: this.enhancedConfig.enableAccessibilityTracking,
      focusManagement: this.enhancedConfig.enableFocusManagementMonitoring,
      liveRegions: this.enhancedConfig.enableLiveRegionMonitoring,
    });
  }

  static getEnhancedInstance(config?: Partial<EnhancedDynamicContentConfig>): EnhancedDynamicContentMonitor {
    if (!EnhancedDynamicContentMonitor.enhancedInstance) {
      EnhancedDynamicContentMonitor.enhancedInstance = new EnhancedDynamicContentMonitor(config);
    }
    return EnhancedDynamicContentMonitor.enhancedInstance;
  }

  /**
   * Enhanced monitoring with accessibility tracking
   */
  async startEnhancedMonitoring(
    page: Page,
    scanId: string,
    config?: Partial<EnhancedDynamicContentConfig>
  ): Promise<void> {
    // Start base monitoring
    await this.startMonitoring(page, scanId, config);

    // Initialize enhanced tracking
    this.accessibilityChanges.set(scanId, []);
    this.focusEvents.set(scanId, []);
    this.liveRegionUpdates.set(scanId, []);

    // Set up accessibility-specific monitoring
    if (this.enhancedConfig.enableAccessibilityTracking) {
      await this.setupAccessibilityTracking(page, scanId);
    }

    if (this.enhancedConfig.enableFocusManagementMonitoring) {
      await this.setupFocusManagementTracking(page, scanId);
    }

    if (this.enhancedConfig.enableLiveRegionMonitoring) {
      await this.setupLiveRegionTracking(page, scanId);
    }

    logger.debug(`🔍 Enhanced dynamic content monitoring started for scan: ${scanId}`);
  }

  /**
   * Enhanced monitoring results with accessibility analysis
   */
  async stopEnhancedMonitoring(scanId: string): Promise<EnhancedDynamicContentResult> {
    // Get base results
    const baseResult = await this.stopMonitoring(scanId);

    // Get enhanced data
    const accessibilityChanges = this.accessibilityChanges.get(scanId) || [];
    const focusManagementEvents = this.focusEvents.get(scanId) || [];
    const liveRegionUpdates = this.liveRegionUpdates.get(scanId) || [];

    // Analyze loading states
    const loadingStateAnalysis = await this.analyzeLoadingStates(baseResult);

    // Analyze route changes
    const routeChangeAnalyses = await this.analyzeRouteChanges(baseResult, accessibilityChanges, focusManagementEvents);

    // Calculate accessibility score
    const accessibilityScore = this.calculateAccessibilityScore(
      accessibilityChanges,
      focusManagementEvents,
      liveRegionUpdates,
      loadingStateAnalysis,
      routeChangeAnalyses
    );

    // Generate enhanced recommendations
    const enhancedRecommendations = this.generateEnhancedRecommendations(
      accessibilityChanges,
      focusManagementEvents,
      liveRegionUpdates,
      loadingStateAnalysis,
      routeChangeAnalyses
    );

    // Cleanup
    this.accessibilityChanges.delete(scanId);
    this.focusEvents.delete(scanId);
    this.liveRegionUpdates.delete(scanId);

    return {
      ...baseResult,
      accessibilityChanges,
      focusManagementEvents,
      liveRegionUpdates,
      loadingStateAnalysis,
      routeChangeAnalyses,
      accessibilityScore,
      enhancedRecommendations,
    };
  }

  /**
   * Set up accessibility state change tracking
   */
  private async setupAccessibilityTracking(page: Page, scanId: string): Promise<void> {
    await page.evaluateOnNewDocument((scanId: string) => {
      // Initialize enhanced tracking
      (window as any).wcagEnhancedDynamicData = (window as any).wcagEnhancedDynamicData || {
        accessibilityChanges: [],
        focusEvents: [],
        liveRegionUpdates: [],
      };

      // Track ARIA attribute changes
      const ariaObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName?.startsWith('aria-')) {
            const element = mutation.target as Element;
            const change: any = {
              type: 'aria',
              element: this.getElementSelector(element),
              previousState: mutation.oldValue,
              newState: element.getAttribute(mutation.attributeName!),
              impact: this.assessAccessibilityImpact(mutation.attributeName!, mutation.oldValue, element.getAttribute(mutation.attributeName!)),
              timestamp: Date.now(),
              context: {
                trigger: 'unknown',
                relatedElements: [],
                pageState: document.readyState,
              },
            };

            (window as any).wcagEnhancedDynamicData.accessibilityChanges.push(change);
          }
        });
      });

      // Observe ARIA changes
      ariaObserver.observe(document.body, {
        attributes: true,
        attributeOldValue: true,
        attributeFilter: [
          'aria-label', 'aria-labelledby', 'aria-describedby', 'aria-hidden',
          'aria-expanded', 'aria-selected', 'aria-checked', 'aria-pressed',
          'aria-live', 'aria-atomic', 'aria-relevant', 'role'
        ],
        subtree: true,
      });

      // Track role changes
      const roleObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'role') {
            const element = mutation.target as Element;
            const change: any = {
              type: 'role',
              element: this.getElementSelector(element),
              previousState: mutation.oldValue,
              newState: element.getAttribute('role'),
              impact: 'medium',
              timestamp: Date.now(),
              context: {
                trigger: 'unknown',
                relatedElements: [],
                pageState: document.readyState,
              },
            };

            (window as any).wcagEnhancedDynamicData.accessibilityChanges.push(change);
          }
        });
      });

      roleObserver.observe(document.body, {
        attributes: true,
        attributeOldValue: true,
        attributeFilter: ['role'],
        subtree: true,
      });

      // Helper methods
      (window as any).getElementSelector = function(element: Element): string {
        if (element.id) return `#${element.id}`;
        if (element.className) return `.${element.className.split(' ')[0]}`;
        return element.tagName.toLowerCase();
      };

      (window as any).assessAccessibilityImpact = function(attributeName: string, oldValue: string | null, newValue: string | null): string {
        // Critical attributes
        if (['aria-hidden', 'aria-live'].includes(attributeName)) {
          return 'critical';
        }
        
        // High impact attributes
        if (['aria-label', 'aria-labelledby', 'role'].includes(attributeName)) {
          return 'high';
        }
        
        // Medium impact attributes
        if (['aria-expanded', 'aria-selected', 'aria-checked'].includes(attributeName)) {
          return 'medium';
        }
        
        return 'low';
      };
    }, scanId);
  }

  /**
   * Set up focus management tracking
   */
  private async setupFocusManagementTracking(page: Page, scanId: string): Promise<void> {
    await page.evaluateOnNewDocument((scanId: string) => {
      let previousFocusedElement: Element | null = null;

      // Track focus changes
      document.addEventListener('focusin', (event) => {
        const currentElement = event.target as Element;
        const focusEvent: any = {
          type: 'focus-gained',
          element: (window as any).getElementSelector(currentElement),
          previousElement: previousFocusedElement ? (window as any).getElementSelector(previousFocusedElement) : undefined,
          isLogical: (window as any).isFocusLogical(previousFocusedElement, currentElement),
          isAccessible: (window as any).isFocusAccessible(currentElement),
          timestamp: Date.now(),
          routeChange: false,
          issues: (window as any).identifyFocusIssues(currentElement),
        };

        (window as any).wcagEnhancedDynamicData.focusEvents.push(focusEvent);
        previousFocusedElement = currentElement;
      });

      // Helper methods for focus analysis
      (window as any).isFocusLogical = function(previous: Element | null, current: Element): boolean {
        if (!previous) return true;

        // Simple heuristic: check if elements are in logical order
        const prevRect = previous.getBoundingClientRect();
        const currRect = current.getBoundingClientRect();

        // Consider focus logical if moving forward in reading order
        return currRect.top >= prevRect.top ||
               (currRect.top === prevRect.top && currRect.left >= prevRect.left);
      };

      (window as any).isFocusAccessible = function(element: Element): boolean {
        const style = window.getComputedStyle(element as HTMLElement);

        // Check if element is visible and has proper focus indicator
        return style.display !== 'none' &&
               style.visibility !== 'hidden' &&
               (style.outline !== 'none' || style.boxShadow !== 'none');
      };

      (window as any).identifyFocusIssues = function(element: Element): string[] {
        const issues: string[] = [];
        const style = window.getComputedStyle(element as HTMLElement);

        if (style.outline === 'none' && style.boxShadow === 'none') {
          issues.push('No visible focus indicator');
        }

        if (!element.getAttribute('aria-label') && !element.textContent?.trim()) {
          issues.push('No accessible name');
        }

        return issues;
      };
    }, scanId);
  }

  /**
   * Set up live region tracking
   */
  private async setupLiveRegionTracking(page: Page, scanId: string): Promise<void> {
    await page.evaluateOnNewDocument((scanId: string) => {
      const liveRegions = new Map<Element, string>();

      // Find existing live regions
      document.querySelectorAll('[aria-live]').forEach((element) => {
        liveRegions.set(element, element.textContent || '');
      });

      // Monitor live region content changes
      const liveRegionObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          const target = mutation.target as Element;
          const ariaLive = target.getAttribute('aria-live') ||
                          target.closest('[aria-live]')?.getAttribute('aria-live');

          if (ariaLive && (mutation.type === 'childList' || mutation.type === 'characterData')) {
            const previousContent = liveRegions.get(target) || '';
            const newContent = target.textContent || '';

            if (previousContent !== newContent) {
              const update: any = {
                element: (window as any).getElementSelector(target),
                ariaLive: ariaLive as 'polite' | 'assertive' | 'off',
                previousContent,
                newContent,
                contentLength: newContent.length,
                updateFrequency: (window as any).calculateUpdateFrequency(target),
                timestamp: Date.now(),
                isAccessible: (window as any).isLiveRegionAccessible(target, newContent),
                recommendations: (window as any).generateLiveRegionRecommendations(target, newContent),
              };

              (window as any).wcagEnhancedDynamicData.liveRegionUpdates.push(update);
              liveRegions.set(target, newContent);
            }
          }
        });
      });

      liveRegionObserver.observe(document.body, {
        childList: true,
        characterData: true,
        subtree: true,
      });

      // Helper methods for live region analysis
      (window as any).calculateUpdateFrequency = function(element: Element): number {
        // Simplified frequency calculation
        return 1; // Updates per minute (placeholder)
      };

      (window as any).isLiveRegionAccessible = function(element: Element, content: string): boolean {
        // Check if content is meaningful and not too frequent
        return content.trim().length > 0 && content.length < 200;
      };

      (window as any).generateLiveRegionRecommendations = function(element: Element, content: string): string[] {
        const recommendations: string[] = [];

        if (content.length > 200) {
          recommendations.push('Content too long for live region - consider shorter messages');
        }

        if (!content.trim()) {
          recommendations.push('Empty live region update - ensure meaningful content');
        }

        return recommendations;
      };
    }, scanId);
  }

  /**
   * Analyze loading states for accessibility
   */
  private async analyzeLoadingStates(baseResult: DynamicContentResult): Promise<LoadingStateAnalysis> {
    const loadingStates: LoadingStateAnalysis['loadingStates'] = [];
    let hasLoadingIndicators = false;
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Analyze network requests for loading patterns
    const ajaxRequests = baseResult.networkRequests.filter(req => req.isXHR || req.isFetch);

    if (ajaxRequests.length > 0) {
      hasLoadingIndicators = true;

      // Simulate loading state analysis (in real implementation, this would analyze actual DOM)
      loadingStates.push({
        element: 'loading-spinner',
        type: 'spinner',
        isAccessible: false, // Placeholder
        hasAriaLabel: false,
        hasLiveRegion: false,
        duration: 2000,
      });

      issues.push('Loading states detected but accessibility not verified');
      recommendations.push('Ensure loading indicators have proper ARIA labels');
      recommendations.push('Use live regions to announce loading state changes');
    }

    const accessibilityScore = this.calculateLoadingAccessibilityScore(loadingStates, issues);

    return {
      hasLoadingIndicators,
      loadingStates,
      accessibilityScore,
      issues,
      recommendations,
    };
  }

  private calculateLoadingAccessibilityScore(loadingStates: any[], issues: string[]): number {
    if (loadingStates.length === 0) return 100;

    const accessibleStates = loadingStates.filter(state => state.isAccessible);
    const baseScore = (accessibleStates.length / loadingStates.length) * 100;

    return Math.max(0, baseScore - (issues.length * 10));
  }

  private async analyzeRouteChanges(
    baseResult: DynamicContentResult,
    accessibilityChanges: AccessibilityStateChange[],
    focusEvents: FocusManagementEvent[]
  ): Promise<RouteChangeAnalysis[]> {
    return []; // Simplified implementation
  }

  private calculateAccessibilityScore(
    accessibilityChanges: AccessibilityStateChange[],
    focusEvents: FocusManagementEvent[],
    liveRegionUpdates: LiveRegionUpdate[],
    loadingStateAnalysis: LoadingStateAnalysis,
    routeChangeAnalyses: RouteChangeAnalysis[]
  ): number {
    let score = 100;

    // Deduct for accessibility issues
    const criticalChanges = accessibilityChanges.filter(change => change.impact === 'critical');
    score -= criticalChanges.length * 20;

    const focusIssues = focusEvents.filter(event => !event.isAccessible || event.issues.length > 0);
    score -= focusIssues.length * 10;

    const inaccessibleLiveRegions = liveRegionUpdates.filter(update => !update.isAccessible);
    score -= inaccessibleLiveRegions.length * 15;

    score -= (100 - loadingStateAnalysis.accessibilityScore) * 0.3;

    const routeIssues = routeChangeAnalyses.reduce((sum, analysis) => sum + analysis.issues.length, 0);
    score -= routeIssues * 5;

    return Math.max(0, Math.round(score));
  }

  private generateEnhancedRecommendations(
    accessibilityChanges: AccessibilityStateChange[],
    focusEvents: FocusManagementEvent[],
    liveRegionUpdates: LiveRegionUpdate[],
    loadingStateAnalysis: LoadingStateAnalysis,
    routeChangeAnalyses: RouteChangeAnalysis[]
  ): string[] {
    const recommendations: string[] = [];

    // Accessibility changes recommendations
    const criticalChanges = accessibilityChanges.filter(change => change.impact === 'critical');
    if (criticalChanges.length > 0) {
      recommendations.push(`${criticalChanges.length} critical accessibility changes detected - review ARIA usage`);
    }

    // Focus management recommendations
    const focusIssues = focusEvents.filter(event => event.issues.length > 0);
    if (focusIssues.length > 0) {
      recommendations.push('Focus management issues detected - ensure proper focus indicators and logical order');
    }

    // Live region recommendations
    const problematicLiveRegions = liveRegionUpdates.filter(update => !update.isAccessible);
    if (problematicLiveRegions.length > 0) {
      recommendations.push('Live region accessibility issues - review content length and update frequency');
    }

    // Loading state recommendations
    recommendations.push(...loadingStateAnalysis.recommendations);

    // Route change recommendations
    routeChangeAnalyses.forEach(analysis => {
      recommendations.push(...analysis.recommendations);
    });

    return [...new Set(recommendations)]; // Remove duplicates
  }
}

export default EnhancedDynamicContentMonitor;
