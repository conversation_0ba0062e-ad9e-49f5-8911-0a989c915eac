/**
 * WCAG Rule 19: Keyboard Focus 3.0 - Enhanced keyboard focus analysis
 * 90% Automated - Manual review for complex focus management
 */

import { Page } from 'puppeteer';
import {
  ManualReviewConfig,
  ManualReviewTemplate,
  ManualReviewItem,
} from '../utils/manual-review-template';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export class KeyboardFocus3Check {
  private checkTemplate = new ManualReviewTemplate();
  private smartCache = SmartCache.getInstance();

  /**
   * Perform enhanced keyboard focus check - 90% automated with enhanced evidence
   */
  async performCheck(config: ManualReviewConfig) {
    const result = await this.checkTemplate.executeManualReviewCheck(
      'WCAG-019',
      'Keyboard Focus 3.0',
      'operable',
      0.08,
      'AAA',
      0.9, // 90% automation rate
      config,
      this.executeKeyboardFocus3Check.bind(this),
    );

    // Enhanced evidence standardization with advanced focus analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-019',
        ruleName: 'Keyboard Focus 3.0',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'enhanced-focus-analysis',
          manualReviewRequired: result.manualReviewItems?.length > 0,
          focusVisibilityAnalysis: true,
          focusManagementAnalysis: true,
          focusTrapAnalysis: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85,
        maxEvidenceItems: 35,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  /**
   * Execute enhanced keyboard focus analysis
   */
  private async executeKeyboardFocus3Check(page: Page, _config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Enhanced focus visibility analysis
    const focusVisibilityAnalysis = await this.analyzeEnhancedFocusVisibility(page);

    // Focus management analysis
    const focusManagementAnalysis = await this.analyzeFocusManagement(page);

    // Focus trap analysis
    const focusTrapAnalysis = await this.analyzeFocusTraps(page);

    // Combine all analyses
    const allAnalyses = [focusVisibilityAnalysis, focusManagementAnalysis, focusTrapAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedChecks = totalChecks - manualReviewCount;
    const automatedScore =
      automatedChecks > 0 ? Math.round((passedChecks / automatedChecks) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Enhanced keyboard focus analysis summary',
      value: `${passedChecks}/${automatedChecks} checks pass automated tests, ${manualReviewCount} require manual review`,
      severity: automatedScore >= 90 ? 'info' : automatedScore >= 70 ? 'warning' : 'error',
    });

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 0.9,
    };
  }

  /**
   * Enhanced focus visibility analysis with AAA requirements
   */
  private async analyzeEnhancedFocusVisibility(page: Page) {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      function generateSelector(element: Element, index: number): string {
        if (element.id) {
          return `#${element.id}`;
        }

        if (element.className) {
          const classes = element.className
            .toString()
            .split(' ')
            .filter((c) => c.length > 0);
          if (classes.length > 0) {
            return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
          }
        }

        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function analyzeFocusIndicator(element: Element): {
        hasIndicator: boolean;
        indicatorType: string;
        meetsAAA: boolean;
        details: string;
      } {
        const computedStyle = window.getComputedStyle(element, ':focus');

        // Check for outline
        const outline = computedStyle.outline;
        const outlineWidth = computedStyle.outlineWidth;
        const outlineStyle = computedStyle.outlineStyle;

        // Check for box-shadow
        const boxShadow = computedStyle.boxShadow;

        // Check for border changes
        const border = computedStyle.border;
        const borderWidth = computedStyle.borderWidth;

        // Check for background changes
        const backgroundColor = computedStyle.backgroundColor;

        let hasIndicator = false;
        let indicatorType = '';
        let meetsAAA = false;
        let details = '';

        if (outline && outline !== 'none' && outlineStyle !== 'none') {
          hasIndicator = true;
          indicatorType = 'outline';

          // AAA requires 2px minimum thickness
          const widthValue = parseFloat(outlineWidth);
          meetsAAA = widthValue >= 2;
          details = `outline: ${outline}`;
        } else if (boxShadow && boxShadow !== 'none') {
          hasIndicator = true;
          indicatorType = 'box-shadow';

          // Check if box-shadow is substantial enough
          const shadowParts = boxShadow.split(' ');
          const hasSubstantialShadow = shadowParts.some((part) => {
            const value = parseFloat(part);
            return !isNaN(value) && Math.abs(value) >= 2;
          });

          meetsAAA = hasSubstantialShadow;
          details = `box-shadow: ${boxShadow}`;
        } else if (border && borderWidth) {
          hasIndicator = true;
          indicatorType = 'border';

          const widthValue = parseFloat(borderWidth);
          meetsAAA = widthValue >= 2;
          details = `border: ${border}`;
        } else if (
          backgroundColor &&
          backgroundColor !== 'rgba(0, 0, 0, 0)' &&
          backgroundColor !== 'transparent'
        ) {
          hasIndicator = true;
          indicatorType = 'background';
          meetsAAA = true; // Background changes are generally acceptable
          details = `background-color: ${backgroundColor}`;
        }

        return { hasIndicator, indicatorType, meetsAAA, details };
      }

      const focusableElements = Array.from(
        document.querySelectorAll(
          'a, button, input:not([type="hidden"]), select, textarea, [tabindex]:not([tabindex="-1"])',
        ),
      );

      let totalChecks = 0;
      let passedChecks = 0;

      if (focusableElements.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      focusableElements.forEach((element, index) => {
        const selector = generateSelector(element, index);
        const focusAnalysis = analyzeFocusIndicator(element);

        totalChecks++;

        if (focusAnalysis.hasIndicator) {
          if (focusAnalysis.meetsAAA) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'Element has AAA-compliant focus indicator',
              value: `${focusAnalysis.indicatorType}: ${focusAnalysis.details}`,
              selector,
              severity: 'info',
            });
          } else {
            // Has indicator but doesn't meet AAA
            evidence.push({
              type: 'text',
              description: 'Element has focus indicator but may not meet AAA requirements',
              value: `${focusAnalysis.indicatorType}: ${focusAnalysis.details}`,
              selector,
              severity: 'warning',
            });

            manualReviewItems.push({
              selector,
              description: 'Focus indicator AAA compliance verification needed',
              automatedFindings: `Has ${focusAnalysis.indicatorType} but automated analysis suggests it may not meet AAA thickness/contrast requirements`,
              reviewRequired:
                'Manually verify focus indicator meets AAA requirements (2px minimum, adequate contrast)',
              priority: 'medium',
              estimatedTime: 2,
            });
          }
        } else {
          issues.push(`Element missing focus indicator: ${selector}`);
          recommendations.push(`Add visible focus indicator to ${selector}`);
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Analyze focus management in dynamic content
   */
  private async analyzeFocusManagement(page: Page) {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      // Look for dynamic content that might need focus management
      const dynamicElements = Array.from(
        document.querySelectorAll(
          '[role="dialog"], [role="alertdialog"], .modal, .popup, .dropdown, .accordion, .tab-panel, [aria-expanded]',
        ),
      );

      const totalChecks = dynamicElements.length;
      let passedChecks = 0;

      if (dynamicElements.length === 0) {
        evidence.push({
          type: 'text',
          description: 'No dynamic content requiring focus management found',
          value: 'Page appears to have static content only',
          severity: 'info',
        });
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      dynamicElements.forEach((element, index) => {
        const selector = element.id
          ? `#${element.id}`
          : `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
        const role = element.getAttribute('role');

        // Check for focus management attributes
        const hasTabIndex = element.hasAttribute('tabindex');
        const hasAriaLabel =
          element.hasAttribute('aria-label') || element.hasAttribute('aria-labelledby');

        if (role === 'dialog' || role === 'alertdialog') {
          // Dialogs require special focus management
          manualReviewItems.push({
            selector,
            description: 'Dialog focus management verification needed',
            automatedFindings: `Dialog element found with role: ${role}`,
            reviewRequired:
              'Verify focus moves to dialog on open, is trapped within dialog, and returns to trigger on close',
            priority: 'high',
            estimatedTime: 8,
          });
        } else if (element.hasAttribute('aria-expanded')) {
          // Expandable content needs focus management
          const isExpanded = element.getAttribute('aria-expanded') === 'true';

          if (isExpanded) {
            manualReviewItems.push({
              selector,
              description: 'Expandable content focus management verification needed',
              automatedFindings: 'Expandable content is currently expanded',
              reviewRequired: 'Verify focus management when content expands/collapses',
              priority: 'medium',
              estimatedTime: 5,
            });
          } else {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'Expandable content found (collapsed)',
              value: 'Content is collapsed - focus management needs testing when expanded',
              selector,
              severity: 'info',
            });
          }
        } else {
          // Other dynamic elements
          if (hasTabIndex && hasAriaLabel) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'Dynamic element has focus management attributes',
              value: 'Element has tabindex and accessible name',
              selector,
              severity: 'info',
            });
          } else {
            manualReviewItems.push({
              selector,
              description: 'Dynamic element focus management verification needed',
              automatedFindings: `Dynamic element with class/role: ${element.className || role || 'unknown'}`,
              reviewRequired: 'Verify appropriate focus management for dynamic content changes',
              priority: 'medium',
              estimatedTime: 4,
            });
          }
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Analyze focus traps and keyboard navigation patterns
   */
  private async analyzeFocusTraps(page: Page) {
    const evidence: Array<{
      type:
        | 'text'
        | 'image'
        | 'code'
        | 'measurement'
        | 'interaction'
        | 'info'
        | 'warning'
        | 'error';
      description: string;
      value: string;
      selector?: string;
      severity?: 'info' | 'warning' | 'error' | 'critical';
    }> = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: Array<{
      selector: string;
      description: string;
      automatedFindings: string;
      reviewRequired: string;
      priority: 'high' | 'medium' | 'low';
      estimatedTime: number;
    }> = [];
    const totalChecks = 1;
    let passedChecks = 0;

    try {
      // Test basic keyboard navigation
      await page.keyboard.press('Tab');

      // Get all focusable elements
      const focusableCount = await page.evaluate(() => {
        const focusableElements = Array.from(
          document.querySelectorAll(
            'a, button, input:not([type="hidden"]), select, textarea, [tabindex]:not([tabindex="-1"])',
          ),
        );
        return focusableElements.length;
      });

      if (focusableCount > 0) {
        // Test tab navigation sequence
        const navigationTest = await this.testTabSequence(page, Math.min(focusableCount, 10));

        if (navigationTest.isLogical) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: 'Tab navigation sequence appears logical',
            value: `Tested ${navigationTest.elementsChecked} elements`,
            severity: 'info',
          });
        } else {
          issues.push('Tab navigation sequence may be illogical');
          recommendations.push('Review tab order and ensure logical navigation sequence');

          manualReviewItems.push({
            selector: 'body',
            description: 'Tab sequence verification needed',
            automatedFindings: 'Automated testing suggests potential tab order issues',
            reviewRequired:
              'Manually test complete tab sequence to ensure logical navigation order',
            priority: 'medium',
            estimatedTime: 6,
          });
        }
      } else {
        evidence.push({
          type: 'text',
          description: 'No focusable elements found',
          value: 'Page may not require keyboard navigation',
          severity: 'info',
        });
        passedChecks++;
      }
    } catch (error) {
      manualReviewItems.push({
        selector: 'body',
        description: 'Keyboard navigation testing failed',
        automatedFindings: 'Automated keyboard testing encountered errors',
        reviewRequired: 'Manually test keyboard navigation, focus traps, and tab sequence',
        priority: 'high',
        estimatedTime: 10,
      });
    }

    return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
  }

  /**
   * Test tab sequence for logical order
   */
  private async testTabSequence(
    page: Page,
    maxElements: number,
  ): Promise<{
    isLogical: boolean;
    elementsChecked: number;
    issues: string[];
  }> {
    const sequence: Array<{ x: number; y: number; tagName: string }> = [];
    const issues: string[] = [];

    try {
      for (let i = 0; i < maxElements; i++) {
        await page.keyboard.press('Tab');

        const elementInfo = await page.evaluate(() => {
          const active = document.activeElement;
          if (!active || active === document.body) return null;

          const rect = active.getBoundingClientRect();
          return {
            x: rect.left + rect.width / 2,
            y: rect.top + rect.height / 2,
            tagName: active.tagName.toLowerCase(),
          };
        });

        if (elementInfo) {
          sequence.push(elementInfo);
        }
      }

      // Analyze sequence for logical order (generally top-to-bottom, left-to-right)
      let isLogical = true;

      for (let i = 1; i < sequence.length; i++) {
        const prev = sequence[i - 1];
        const curr = sequence[i];

        // Check if focus moved significantly backwards in reading order
        if (curr.y < prev.y - 50 && curr.x < prev.x - 100) {
          isLogical = false;
          issues.push('Focus moved backwards in reading order');
          break;
        }
      }

      return {
        isLogical,
        elementsChecked: sequence.length,
        issues,
      };
    } catch (error) {
      return {
        isLogical: false,
        elementsChecked: 0,
        issues: ['Error testing tab sequence'],
      };
    }
  }
}
