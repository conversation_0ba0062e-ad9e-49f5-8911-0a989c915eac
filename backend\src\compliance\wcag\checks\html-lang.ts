/**
 * WCAG-024: Language of Page Check
 * Success Criterion: 3.1.1 Language of Page (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig, CheckFunction } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface HtmlLangConfig extends EnhancedCheckConfig {
  enableLanguageDetection?: boolean;
  enableSemanticValidation?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableCMSDetection?: boolean;
  checkLanguageConsistency?: boolean;
}

export class HtmlLangCheck {
  private checkTemplate = new CheckTemplate();
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();

  async performCheck(config: HtmlLangConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: HtmlLangConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableLanguageDetection: true,
      enableSemanticValidation: true,
      enableContentQualityAnalysis: true,
      enableCMSDetection: true,
      checkLanguageConsistency: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-024',
      'Language of Page',
      'understandable',
      0.0611,
      'A',
      enhancedConfig,
      this.executeHtmlLangCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with HTML language analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-024',
        ruleName: 'Language of Page',
        scanDuration: result.executionTime,
        elementsAnalyzed: 1,
        checkSpecificData: {
          automationRate: 1.0,
          checkType: 'html-language-validation',
          langAttributeValidation: true,
          pageLanguageDetection: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.95,
        maxEvidenceItems: 5,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: 1, // Always 1 html element
        failed: result.score === 0 ? 1 : 0,
        passed: result.score === 100 ? 1 : 0,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: 1,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'html-lang-detection',
        confidence: 1.0,
        additionalData: {
          checkType: 'language-validation',
          automationLevel: 'full',
        },
      },
    };
  }

  private async executeHtmlLangCheck(
    page: Page,
    config: CheckConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Check for html lang attribute
    const langAnalysis = await page.evaluate(() => {
      const html = document.documentElement;
      return {
        hasLang: html.hasAttribute('lang'),
        langValue: html.getAttribute('lang'),
        hasXmlLang: html.hasAttribute('xml:lang'),
        xmlLangValue: html.getAttribute('xml:lang'),
        htmlOuterHTML: html.outerHTML.substring(0, 200), // First 200 chars for evidence
      };
    });

    let score = 100;
    const elementCount = 1; // Always 1 html element
    const scanDuration = Date.now() - startTime;

    if (!langAnalysis.hasLang || !langAnalysis.langValue) {
      score = 0;
      issues.push('HTML document missing lang attribute');
      
      evidence.push({
        type: 'code',
        description: 'HTML element missing lang attribute',
        value: langAnalysis.htmlOuterHTML,
        selector: 'html',
        elementCount: 1,
        affectedSelectors: ['html'],
        severity: 'error',
        fixExample: {
          before: '<html>',
          after: '<html lang="en">',
          description: 'Add lang attribute to html element',
          codeExample: `
<!-- Before -->
<html>
  <head><title>Page Title</title></head>
  <body>Content</body>
</html>

<!-- After -->
<html lang="en">
  <head><title>Page Title</title></head>
  <body>Content</body>
</html>
          `,
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/language-of-page.html',
            'https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/lang'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: 1,
          checkSpecificData: {
            hasXmlLang: langAnalysis.hasXmlLang,
            xmlLangValue: langAnalysis.xmlLangValue || '',
          },
        },
      });
      
      recommendations.push('Add lang attribute to html element: <html lang="en">');
      recommendations.push('Use a valid ISO 639-1 language code');
      recommendations.push('Consider the primary language of your content');
      
    } else {
      // Validate lang code format
      const langCode = langAnalysis.langValue.toLowerCase();
      const validLangPattern = /^[a-z]{2,3}(-[a-z]{2,4})*$/i;
      
      if (!validLangPattern.test(langCode)) {
        score = 0;
        issues.push(`Invalid language code: ${langAnalysis.langValue}`);
        
        evidence.push({
          type: 'code',
          description: 'Invalid language code format',
          value: `<html lang="${langAnalysis.langValue}">`,
          selector: 'html',
          elementCount: 1,
          affectedSelectors: ['html'],
          severity: 'error',
          fixExample: {
            before: `<html lang="${langAnalysis.langValue}">`,
            after: '<html lang="en">',
            description: 'Use valid ISO language code',
            resources: [
              'https://www.w3.org/International/questions/qa-choosing-language-tags',
              'https://www.iana.org/assignments/language-subtag-registry/language-subtag-registry'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              invalidLangCode: langAnalysis.langValue,
              suggestedCodes: ['en', 'en-US', 'fr', 'es', 'de'],
            },
          },
        });
        
        recommendations.push('Use valid ISO language code (e.g., "en", "en-US", "fr", "es")');
        recommendations.push('Check the IANA Language Subtag Registry for valid codes');
      } else {
        // Success case - add positive evidence
        evidence.push({
          type: 'info',
          description: 'HTML element has valid lang attribute',
          value: `<html lang="${langAnalysis.langValue}">`,
          selector: 'html',
          elementCount: 1,
          affectedSelectors: ['html'],
          severity: 'info',
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              validLangCode: langAnalysis.langValue,
              hasXmlLang: langAnalysis.hasXmlLang,
              xmlLangValue: langAnalysis.xmlLangValue || '',
            },
          },
        });
        
        recommendations.push('Continue using valid language codes for all content');
        if (!langAnalysis.hasXmlLang) {
          recommendations.push('Consider adding xml:lang attribute for XHTML compatibility');
        }
      }
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Get common language codes for validation
   */
  private getCommonLanguageCodes(): string[] {
    return [
      'en', 'en-US', 'en-GB', 'en-CA', 'en-AU',
      'es', 'es-ES', 'es-MX', 'es-AR',
      'fr', 'fr-FR', 'fr-CA',
      'de', 'de-DE', 'de-AT', 'de-CH',
      'it', 'it-IT',
      'pt', 'pt-BR', 'pt-PT',
      'ru', 'ru-RU',
      'ja', 'ja-JP',
      'ko', 'ko-KR',
      'zh', 'zh-CN', 'zh-TW',
      'ar', 'ar-SA',
      'hi', 'hi-IN',
      'nl', 'nl-NL',
      'sv', 'sv-SE',
      'no', 'no-NO',
      'da', 'da-DK',
      'fi', 'fi-FI',
      'pl', 'pl-PL',
      'tr', 'tr-TR',
    ];
  }
}
