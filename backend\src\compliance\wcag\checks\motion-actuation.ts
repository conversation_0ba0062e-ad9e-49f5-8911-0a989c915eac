/**
 * WCAG-056: Motion Actuation Check (2.5.4 Level A)
 * 75% Automated - Detects motion-triggered functionality
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced, WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface MotionActuationConfig extends EnhancedCheckConfig {
  enableAdvancedMotionDetection?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
}

export class MotionActuationCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();

  async performCheck(config: MotionActuationConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: MotionActuationConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAdvancedMotionDetection: true,
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-056',
      'Motion Actuation',
      'operable',
      0.0458,
      'A',
      enhancedConfig,
      this.executeMotionActuationCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with motion actuation analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-056',
        ruleName: 'Motion Actuation',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.75,
          checkType: 'motion-actuation-analysis',
          motionEventDetection: true,
          alternativeControlValidation: true,
          advancedMotionDetection: enhancedConfig.enableAdvancedMotionDetection,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.75,
        maxEvidenceItems: 20,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeMotionActuationCheck(
    page: Page,
    config: MotionActuationConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze motion-triggered functionality
    const motionAnalysis = await page.evaluate(() => {
      const motionElements: Array<{
        tagName: string;
        selector: string;
        motionType: string;
        hasAlternative: boolean;
        eventListeners: string[];
        attributes: Record<string, string>;
      }> = [];

      // Check for device motion event listeners
      const elementsWithMotionEvents = document.querySelectorAll('*');
      
      elementsWithMotionEvents.forEach((element, index) => {
        const eventListeners: string[] = [];
        let hasMotionEvents = false;

        // Check for data attributes that suggest motion functionality
        const motionAttributes = [
          'data-shake',
          'data-tilt',
          'data-motion',
          'data-gyroscope',
          'data-accelerometer',
          'data-orientation'
        ];

        motionAttributes.forEach(attr => {
          if (element.hasAttribute(attr)) {
            hasMotionEvents = true;
            eventListeners.push(attr);
          }
        });

        // Check for classes that suggest motion functionality
        const motionClasses = [
          'shake-to-refresh',
          'tilt-control',
          'motion-control',
          'gyro-control',
          'orientation-control'
        ];

        motionClasses.forEach(className => {
          if (element.classList.contains(className)) {
            hasMotionEvents = true;
            eventListeners.push(`class:${className}`);
          }
        });

        // Check for JavaScript patterns in onclick handlers
        const onclickHandler = element.getAttribute('onclick');
        if (onclickHandler) {
          const motionPatterns = [
            /shake/i,
            /tilt/i,
            /motion/i,
            /gyro/i,
            /accelerometer/i,
            /orientation/i
          ];

          motionPatterns.forEach(pattern => {
            if (pattern.test(onclickHandler)) {
              hasMotionEvents = true;
              eventListeners.push('onclick-motion');
            }
          });
        }

        if (hasMotionEvents) {
          // Check for alternative input methods
          const hasAlternative = checkForAlternativeInputs(element);

          motionElements.push({
            tagName: element.tagName.toLowerCase(),
            selector: generateSelector(element, index),
            motionType: eventListeners.join(', '),
            hasAlternative,
            eventListeners,
            attributes: getRelevantAttributes(element),
          });
        }
      });

      return {
        motionElements,
        totalElements: elementsWithMotionEvents.length,
      };

      // Helper functions (defined in evaluate context)
      function checkForAlternativeInputs(element: Element): boolean {
        // Check for alternative input methods near the motion element
        const parent = element.parentElement;
        if (!parent) return false;

        // Look for buttons, links, or other interactive elements
        const alternatives = parent.querySelectorAll(
          'button, a[href], input[type="button"], input[type="submit"], [role="button"], [tabindex="0"]'
        );

        // Check if there are alternative controls
        return alternatives.length > 1; // More than just the motion element itself
      }

      function generateSelector(element: Element, index: number): string {
        if (element.id) return `#${element.id}`;
        if (element.className) {
          const classes = element.className.split(' ').filter(c => c.trim());
          if (classes.length > 0) return `.${classes[0]}`;
        }
        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function getRelevantAttributes(element: Element): Record<string, string> {
        const attrs: Record<string, string> = {};
        const relevantAttrs = ['id', 'class', 'role', 'aria-label', 'title'];
        
        relevantAttrs.forEach(attr => {
          const value = element.getAttribute(attr);
          if (value) attrs[attr] = value;
        });

        return attrs;
      }
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const elementCount = motionAnalysis.motionElements.length;

    if (elementCount > 0) {
      const elementsWithoutAlternatives = motionAnalysis.motionElements.filter(
        el => !el.hasAlternative
      );

      if (elementsWithoutAlternatives.length > 0) {
        score = 0;
        issues.push(
          `${elementsWithoutAlternatives.length} motion-triggered functions lack alternative input methods`
        );

        evidence.push({
          type: 'interaction',
          description: 'Motion-triggered functionality without alternatives',
          value: `Found ${elementsWithoutAlternatives.length} elements that rely on device motion without providing alternative input methods`,
          elementCount: elementsWithoutAlternatives.length,
          affectedSelectors: elementsWithoutAlternatives.map(el => el.selector),
          severity: 'error',
          fixExample: {
            before: '<div data-shake="refresh">Shake to refresh</div>',
            after: '<div data-shake="refresh">Shake to refresh <button onclick="refresh()">or click here</button></div>',
            description: 'Provide alternative input methods for motion-triggered functionality',
            codeExample: `
<!-- Before: Motion-only control -->
<div class="shake-to-refresh" data-motion="shake">
  Shake device to refresh content
</div>

<!-- After: Motion with alternative -->
<div class="refresh-container">
  <div class="shake-to-refresh" data-motion="shake">
    Shake device to refresh content
  </div>
  <button type="button" onclick="refreshContent()" class="refresh-button">
    Refresh Content
  </button>
</div>
            `,
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/motion-actuation.html',
              'https://developer.mozilla.org/en-US/docs/Web/API/DeviceMotionEvent'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: motionAnalysis.totalElements,
            checkSpecificData: {
              motionElementsFound: elementCount,
              elementsWithoutAlternatives: elementsWithoutAlternatives.length,
              motionTypes: [...new Set(motionAnalysis.motionElements.map(el => el.motionType))],
            },
          },
        });

        // Add specific examples for each problematic element
        elementsWithoutAlternatives.slice(0, 5).forEach(element => {
          evidence.push({
            type: 'code',
            description: `Motion-triggered element: ${element.motionType}`,
            value: `<${element.tagName} ${Object.entries(element.attributes)
              .map(([key, value]) => `${key}="${value}"`)
              .join(' ')}>`,
            selector: element.selector,
            severity: 'error',
          });
        });

        recommendations.push(
          'Provide alternative input methods (buttons, links, keyboard shortcuts) for all motion-triggered functionality'
        );
        recommendations.push(
          'Ensure users can disable motion actuation in settings or preferences'
        );
        recommendations.push(
          'Test functionality with assistive technologies that may not support motion input'
        );
      } else {
        // All motion elements have alternatives
        evidence.push({
          type: 'info',
          description: 'Motion functionality with alternatives found',
          value: `Found ${elementCount} motion-triggered elements, all with alternative input methods`,
          elementCount,
          severity: 'info',
          metadata: {
            scanDuration,
            elementsAnalyzed: motionAnalysis.totalElements,
            checkSpecificData: {
              motionElementsFound: elementCount,
              allHaveAlternatives: true,
            },
          },
        });
      }
    } else {
      // No motion-triggered functionality found
      evidence.push({
        type: 'info',
        description: 'No motion-triggered functionality detected',
        value: 'Page does not appear to use device motion for triggering functions',
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: motionAnalysis.totalElements,
          checkSpecificData: {
            motionElementsFound: 0,
          },
        },
      });
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
