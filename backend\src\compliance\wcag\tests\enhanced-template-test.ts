/**
 * Test file for Enhanced Check Template with Axe-core Integration
 * Tests the integration of axe-core validation with existing WCAG checks
 */

import { EnhancedCheckTemplate } from '../utils/enhanced-check-template';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import logger from '../../../utils/logger';

/**
 * Test the enhanced check template functionality
 */
async function testEnhancedCheckTemplate() {
  console.log('🧪 Testing Enhanced Check Template...');
  
  const enhancedTemplate = new EnhancedCheckTemplate();
  
  // Test axe-core rule mapping
  const ruleMapping = (enhancedTemplate as any).getAxeRuleMapping();
  console.log('📚 Axe-core Rule Mapping:', Object.keys(ruleMapping).length, 'WCAG rules mapped');
  
  // Test specific mappings
  const contrastRules = ruleMapping['WCAG-004'];
  const focusRules = ruleMapping['WCAG-007'];
  const langRules = ruleMapping['WCAG-024'];
  
  console.log('✅ WCAG-004 (Contrast) maps to:', contrastRules);
  console.log('✅ WCAG-007 (Focus) maps to:', focusRules);
  console.log('✅ WCAG-024 (Language) maps to:', langRules);
  
  // Test impact mapping
  const criticalSeverity = (enhancedTemplate as any).mapAxeImpactToSeverity('critical');
  const moderateSeverity = (enhancedTemplate as any).mapAxeImpactToSeverity('moderate');
  const minorSeverity = (enhancedTemplate as any).mapAxeImpactToSeverity('minor');
  
  console.log('✅ Impact mapping test:');
  console.log('   Critical ->', criticalSeverity, '(expected: error)');
  console.log('   Moderate ->', moderateSeverity, '(expected: warning)');
  console.log('   Minor ->', minorSeverity, '(expected: info)');
  
  // Validate mappings
  if (criticalSeverity === 'error' && moderateSeverity === 'warning' && minorSeverity === 'info') {
    console.log('✅ Impact mapping test PASSED');
  } else {
    console.log('❌ Impact mapping test FAILED');
  }
}

/**
 * Test evidence standardization functionality
 */
function testEvidenceStandardization() {
  console.log('\n🎨 Testing Evidence Standardization...');
  
  // Create mock evidence
  const mockEvidence = [
    {
      type: 'text' as const,
      description: 'Test evidence item 1',
      value: 'button:nth-of-type(1) - Missing accessible name',
      selector: 'button:nth-of-type(1)',
      severity: 'error' as const
    },
    {
      type: 'code' as const,
      description: 'Test evidence item 2',
      value: '3 elements found with contrast issues',
      severity: 'warning' as const
    }
  ];
  
  // Test standardization
  const standardized = EvidenceStandardizer.standardizeEvidence(mockEvidence, {
    ruleId: 'WCAG-004',
    ruleName: 'Contrast Minimum',
    scanDuration: 1500,
    elementsAnalyzed: 25,
    checkSpecificData: {
      testMode: true,
      enhancedAccuracy: true
    }
  });
  
  console.log('✅ Standardized Evidence Results:');
  standardized.forEach((item, index) => {
    console.log(`   Evidence ${index + 1}:`);
    console.log(`     - Element Count: ${item.elementCount}`);
    console.log(`     - Affected Selectors: ${item.affectedSelectors?.length || 0}`);
    console.log(`     - Has Fix Example: ${!!item.fixExample}`);
    console.log(`     - Has Metadata: ${!!item.metadata}`);
    
    if (item.fixExample) {
      console.log(`     - Fix Example: ${item.fixExample.description}`);
    }
  });
  
  // Validate standardization
  const validation = EvidenceStandardizer.validateEnhancedEvidence(standardized);
  console.log('✅ Evidence Validation:');
  console.log(`   - Is Valid: ${validation.isValid}`);
  console.log(`   - Errors: ${validation.errors.length}`);
  console.log(`   - Warnings: ${validation.warnings.length}`);
  
  if (validation.errors.length > 0) {
    console.log('   - Error Details:', validation.errors);
  }
  if (validation.warnings.length > 0) {
    console.log('   - Warning Details:', validation.warnings);
  }
}

/**
 * Test axe-core availability and integration
 */
function testAxeCoreIntegration() {
  console.log('\n🔧 Testing Axe-core Integration...');
  
  let axeCore: any = null;
  try {
    axeCore = require('axe-core');
    console.log('✅ Axe-core library loaded successfully');
    console.log(`   - Version: ${axeCore.version || 'Unknown'}`);
    console.log(`   - Source available: ${!!axeCore.source}`);
    
    // Test if axe-core source can be injected
    if (axeCore.source && typeof axeCore.source === 'string') {
      console.log(`   - Source size: ${Math.round(axeCore.source.length / 1024)}KB`);
      console.log('✅ Axe-core ready for browser injection');
    } else {
      console.log('⚠️ Axe-core source not available for injection');
    }
    
  } catch (error) {
    console.log('❌ Axe-core library not available:', error instanceof Error ? error.message : 'Unknown error');
    console.log('   Enhanced validation will use fallback mode');
  }
  
  return !!axeCore;
}

/**
 * Test enhanced check configuration
 */
function testEnhancedCheckConfig() {
  console.log('\n⚙️ Testing Enhanced Check Configuration...');
  
  // Test configuration options
  const testConfigs = [
    {
      name: 'Default Enhanced Config',
      config: {
        scanId: 'test-scan-1',
        targetUrl: 'https://example.com',
        enableAxeValidation: true,
        enablePerformanceMetrics: true,
        enableDetailedEvidence: true
      }
    },
    {
      name: 'Minimal Enhanced Config',
      config: {
        scanId: 'test-scan-2',
        targetUrl: 'https://example.com',
        enableAxeValidation: false
      }
    },
    {
      name: 'Custom Rules Config',
      config: {
        scanId: 'test-scan-3',
        targetUrl: 'https://example.com',
        enableAxeValidation: true,
        axeRules: ['color-contrast', 'focus-order-semantics']
      }
    }
  ];
  
  testConfigs.forEach((test, index) => {
    console.log(`✅ Config Test ${index + 1}: ${test.name}`);
    console.log(`   - Scan ID: ${test.config.scanId}`);
    console.log(`   - Axe Validation: ${test.config.enableAxeValidation}`);
    console.log(`   - Custom Rules: ${test.config.axeRules?.length || 0}`);
    console.log(`   - Performance Metrics: ${test.config.enablePerformanceMetrics || false}`);
  });
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Enhanced Check Template Tests\n');
  
  await testEnhancedCheckTemplate();
  testEvidenceStandardization();
  const axeAvailable = testAxeCoreIntegration();
  testEnhancedCheckConfig();
  
  console.log('\n✅ All tests completed!');
  console.log('\n📝 Summary:');
  console.log(`   - Enhanced Check Template: ✅ Ready`);
  console.log(`   - Evidence Standardization: ✅ Ready`);
  console.log(`   - Axe-core Integration: ${axeAvailable ? '✅ Available' : '⚠️ Fallback Mode'}`);
  console.log(`   - Enhanced Configuration: ✅ Ready`);
  
  if (!axeAvailable) {
    console.log('\n📦 To enable axe-core integration, run: npm install axe-core');
  }
}

// Export for use in other test files
export {
  testEnhancedCheckTemplate,
  testEvidenceStandardization,
  testAxeCoreIntegration,
  testEnhancedCheckConfig,
  runAllTests
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}
