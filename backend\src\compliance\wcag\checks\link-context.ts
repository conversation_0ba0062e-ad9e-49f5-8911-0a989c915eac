/**
 * WCAG-049: Link Context Check
 * Success Criterion: 2.4.4 Link Purpose (In Context) (Level A)
 * Enhanced link purpose validation with context analysis
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig } from '../utils/check-template';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

export class LinkContextCheck {
  private checkTemplate = new CheckTemplate();

  async performCheck(config: CheckConfig): Promise<WcagCheckResultEnhanced> {
    const result = await this.checkTemplate.executeCheck(
      'WCAG-049',
      'Link Context',
      'operable',
      0.0611,
      'A',
      config,
      this.executeLinkContextCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with link context analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-038',
        ruleName: 'Link Context',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.85,
          checkType: 'link-context-analysis',
          linkPurposeValidation: true,
          contextualAnalysis: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 40,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeLinkContextCheck(
    page: Page,
    _config: CheckConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze link context and purpose
    const linkAnalysis = await page.evaluate(() => {
      const problematicLinks: Array<{
        href: string;
        text: string;
        selector: string;
        context: string;
        ariaLabel?: string;
        title?: string;
        issues: string[];
        severity: 'error' | 'warning' | 'info';
        hasContext: boolean;
        isPurposeClear: boolean;
      }> = [];

      // Generic/vague link text patterns
      const genericPatterns = [
        /^(click here|here|more|read more|continue|next|previous|back|go|link|this|that)$/i,
        /^(learn more|find out more|see more|view more|show more)$/i,
        /^(download|open|close|submit|send|get|buy|shop|order)$/i,
        /^(page \d+|item \d+|\d+)$/i,
      ];

      const links = document.querySelectorAll('a[href]');
      
      links.forEach((link, index) => {
        const linkElement = link as HTMLAnchorElement;
        const href = linkElement.href;
        const text = linkElement.textContent?.trim() || '';
        const ariaLabel = linkElement.getAttribute('aria-label');
        const title = linkElement.getAttribute('title');
        
        // Skip empty links or fragment-only links
        if (!href || href === '#' || !text) return;
        
        const issues: string[] = [];
        let severity: 'error' | 'warning' | 'info' = 'info';
        
        // Check for generic link text
        const isGeneric = genericPatterns.some(pattern => pattern.test(text));
        if (isGeneric) {
          issues.push('Generic link text');
          severity = 'warning';
        }
        
        // Check for very short link text
        if (text.length < 3 && !ariaLabel) {
          issues.push('Very short link text');
          severity = 'warning';
        }
        
        // Check for duplicate link text with different destinations
        const duplicateLinks = Array.from(links).filter(otherLink => 
          otherLink !== link && 
          otherLink.textContent?.trim() === text &&
          (otherLink as HTMLAnchorElement).href !== href
        );
        
        if (duplicateLinks.length > 0) {
          issues.push('Duplicate link text with different destinations');
          severity = 'error';
        }
        
        // Get context from surrounding elements
        let context = '';
        
        // Check parent elements for context
        let parent = linkElement.parentElement;
        while (parent && context.length < 100) {
          const parentText = parent.textContent?.trim() || '';
          if (parentText.length > text.length) {
            context = parentText.substring(0, 100);
            break;
          }
          parent = parent.parentElement;
        }
        
        // Check for aria-describedby
        const describedBy = linkElement.getAttribute('aria-describedby');
        if (describedBy) {
          const describingElement = document.getElementById(describedBy);
          if (describingElement) {
            context += ' ' + (describingElement.textContent?.trim() || '');
          }
        }
        
        // Check for context in list items, table cells, headings
        const listItem = linkElement.closest('li');
        const tableCell = linkElement.closest('td, th');
        const heading = linkElement.closest('h1, h2, h3, h4, h5, h6');
        
        if (listItem && listItem.textContent) {
          context += ' ' + listItem.textContent.trim();
        }
        if (tableCell && tableCell.textContent) {
          context += ' ' + tableCell.textContent.trim();
        }
        if (heading && heading.textContent) {
          context += ' ' + heading.textContent.trim();
        }
        
        const hasContext = context.length > text.length + 10;
        
        // Determine if purpose is clear
        const effectiveText = ariaLabel || title || text;
        const isPurposeClear = !isGeneric && 
                              effectiveText.length > 5 && 
                              (hasContext || effectiveText.length > 15);
        
        if (!isPurposeClear || issues.length > 0) {
          problematicLinks.push({
            href,
            text,
            selector: `a:nth-of-type(${index + 1})`,
            context: context.substring(0, 200),
            ariaLabel,
            title,
            issues,
            severity,
            hasContext,
            isPurposeClear,
          });
        }
      });

      return {
        problematicLinks,
        totalLinks: links.length,
        problematicCount: problematicLinks.length,
        genericLinks: problematicLinks.filter(l => l.issues.includes('Generic link text')).length,
        duplicateLinks: problematicLinks.filter(l => l.issues.includes('Duplicate link text')).length,
        shortLinks: problematicLinks.filter(l => l.issues.includes('Very short link text')).length,
      };
    });

    let score = 100;
    const elementCount = linkAnalysis.problematicCount;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      // Calculate score based on severity and count
      linkAnalysis.problematicLinks.forEach((link) => {
        const deduction = link.severity === 'error' ? 15 : 
                         link.severity === 'warning' ? 10 : 5;
        score = Math.max(0, score - deduction);
      });

      issues.push(`${elementCount} links with unclear purpose found`);
      if (linkAnalysis.genericLinks > 0) {
        issues.push(`${linkAnalysis.genericLinks} links with generic text`);
      }
      if (linkAnalysis.duplicateLinks > 0) {
        issues.push(`${linkAnalysis.duplicateLinks} duplicate link texts with different destinations`);
      }

      linkAnalysis.problematicLinks.forEach((link) => {
        evidence.push({
          type: 'code',
          description: `Link with unclear purpose: ${link.issues.join(', ')}`,
          value: `"${link.text}" -> ${link.href}`,
          selector: link.selector,
          elementCount: 1,
          affectedSelectors: [link.selector],
          severity: link.severity,
          fixExample: {
            before: this.getBeforeExample(link),
            after: this.getAfterExample(link),
            description: this.getFixDescription(link.issues),
            codeExample: this.getCodeExample(link.issues[0] || 'generic'),
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/link-purpose-in-context.html',
              'https://www.w3.org/WAI/WCAG21/Techniques/G91',
              'https://www.w3.org/WAI/WCAG21/Techniques/H30'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              linkText: link.text,
              href: link.href,
              hasContext: link.hasContext,
              isPurposeClear: link.isPurposeClear,
              issues: link.issues,
              ariaLabel: link.ariaLabel || '',
              title: link.title || '',
            },
          },
        });
      });

      recommendations.push('Use descriptive link text that explains the link purpose');
      recommendations.push('Avoid generic phrases like "click here" or "read more"');
      recommendations.push('Ensure duplicate link text leads to the same destination');
      recommendations.push('Provide context through aria-label or surrounding content');
      recommendations.push('Make link purpose clear from the link text alone when possible');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(link: any): string {
    if (link.issues.includes('Generic link text')) {
      return `<a href="${link.href}">Click here</a>`;
    }
    if (link.issues.includes('Duplicate link text')) {
      return `<a href="/page1">Read more</a>\n<a href="/page2">Read more</a>`;
    }
    if (link.issues.includes('Very short link text')) {
      return `<a href="${link.href}">Go</a>`;
    }
    return `<a href="${link.href}">${link.text}</a>`;
  }

  private getAfterExample(link: any): string {
    if (link.issues.includes('Generic link text')) {
      return `<a href="${link.href}">Learn about our accessibility features</a>`;
    }
    if (link.issues.includes('Duplicate link text')) {
      return `<a href="/page1">Read more about accessibility</a>\n<a href="/page2">Read more about usability</a>`;
    }
    if (link.issues.includes('Very short link text')) {
      return `<a href="${link.href}">Go to main content</a>`;
    }
    return `<a href="${link.href}" aria-label="Descriptive link purpose">${link.text}</a>`;
  }

  private getFixDescription(issues: string[]): string {
    if (issues.includes('Generic link text')) {
      return 'Replace generic text with descriptive link purpose';
    }
    if (issues.includes('Duplicate link text')) {
      return 'Make link text unique or ensure same destination';
    }
    if (issues.includes('Very short link text')) {
      return 'Expand link text to be more descriptive';
    }
    return 'Make link purpose clear from text or context';
  }

  private getCodeExample(issueType: string): string {
    switch (issueType) {
      case 'Generic link text':
        return `
<!-- Before: Generic link text -->
<p>Our accessibility guide covers WCAG compliance. <a href="/guide">Click here</a> for details.</p>

<!-- After: Descriptive link text -->
<p>Our accessibility guide covers WCAG compliance. <a href="/guide">Read our accessibility guide</a> for details.</p>

<!-- Alternative: Using aria-label -->
<p>Our accessibility guide covers WCAG compliance. <a href="/guide" aria-label="Read our accessibility guide">Click here</a> for details.</p>
        `;
      case 'Duplicate link text with different destinations':
        return `
<!-- Before: Duplicate link text -->
<article>
  <h2>Product A</h2>
  <p>Description of Product A</p>
  <a href="/product-a">Learn more</a>
</article>
<article>
  <h2>Product B</h2>
  <p>Description of Product B</p>
  <a href="/product-b">Learn more</a>
</article>

<!-- After: Unique link text -->
<article>
  <h2>Product A</h2>
  <p>Description of Product A</p>
  <a href="/product-a">Learn more about Product A</a>
</article>
<article>
  <h2>Product B</h2>
  <p>Description of Product B</p>
  <a href="/product-b">Learn more about Product B</a>
</article>
        `;
      default:
        return 'Use descriptive, unique link text that clearly indicates the link purpose';
    }
  }
}
