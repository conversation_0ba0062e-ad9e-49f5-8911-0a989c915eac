/**
 * WCAG-059: Concurrent Input Mechanisms Check (2.5.6 Level AAA)
 * 65% Automated - Detects restrictions on input mechanisms
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced, WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface ConcurrentInputMechanismsConfig extends EnhancedCheckConfig {
  enableAdvancedInputTracking?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
}

export class ConcurrentInputMechanismsCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();

  async performCheck(config: ConcurrentInputMechanismsConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: ConcurrentInputMechanismsConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAdvancedInputTracking: true,
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-059',
      'Concurrent Input Mechanisms',
      'operable',
      0.0305,
      'AAA',
      enhancedConfig,
      this.executeConcurrentInputMechanismsCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with input mechanism analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-059',
        ruleName: 'Concurrent Input Mechanisms',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.65,
          checkType: 'input-mechanism-analysis',
          inputRestrictionDetection: true,
          multiModalSupport: true,
          advancedInputTracking: enhancedConfig.enableAdvancedInputTracking,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.7,
        maxEvidenceItems: 25,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeConcurrentInputMechanismsCheck(
    page: Page,
    config: ConcurrentInputMechanismsConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze input mechanism restrictions
    const inputAnalysis = await page.evaluate(() => {
      const restrictiveElements: Array<{
        selector: string;
        tagName: string;
        restrictionType: string;
        hasPointerEvents: boolean;
        hasTouchEvents: boolean;
        hasKeyboardEvents: boolean;
        hasMouseEvents: boolean;
        eventListeners: string[];
        attributes: Record<string, string>;
        cssProperties: Record<string, string>;
      }> = [];

      // Check for elements that might restrict input mechanisms
      const allElements = document.querySelectorAll('*');
      
      allElements.forEach((element, index) => {
        const computedStyle = window.getComputedStyle(element);
        const eventListeners: string[] = [];
        let hasRestrictions = false;
        let restrictionType = '';

        // Check for CSS properties that might restrict input
        const touchAction = computedStyle.touchAction;
        const pointerEvents = computedStyle.pointerEvents;
        const userSelect = computedStyle.userSelect;

        // Check for restrictive touch-action values
        if (touchAction && touchAction !== 'auto' && touchAction !== 'manipulation') {
          if (touchAction === 'none') {
            hasRestrictions = true;
            restrictionType = 'touch-disabled';
            eventListeners.push('touch-action: none');
          } else if (touchAction.includes('pan-') && !touchAction.includes('pinch-zoom')) {
            hasRestrictions = true;
            restrictionType = 'touch-restricted';
            eventListeners.push(`touch-action: ${touchAction}`);
          }
        }

        // Check for pointer-events restrictions
        if (pointerEvents === 'none') {
          hasRestrictions = true;
          restrictionType = 'pointer-disabled';
          eventListeners.push('pointer-events: none');
        }

        // Check for JavaScript that might detect and restrict input types
        const onclickHandler = element.getAttribute('onclick');
        if (onclickHandler) {
          const restrictivePatterns = [
            /touch.*only/i,
            /mouse.*only/i,
            /pointer.*only/i,
            /keyboard.*only/i,
            /preventDefault.*touch/i,
            /preventDefault.*mouse/i,
            /stopPropagation.*touch/i,
          ];

          restrictivePatterns.forEach(pattern => {
            if (pattern.test(onclickHandler)) {
              hasRestrictions = true;
              restrictionType = 'js-input-restriction';
              eventListeners.push('onclick-restriction');
            }
          });
        }

        // Check for data attributes that suggest input restrictions
        const restrictiveAttributes = [
          'data-touch-only',
          'data-mouse-only',
          'data-keyboard-only',
          'data-pointer-only',
          'data-no-touch',
          'data-no-mouse',
          'data-no-keyboard'
        ];

        restrictiveAttributes.forEach(attr => {
          if (element.hasAttribute(attr)) {
            hasRestrictions = true;
            restrictionType = 'attribute-restriction';
            eventListeners.push(attr);
          }
        });

        // Check for classes that suggest input restrictions
        const restrictiveClasses = [
          'touch-only',
          'mouse-only',
          'keyboard-only',
          'pointer-only',
          'no-touch',
          'no-mouse',
          'no-keyboard',
          'desktop-only',
          'mobile-only'
        ];

        restrictiveClasses.forEach(className => {
          if (element.classList.contains(className)) {
            hasRestrictions = true;
            restrictionType = 'class-restriction';
            eventListeners.push(`class:${className}`);
          }
        });

        if (hasRestrictions) {
          restrictiveElements.push({
            selector: generateSelector(element, index),
            tagName: element.tagName.toLowerCase(),
            restrictionType,
            hasPointerEvents: pointerEvents !== 'none',
            hasTouchEvents: touchAction !== 'none',
            hasKeyboardEvents: element.hasAttribute('tabindex') || ['button', 'a', 'input', 'textarea', 'select'].includes(element.tagName.toLowerCase()),
            hasMouseEvents: true, // Assume mouse events unless explicitly disabled
            eventListeners,
            attributes: getRelevantAttributes(element),
            cssProperties: {
              touchAction: touchAction || 'auto',
              pointerEvents: pointerEvents || 'auto',
              userSelect: userSelect || 'auto',
            },
          });
        }
      });

      return {
        restrictiveElements,
        totalElements: allElements.length,
      };

      function generateSelector(element: Element, index: number): string {
        if (element.id) return `#${element.id}`;
        if (element.className) {
          const classes = element.className.split(' ').filter(c => c.trim());
          if (classes.length > 0) return `.${classes[0]}`;
        }
        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function getRelevantAttributes(element: Element): Record<string, string> {
        const attrs: Record<string, string> = {};
        const relevantAttrs = ['id', 'class', 'role', 'tabindex', 'onclick'];
        
        relevantAttrs.forEach(attr => {
          const value = element.getAttribute(attr);
          if (value) attrs[attr] = value;
        });

        return attrs;
      }
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const elementCount = inputAnalysis.restrictiveElements.length;

    if (elementCount > 0) {
      // Categorize restrictions by severity
      const severeRestrictions = inputAnalysis.restrictiveElements.filter(
        el => el.restrictionType === 'touch-disabled' || el.restrictionType === 'pointer-disabled'
      );
      const moderateRestrictions = inputAnalysis.restrictiveElements.filter(
        el => el.restrictionType === 'touch-restricted' || el.restrictionType === 'js-input-restriction'
      );
      const minorRestrictions = inputAnalysis.restrictiveElements.filter(
        el => el.restrictionType === 'attribute-restriction' || el.restrictionType === 'class-restriction'
      );

      // Calculate score penalties
      if (severeRestrictions.length > 0) {
        score -= Math.min(50, severeRestrictions.length * 20);
        issues.push(`${severeRestrictions.length} elements completely disable input mechanisms`);
      }

      if (moderateRestrictions.length > 0) {
        score -= Math.min(30, moderateRestrictions.length * 10);
        issues.push(`${moderateRestrictions.length} elements restrict specific input mechanisms`);
      }

      if (minorRestrictions.length > 0) {
        score -= Math.min(20, minorRestrictions.length * 5);
        issues.push(`${minorRestrictions.length} elements may have input mechanism preferences`);
      }

      evidence.push({
        type: 'interaction',
        description: 'Elements with input mechanism restrictions',
        value: `Found ${elementCount} elements that may restrict concurrent input mechanisms`,
        elementCount,
        affectedSelectors: inputAnalysis.restrictiveElements.map(el => el.selector),
        severity: severeRestrictions.length > 0 ? 'error' : 'warning',
        fixExample: {
          before: '.element { touch-action: none; pointer-events: none; }',
          after: '.element { touch-action: manipulation; /* Allow all input types */ }',
          description: 'Remove restrictions on input mechanisms to allow concurrent use',
          codeExample: `
/* Before: Restrictive input handling */
.touch-only {
  touch-action: none;
  pointer-events: none;
}

.mouse-only {
  /* JavaScript: if (!event.type.includes('mouse')) return; */
}

/* After: Inclusive input handling */
.inclusive-element {
  touch-action: manipulation; /* Allow touch and mouse */
  /* Support all input types simultaneously */
}

/* JavaScript: Handle all input types */
element.addEventListener('click', handleInteraction);
element.addEventListener('keydown', handleKeyboard);
element.addEventListener('touchstart', handleTouch);
// Don't prevent or restrict any input mechanism
          `,
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/concurrent-input-mechanisms.html',
            'https://developer.mozilla.org/en-US/docs/Web/CSS/touch-action',
            'https://developer.mozilla.org/en-US/docs/Web/API/Pointer_events'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: inputAnalysis.totalElements,
          checkSpecificData: {
            severeRestrictions: severeRestrictions.length,
            moderateRestrictions: moderateRestrictions.length,
            minorRestrictions: minorRestrictions.length,
            restrictionTypes: [...new Set(inputAnalysis.restrictiveElements.map(el => el.restrictionType))],
          },
        },
      });

      // Add specific examples for each type of restriction
      inputAnalysis.restrictiveElements.slice(0, 5).forEach(element => {
        const severityMap: Record<string, 'error' | 'warning' | 'info'> = {
          'touch-disabled': 'error',
          'pointer-disabled': 'error',
          'touch-restricted': 'warning',
          'js-input-restriction': 'warning',
          'attribute-restriction': 'info',
          'class-restriction': 'info',
        };

        evidence.push({
          type: 'code',
          description: `Input restriction: ${element.restrictionType}`,
          value: `<${element.tagName} ${Object.entries(element.attributes)
            .map(([key, value]) => `${key}="${value}"`)
            .join(' ')}>`,
          selector: element.selector,
          severity: severityMap[element.restrictionType] || 'warning',
          metadata: {
            checkSpecificData: {
              restrictionType: element.restrictionType,
              eventListeners: element.eventListeners,
              cssProperties: element.cssProperties,
            },
          },
        });
      });

      recommendations.push('Remove restrictions that prevent concurrent use of input mechanisms');
      recommendations.push('Ensure touch, mouse, keyboard, and other input methods can be used simultaneously');
      recommendations.push('Avoid CSS properties like "touch-action: none" unless absolutely necessary');
      recommendations.push('Test with multiple input devices to ensure all work together');
      
      if (severeRestrictions.length > 0) {
        recommendations.push('CRITICAL: Some elements completely disable input mechanisms');
      }
    } else {
      // No input restrictions found
      evidence.push({
        type: 'info',
        description: 'No input mechanism restrictions detected',
        value: 'Page appears to support concurrent use of different input mechanisms',
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: inputAnalysis.totalElements,
          checkSpecificData: {
            noRestrictionsFound: true,
          },
        },
      });
    }

    // Ensure score doesn't go below 0
    score = Math.max(0, score);

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
