/**
 * WCAG-052: Character Key Shortcuts Check
 * Success Criterion: 2.1.4 Character Key Shortcuts (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface CharacterKeyShortcutsConfig extends EnhancedCheckConfig {
  enableAdvancedKeyboardTracking?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
}

export class CharacterKeyShortcutsCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();

  async performCheck(config: CharacterKeyShortcutsConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: CharacterKeyShortcutsConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAdvancedKeyboardTracking: true,
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-052',
      'Character Key Shortcuts',
      'operable',
      0.0458,
      'A',
      enhancedConfig,
      this.executeCharacterKeyShortcutsCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with character key shortcut analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-052',
        ruleName: 'Character Key Shortcuts',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.8,
          checkType: 'character-key-shortcut-analysis',
          keyboardShortcutDetection: true,
          shortcutConfigurationValidation: true,
          advancedKeyboardTracking: enhancedConfig.enableAdvancedKeyboardTracking,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 25,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeCharacterKeyShortcutsCheck(
    page: Page,
    _config: CharacterKeyShortcutsConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze character key shortcuts
    const shortcutAnalysis = await page.evaluate(() => {
      const problematicShortcuts: Array<{
        key: string;
        selector: string;
        description: string;
        hasToggle: boolean;
        hasRemap: boolean;
        hasModifier: boolean;
        isInFormField: boolean;
        issues: string[];
        severity: 'error' | 'warning' | 'info';
      }> = [];

      // Check for keyboard event listeners
      const elementsWithKeyHandlers = document.querySelectorAll('[onkeydown], [onkeyup], [onkeypress]');
      const characterKeys = /^[a-zA-Z0-9]$/;
      
      // Analyze inline event handlers
      elementsWithKeyHandlers.forEach((element, index) => {
        const keydownHandler = element.getAttribute('onkeydown') || '';
        const keyupHandler = element.getAttribute('onkeyup') || '';
        const keypressHandler = element.getAttribute('onkeypress') || '';
        
        const allHandlers = keydownHandler + keyupHandler + keypressHandler;
        
        // Look for single character key patterns
        const singleCharMatches = allHandlers.match(/event\.key\s*===?\s*['"`]([a-zA-Z0-9])['"`]/g);
        const keyCodeMatches = allHandlers.match(/event\.keyCode\s*===?\s*(\d+)/g);
        
        if (singleCharMatches || keyCodeMatches) {
          const keys = [];
          
          if (singleCharMatches) {
            singleCharMatches.forEach(match => {
              const key = match.match(/['"`]([a-zA-Z0-9])['"`]/)?.[1];
              if (key) keys.push(key);
            });
          }
          
          if (keyCodeMatches) {
            keyCodeMatches.forEach(match => {
              const code = parseInt(match.match(/(\d+)/)?.[1] || '0');
              // Convert common key codes to characters
              if ((code >= 65 && code <= 90) || (code >= 48 && code <= 57)) {
                keys.push(String.fromCharCode(code));
              }
            });
          }
          
          keys.forEach(key => {
            const hasModifierCheck = allHandlers.includes('ctrlKey') || 
                                   allHandlers.includes('altKey') || 
                                   allHandlers.includes('shiftKey') ||
                                   allHandlers.includes('metaKey');
            
            const isInFormField = element.tagName === 'INPUT' || 
                                 element.tagName === 'TEXTAREA' ||
                                 element.hasAttribute('contenteditable');
            
            const issues: string[] = [];
            let severity: 'error' | 'warning' | 'info' = 'warning';
            
            if (!hasModifierCheck && !isInFormField) {
              issues.push('Single character shortcut without modifier key');
              severity = 'error';
            }
            
            problematicShortcuts.push({
              key,
              selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
              description: `Character shortcut "${key}" detected`,
              hasToggle: false, // Cannot determine from static analysis
              hasRemap: false, // Cannot determine from static analysis
              hasModifier: hasModifierCheck,
              isInFormField,
              issues,
              severity,
            });
          });
        }
      });

      // Check for JavaScript event listeners (common patterns)
      const scripts = document.querySelectorAll('script');
      scripts.forEach((script) => {
        const content = script.textContent || '';
        
        // Look for addEventListener with single character keys
        const addEventListenerMatches = content.match(/addEventListener\s*\(\s*['"`]key\w+['"`]\s*,.*?\)/gs);
        
        if (addEventListenerMatches) {
          addEventListenerMatches.forEach(match => {
            // Look for single character key checks in the handler
            const singleCharPattern = /event\.key\s*===?\s*['"`]([a-zA-Z0-9])['"`]/g;
            const charMatches = match.match(singleCharPattern);
            
            if (charMatches) {
              charMatches.forEach(charMatch => {
                const key = charMatch.match(/['"`]([a-zA-Z0-9])['"`]/)?.[1];
                if (key) {
                  const hasModifierCheck = match.includes('ctrlKey') || 
                                         match.includes('altKey') || 
                                         match.includes('shiftKey') ||
                                         match.includes('metaKey');
                  
                  const issues: string[] = [];
                  let severity: 'error' | 'warning' | 'info' = 'warning';
                  
                  if (!hasModifierCheck) {
                    issues.push('Single character shortcut without modifier key');
                    severity = 'error';
                  }
                  
                  problematicShortcuts.push({
                    key,
                    selector: 'script',
                    description: `JavaScript character shortcut "${key}" detected`,
                    hasToggle: false,
                    hasRemap: false,
                    hasModifier: hasModifierCheck,
                    isInFormField: false,
                    issues,
                    severity,
                  });
                }
              });
            }
          });
        }
      });

      // Check for accesskey attributes (single character shortcuts)
      const accessKeyElements = document.querySelectorAll('[accesskey]');
      accessKeyElements.forEach((element, index) => {
        const accessKey = element.getAttribute('accesskey') || '';
        
        if (accessKey.length === 1 && characterKeys.test(accessKey)) {
          problematicShortcuts.push({
            key: accessKey,
            selector: `[accesskey="${accessKey}"]:nth-of-type(${index + 1})`,
            description: `AccessKey shortcut "${accessKey}" detected`,
            hasToggle: false,
            hasRemap: false,
            hasModifier: true, // AccessKey typically uses Alt modifier
            isInFormField: false,
            issues: ['AccessKey shortcut may conflict with assistive technology'],
            severity: 'warning',
          });
        }
      });

      return {
        problematicShortcuts,
        totalShortcuts: problematicShortcuts.length,
        singleCharShortcuts: problematicShortcuts.filter(s => !s.hasModifier && !s.isInFormField).length,
        accessKeyShortcuts: problematicShortcuts.filter(s => s.selector.includes('accesskey')).length,
      };
    });

    let score = 100;
    const elementCount = shortcutAnalysis.totalShortcuts;
    const scanDuration = Date.now() - startTime;

    if (elementCount > 0) {
      // Calculate score based on severity
      shortcutAnalysis.problematicShortcuts.forEach((shortcut) => {
        const deduction = shortcut.severity === 'error' ? 15 : 
                         shortcut.severity === 'warning' ? 8 : 3;
        score = Math.max(0, score - deduction);
      });

      issues.push(`${elementCount} character key shortcuts found`);
      if (shortcutAnalysis.singleCharShortcuts > 0) {
        issues.push(`${shortcutAnalysis.singleCharShortcuts} single character shortcuts without modifiers`);
      }
      if (shortcutAnalysis.accessKeyShortcuts > 0) {
        issues.push(`${shortcutAnalysis.accessKeyShortcuts} accesskey shortcuts may conflict with assistive technology`);
      }

      shortcutAnalysis.problematicShortcuts.forEach((shortcut) => {
        evidence.push({
          type: 'code',
          description: `Character key shortcut issue: ${shortcut.description}`,
          value: `Key: "${shortcut.key}" | Modifier: ${shortcut.hasModifier} | Form field: ${shortcut.isInFormField}`,
          selector: shortcut.selector,
          elementCount: 1,
          affectedSelectors: [shortcut.selector],
          severity: shortcut.severity,
          fixExample: {
            before: this.getBeforeExample(shortcut),
            after: this.getAfterExample(shortcut),
            description: this.getFixDescription(shortcut.issues),
            codeExample: this.getCodeExample('character_shortcuts'),
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/character-key-shortcuts.html',
              'https://www.w3.org/WAI/WCAG21/Techniques/G217',
              'https://www.w3.org/WAI/WCAG21/Techniques/F99'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              key: shortcut.key,
              hasToggle: shortcut.hasToggle,
              hasRemap: shortcut.hasRemap,
              hasModifier: shortcut.hasModifier,
              isInFormField: shortcut.isInFormField,
              issues: shortcut.issues,
            },
          },
        });
      });
    }

    // Add recommendations
    recommendations.push('Use modifier keys (Ctrl, Alt, Shift) with character shortcuts');
    recommendations.push('Provide mechanism to turn off or remap character shortcuts');
    recommendations.push('Avoid single character shortcuts that conflict with assistive technology');
    recommendations.push('Consider using function keys or key combinations instead');
    recommendations.push('Test shortcuts with screen readers and voice input software');

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private getBeforeExample(shortcut: any): string {
    if (shortcut.selector.includes('accesskey')) {
      return `<button accesskey="${shortcut.key}">Save</button>`;
    }
    return `<div onkeydown="if(event.key === '${shortcut.key}') doAction()">Element</div>`;
  }

  private getAfterExample(shortcut: any): string {
    if (shortcut.selector.includes('accesskey')) {
      return '<button>Save <span class="shortcut">(Ctrl+S)</span></button>';
    }
    return `<div onkeydown="if(event.ctrlKey && event.key === '${shortcut.key}') doAction()">Element</div>`;
  }

  private getFixDescription(issues: string[]): string {
    if (issues.includes('without modifier key')) {
      return 'Add modifier key requirement (Ctrl, Alt, Shift) to character shortcuts';
    }
    if (issues.includes('AccessKey shortcut')) {
      return 'Replace accesskey with standard keyboard shortcuts or provide toggle mechanism';
    }
    return 'Ensure character shortcuts can be turned off or remapped';
  }

  private getCodeExample(type: string): string {
    return `
<!-- Before: Single character shortcuts -->
<div onkeydown="handleShortcut(event)">
  <script>
  function handleShortcut(event) {
    if (event.key === 's') {
      save(); // Conflicts with screen reader shortcuts
    }
    if (event.key === 'h') {
      showHelp(); // Conflicts with voice input
    }
  }
  </script>
</div>

<!-- After: Modifier-based shortcuts with toggle -->
<div onkeydown="handleShortcut(event)">
  <button onclick="toggleShortcuts()">
    Shortcuts: <span id="shortcut-status">Enabled</span>
  </button>
  
  <script>
  let shortcutsEnabled = true;
  
  function handleShortcut(event) {
    if (!shortcutsEnabled) return;
    
    // Use modifier keys
    if (event.ctrlKey && event.key === 's') {
      event.preventDefault();
      save();
    }
    if (event.ctrlKey && event.shiftKey && event.key === 'h') {
      event.preventDefault();
      showHelp();
    }
  }
  
  function toggleShortcuts() {
    shortcutsEnabled = !shortcutsEnabled;
    document.getElementById('shortcut-status').textContent = 
      shortcutsEnabled ? 'Enabled' : 'Disabled';
  }
  
  // Alternative: Remap functionality
  function remapShortcut(oldKey, newKey) {
    // Implementation for remapping shortcuts
  }
  </script>
</div>

<!-- Best practice: Use standard shortcuts -->
<div>
  <button onclick="save()" title="Save (Ctrl+S)">Save</button>
  <button onclick="showHelp()" title="Help (F1)">Help</button>
</div>
    `;
  }
}
