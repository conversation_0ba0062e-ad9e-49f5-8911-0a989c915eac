/**
 * Browser Pool for WCAG Scanning Optimization
 * Provides connection pooling, page reuse, and memory management for Puppeteer instances
 */

import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { getPerformanceConfig } from '../../../config/performance';
import logger from '../../../utils/logger';

export interface BrowserPoolConfig {
  maxBrowsers?: number;
  maxPagesPerBrowser?: number;
  browserTimeout?: number;
  pageTimeout?: number;
  enableMemoryMonitoring?: boolean;
  memoryThresholdMB?: number;
}

export interface BrowserInstance {
  browser: Browser;
  pages: Page[];
  createdAt: Date;
  lastUsed: Date;
  memoryUsage: number;
  isHealthy: boolean;
}

export interface PageInstance {
  page: Page;
  browser: Browser;
  isInUse: boolean;
  createdAt: Date;
  lastUsed: Date;
  scanCount: number;
}

/**
 * Optimized browser pool for WCAG scanning with intelligent resource management
 */
export class BrowserPool {
  private static instance: BrowserPool;
  private browsers: Map<string, BrowserInstance> = new Map();
  private availablePages: PageInstance[] = [];
  private inUsePages: Map<string, PageInstance> = new Map();
  private config: BrowserPoolConfig;
  private performanceConfig = getPerformanceConfig();
  private cleanupInterval: NodeJS.Timeout | null = null;
  private memoryMonitorInterval: NodeJS.Timeout | null = null;

  private constructor(config: BrowserPoolConfig = {}) {
    this.config = {
      maxBrowsers: config.maxBrowsers || this.performanceConfig.maxConcurrentAnalyses,
      maxPagesPerBrowser: config.maxPagesPerBrowser || 3,
      browserTimeout: config.browserTimeout || 300000, // 5 minutes
      pageTimeout: config.pageTimeout || 120000, // 2 minutes
      enableMemoryMonitoring: config.enableMemoryMonitoring ?? true,
      memoryThresholdMB: config.memoryThresholdMB || this.performanceConfig.maxMemoryUsageMB,
    };

    this.startCleanupInterval();
    if (this.config.enableMemoryMonitoring) {
      this.startMemoryMonitoring();
    }

    logger.info('🌐 Browser pool initialized', {
      maxBrowsers: this.config.maxBrowsers,
      maxPagesPerBrowser: this.config.maxPagesPerBrowser,
      memoryThreshold: this.config.memoryThresholdMB,
    });
  }

  /**
   * Get singleton instance of browser pool
   */
  static getInstance(config?: BrowserPoolConfig): BrowserPool {
    if (!BrowserPool.instance) {
      BrowserPool.instance = new BrowserPool(config);
    }
    return BrowserPool.instance;
  }

  /**
   * Get an available page for scanning
   */
  async getPage(scanId: string): Promise<PageInstance> {
    logger.debug(`🔍 Getting page for scan: ${scanId}`);

    // Try to get an available page first
    if (this.availablePages.length > 0) {
      const pageInstance = this.availablePages.pop()!;
      pageInstance.isInUse = true;
      pageInstance.lastUsed = new Date();
      pageInstance.scanCount++;
      this.inUsePages.set(scanId, pageInstance);

      logger.debug(`♻️ Reusing existing page for scan: ${scanId}`);
      return pageInstance;
    }

    // Create new page if possible
    const pageInstance = await this.createNewPage();
    pageInstance.isInUse = true;
    pageInstance.lastUsed = new Date();
    pageInstance.scanCount = 1;
    this.inUsePages.set(scanId, pageInstance);

    logger.debug(`🆕 Created new page for scan: ${scanId}`);
    return pageInstance;
  }

  /**
   * Release a page back to the pool
   */
  async releasePage(scanId: string): Promise<void> {
    const pageInstance = this.inUsePages.get(scanId);
    if (!pageInstance) {
      logger.warn(`⚠️ Attempted to release unknown page for scan: ${scanId}`);
      return;
    }

    this.inUsePages.delete(scanId);
    pageInstance.isInUse = false;
    pageInstance.lastUsed = new Date();

    // Check if page should be recycled (after too many uses or if unhealthy)
    const shouldRecycle =
      pageInstance.scanCount > 10 || !(await this.isPageHealthy(pageInstance.page));

    if (shouldRecycle) {
      logger.debug(`🔄 Recycling page after ${pageInstance.scanCount} scans`);
      await this.recyclePage(pageInstance);
    } else {
      // Clean up page state before returning to pool
      await this.cleanupPageState(pageInstance.page);
      this.availablePages.push(pageInstance);
      logger.debug(`✅ Released page back to pool for scan: ${scanId}`);
    }
  }

  /**
   * Create a new page instance
   */
  private async createNewPage(): Promise<PageInstance> {
    const browser = await this.getOrCreateBrowser();
    const page = await browser.newPage();

    // Configure page for optimal performance
    await this.configurePage(page);

    return {
      page,
      browser,
      isInUse: false,
      createdAt: new Date(),
      lastUsed: new Date(),
      scanCount: 0,
    };
  }

  /**
   * Get or create a browser instance
   */
  private async getOrCreateBrowser(): Promise<Browser> {
    // Find browser with available capacity
    for (const [browserId, browserInstance] of this.browsers) {
      if (
        browserInstance.pages.length < this.config.maxPagesPerBrowser! &&
        browserInstance.isHealthy
      ) {
        return browserInstance.browser;
      }
    }

    // Create new browser if under limit
    if (this.browsers.size < this.config.maxBrowsers!) {
      return await this.createNewBrowser();
    }

    // Wait for available browser
    return new Promise((resolve) => {
      const checkAvailable = () => {
        for (const [browserId, browserInstance] of this.browsers) {
          if (
            browserInstance.pages.length < this.config.maxPagesPerBrowser! &&
            browserInstance.isHealthy
          ) {
            resolve(browserInstance.browser);
            return;
          }
        }
        setTimeout(checkAvailable, 100);
      };
      checkAvailable();
    });
  }

  /**
   * Create a new browser instance
   */
  private async createNewBrowser(): Promise<Browser> {
    logger.debug('🌐 Creating new browser instance');

    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--memory-pressure-off',
        '--max_old_space_size=4096',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI',
        '--disable-ipc-flooding-protection',
      ],
    });

    const browserId = `browser-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const browserInstance: BrowserInstance = {
      browser,
      pages: [],
      createdAt: new Date(),
      lastUsed: new Date(),
      memoryUsage: 0,
      isHealthy: true,
    };

    this.browsers.set(browserId, browserInstance);

    // Handle browser disconnection
    browser.on('disconnected', () => {
      logger.warn(`🔌 Browser disconnected: ${browserId}`);
      this.browsers.delete(browserId);
    });

    logger.info(`✅ Created new browser: ${browserId}`);
    return browser;
  }

  /**
   * Configure page for optimal WCAG scanning
   */
  private async configurePage(page: Page): Promise<void> {
    // Set viewport for consistent testing
    await page.setViewport({ width: 1920, height: 1080 });

    // Set user agent
    await page.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 WCAG-Scanner/1.0',
    );

    // Optimize for performance
    await page.setDefaultNavigationTimeout(this.performanceConfig.networkTimeoutMs);
    await page.setDefaultTimeout(this.performanceConfig.analysisTimeoutMs);

    // Block unnecessary resources to improve performance
    await page.setRequestInterception(true);
    page.on('request', (request) => {
      const resourceType = request.resourceType();
      if (['font', 'media'].includes(resourceType)) {
        request.abort();
      } else {
        request.continue();
      }
    });

    // Add error handling
    page.on('error', (error) => {
      logger.error('Page error:', { error: error.message, stack: error.stack });
    });

    page.on('pageerror', (error) => {
      logger.error('Page script error:', { error: error.message, stack: error.stack });
    });
  }

  /**
   * Clean up page state between scans
   */
  private async cleanupPageState(page: Page): Promise<void> {
    try {
      // Clear any existing navigation
      await page.evaluate(() => {
        // Clear any timers or intervals
        for (let i = 1; i < 99999; i++) {
          window.clearTimeout(i);
          window.clearInterval(i);
        }

        // Clear any event listeners
        window.removeEventListener?.('beforeunload', () => {});
        window.removeEventListener?.('unload', () => {});

        // Clear any WCAG-specific globals
        delete (window as any).wcagDynamicChanges;
        delete (window as any).wcagScanData;
      });

      // Clear cookies and local storage
      await page.deleteCookie(...(await page.cookies()));
      await page.evaluate(() => {
        localStorage.clear();
        sessionStorage.clear();
      });
    } catch (error) {
      logger.warn('Error cleaning up page state:', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Check if page is healthy
   */
  private async isPageHealthy(page: Page): Promise<boolean> {
    try {
      await page.evaluate(() => document.readyState);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Recycle a page (close and remove from tracking)
   */
  private async recyclePage(pageInstance: PageInstance): Promise<void> {
    try {
      if (!pageInstance.page.isClosed()) {
        await pageInstance.page.close();
      }
    } catch (error) {
      logger.warn('Error closing page during recycling:', {
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Start cleanup interval for unused resources
   */
  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(async () => {
      await this.cleanupUnusedResources();
    }, 60000); // Run every minute
  }

  /**
   * Start memory monitoring
   */
  private startMemoryMonitoring(): void {
    this.memoryMonitorInterval = setInterval(async () => {
      await this.monitorMemoryUsage();
    }, 30000); // Check every 30 seconds
  }

  /**
   * Clean up unused resources
   */
  private async cleanupUnusedResources(): Promise<void> {
    const now = new Date();
    const pageTimeout = this.config.pageTimeout!;
    const browserTimeout = this.config.browserTimeout!;

    // Clean up old available pages
    this.availablePages = this.availablePages.filter((pageInstance) => {
      const age = now.getTime() - pageInstance.lastUsed.getTime();
      if (age > pageTimeout) {
        this.recyclePage(pageInstance);
        return false;
      }
      return true;
    });

    // Clean up old browsers
    for (const [browserId, browserInstance] of this.browsers) {
      const age = now.getTime() - browserInstance.lastUsed.getTime();
      if (age > browserTimeout && browserInstance.pages.length === 0) {
        try {
          await browserInstance.browser.close();
          this.browsers.delete(browserId);
          logger.debug(`🗑️ Cleaned up old browser: ${browserId}`);
        } catch (error) {
          logger.warn(`Error cleaning up browser ${browserId}:`, {
            error: error instanceof Error ? error.message : String(error),
          });
        }
      }
    }
  }

  /**
   * Monitor memory usage and trigger cleanup if needed
   */
  private async monitorMemoryUsage(): Promise<void> {
    const memoryUsage = process.memoryUsage();
    const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);

    if (heapUsedMB > this.config.memoryThresholdMB!) {
      logger.warn(`🚨 High memory usage detected: ${heapUsedMB}MB`);
      await this.emergencyCleanup();
    }
  }

  /**
   * Emergency cleanup when memory usage is high
   */
  private async emergencyCleanup(): Promise<void> {
    logger.warn('🚨 Performing emergency cleanup due to high memory usage');

    // Close all available pages
    for (const pageInstance of this.availablePages) {
      await this.recyclePage(pageInstance);
    }
    this.availablePages = [];

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    logger.info('✅ Emergency cleanup completed');
  }

  /**
   * Get pool statistics
   */
  getStats(): {
    totalBrowsers: number;
    totalPages: number;
    availablePages: number;
    inUsePages: number;
    memoryUsageMB: number;
  } {
    const memoryUsage = process.memoryUsage();
    return {
      totalBrowsers: this.browsers.size,
      totalPages: this.availablePages.length + this.inUsePages.size,
      availablePages: this.availablePages.length,
      inUsePages: this.inUsePages.size,
      memoryUsageMB: Math.round(memoryUsage.heapUsed / 1024 / 1024),
    };
  }

  /**
   * Shutdown the browser pool
   */
  async shutdown(): Promise<void> {
    logger.info('🔄 Shutting down browser pool');

    // Clear intervals
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    if (this.memoryMonitorInterval) {
      clearInterval(this.memoryMonitorInterval);
    }

    // Close all pages
    for (const pageInstance of this.availablePages) {
      await this.recyclePage(pageInstance);
    }
    for (const pageInstance of this.inUsePages.values()) {
      await this.recyclePage(pageInstance);
    }

    // Close all browsers
    for (const browserInstance of this.browsers.values()) {
      try {
        await browserInstance.browser.close();
      } catch (error) {
        logger.warn('Error closing browser during shutdown:', {
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    this.browsers.clear();
    this.availablePages = [];
    this.inUsePages.clear();

    logger.info('✅ Browser pool shutdown completed');
  }
}

export default BrowserPool;
