/**
 * WCAG Rule 8: Error Identification - 3.3.1
 * 90% Automated - Manual review for error message clarity
 */

import { Page } from 'puppeteer';
import {
  ManualReviewConfig,
  ManualReviewTemplate,
  ManualReviewItem,
} from '../utils/manual-review-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface ErrorIdentificationConfig extends EnhancedCheckConfig {
  enableFormAccessibilityAnalysis?: boolean;
  enableSemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableErrorPatternRecognition?: boolean;
  enableMessageQualityAnalysis?: boolean;
}

export class ErrorIdentificationCheck {
  private checkTemplate = new ManualReviewTemplate();
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();

  /**
   * Perform error identification check - 90% automated with enhanced evidence
   */
  async performCheck(config: ErrorIdentificationConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: ErrorIdentificationConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'enhance',
        maxExecutionTime: 5000,
      },
      enableFormAccessibilityAnalysis: true,
      enableSemanticValidation: true,
      enableAccessibilityPatterns: true,
      enableErrorPatternRecognition: true,
      enableMessageQualityAnalysis: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-008',
      'Error Identification',
      'understandable',
      0.1,
      'A',
      enhancedConfig,
      this.executeErrorIdentificationCheck.bind(this),
      true, // Requires browser
      true, // Manual review for error message clarity
    );

    // Enhanced evidence standardization with form validation specifics
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-008',
        ruleName: 'Error Identification',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'form-validation-analysis',
          manualReviewRequired: result.manualReviewItems?.length > 0,
          formAnalysis: true,
          errorMessageValidation: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8, // High threshold for form accessibility
        maxEvidenceItems: 40,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  /**
   * Execute comprehensive error identification analysis
   */
  private async executeErrorIdentificationCheck(page: Page, _config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Analyze form validation
    const formAnalysis = await this.analyzeFormValidation(page);

    // Analyze existing error messages
    const errorMessageAnalysis = await this.analyzeErrorMessages(page);

    // Test form submission for error handling
    const submissionAnalysis = await this.testFormSubmission(page);

    // Combine all analyses
    const allAnalyses = [formAnalysis, errorMessageAnalysis, submissionAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedChecks = totalChecks - manualReviewCount;
    const automatedScore =
      automatedChecks > 0 ? Math.round((passedChecks / automatedChecks) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Error identification analysis summary',
      value: `${passedChecks}/${automatedChecks} checks pass automated tests, ${manualReviewCount} require manual review`,
      severity: automatedScore >= 90 ? 'info' : automatedScore >= 70 ? 'warning' : 'error',
    });

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 0.9,
    };
  }

  /**
   * Analyze form validation attributes and patterns
   */
  private async analyzeFormValidation(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      function generateSelector(element: Element, index: number): string {
        if (element.id) {
          return `#${element.id}`;
        }

        if (element.className) {
          const classes = element.className
            .toString()
            .split(' ')
            .filter((c) => c.length > 0);
          if (classes.length > 0) {
            return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
          }
        }

        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      const formInputs = Array.from(
        document.querySelectorAll('input:not([type="hidden"]), select, textarea'),
      );
      let totalChecks = 0;
      let passedChecks = 0;

      if (formInputs.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      formInputs.forEach((input, index) => {
        const element = input as HTMLInputElement;
        const selector = generateSelector(element, index);

        // Check for required field indication
        totalChecks++;
        const isRequired =
          element.hasAttribute('required') || element.getAttribute('aria-required') === 'true';

        if (isRequired) {
          // Check if required fields have proper indication
          const hasRequiredIndicator =
            element.getAttribute('aria-label')?.includes('required') ||
            element.getAttribute('aria-describedby') ||
            element.placeholder?.includes('required') ||
            element.title?.includes('required');

          const parentLabel =
            element.closest('label') || document.querySelector(`label[for="${element.id}"]`);
          const labelHasIndicator =
            parentLabel?.textContent?.includes('*') ||
            parentLabel?.textContent?.includes('required');

          if (hasRequiredIndicator || labelHasIndicator) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'Required field has proper indication',
              value: 'Field is marked as required with appropriate indicator',
              selector,
              severity: 'info',
            });
          } else {
            issues.push(`Required field lacks clear indication: ${selector}`);
            recommendations.push(
              `Add visual and programmatic indication for required field: ${selector}`,
            );
          }
        } else {
          passedChecks++; // Non-required fields pass this check
        }

        // Check for validation patterns
        totalChecks++;
        const hasPattern =
          element.pattern ||
          element.type === 'email' ||
          element.type === 'url' ||
          element.type === 'tel';

        if (hasPattern) {
          // Check if pattern has description
          const hasPatternDescription =
            element.getAttribute('aria-describedby') || element.title || element.placeholder;

          if (hasPatternDescription) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'Input pattern has description',
              value: `Pattern validation with description provided`,
              selector,
              severity: 'info',
            });
          } else {
            manualReviewItems.push({
              selector,
              description: 'Input pattern description verification needed',
              automatedFindings: 'Field has validation pattern but no clear description',
              reviewRequired: 'Verify that users understand the required input format',
              priority: 'medium',
              estimatedTime: 2,
            });
          }
        } else {
          passedChecks++; // Fields without patterns pass this check
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Analyze existing error messages on the page
   */
  private async analyzeErrorMessages(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      // Look for common error message patterns
      const errorSelectors = [
        '.error',
        '.error-message',
        '.field-error',
        '.validation-error',
        '[role="alert"]',
        '[aria-live="polite"]',
        '[aria-live="assertive"]',
        '.invalid',
        '.has-error',
        '.form-error',
      ];

      let errorElements: Element[] = [];
      errorSelectors.forEach((selector) => {
        try {
          const elements = Array.from(document.querySelectorAll(selector));
          errorElements = errorElements.concat(elements);
        } catch (e) {
          // Ignore invalid selectors
        }
      });

      // Remove duplicates
      errorElements = errorElements.filter(
        (element, index, self) => self.indexOf(element) === index,
      );

      const totalChecks = errorElements.length;
      let passedChecks = 0;

      if (errorElements.length === 0) {
        // No error messages found - this could be good or bad
        evidence.push({
          type: 'text',
          description: 'No error messages found on page',
          value: 'Page may not have forms or errors are not currently displayed',
          severity: 'info',
        });
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      errorElements.forEach((element, index) => {
        const selector = element.id ? `#${element.id}` : `.error:nth-of-type(${index + 1})`;
        const errorText = element.textContent?.trim() || '';

        // Check if error message is descriptive
        if (errorText.length > 10 && !errorText.toLowerCase().includes('error')) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: 'Error message is descriptive',
            value: `Message: "${errorText.substring(0, 100)}"`,
            selector,
            severity: 'info',
          });
        } else if (errorText.length <= 10) {
          issues.push(`Error message too short: ${selector}`);
          recommendations.push(`Provide more descriptive error message for ${selector}`);
        } else {
          manualReviewItems.push({
            selector,
            description: 'Error message clarity verification needed',
            automatedFindings: `Error message: "${errorText}"`,
            reviewRequired:
              'Verify that error message clearly explains the problem and how to fix it',
            priority: 'high',
            estimatedTime: 2,
          });
        }

        // Check if error is associated with form field
        const ariaDescribedby = element.id;
        if (ariaDescribedby) {
          const associatedField = document.querySelector(
            `[aria-describedby*="${ariaDescribedby}"]`,
          );
          if (associatedField) {
            evidence.push({
              type: 'text',
              description: 'Error message is associated with form field',
              value: 'Proper aria-describedby association found',
              selector,
              severity: 'info',
            });
          } else {
            issues.push(`Error message not associated with form field: ${selector}`);
            recommendations.push(`Associate error message with form field using aria-describedby`);
          }
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Test form submission for error handling
   */
  private async testFormSubmission(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    const evidence: Array<{
      type:
        | 'text'
        | 'image'
        | 'code'
        | 'measurement'
        | 'interaction'
        | 'info'
        | 'warning'
        | 'error';
      description: string;
      value: string;
      selector?: string;
      severity?: 'info' | 'warning' | 'error' | 'critical';
    }> = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: Array<{
      selector: string;
      description: string;
      automatedFindings: string;
      reviewRequired: string;
      priority: 'high' | 'medium' | 'low';
      estimatedTime: number;
    }> = [];
    let totalChecks = 0;
    let passedChecks = 0;

    try {
      // Find forms on the page
      const forms = await page.$$('form');

      if (forms.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      for (let i = 0; i < Math.min(forms.length, 2); i++) {
        // Test max 2 forms to avoid excessive testing
        totalChecks++;

        try {
          // Try to submit form without filling required fields
          const formSelector = `form:nth-of-type(${i + 1})`;

          // Check if form has required fields
          const hasRequiredFields = await page.evaluate((selector) => {
            const form = document.querySelector(selector) as HTMLFormElement;
            if (!form) return false;

            const requiredInputs = form.querySelectorAll(
              'input[required], select[required], textarea[required]',
            );
            return requiredInputs.length > 0;
          }, formSelector);

          if (hasRequiredFields) {
            // This requires manual testing as automated submission might have side effects
            manualReviewItems.push({
              selector: formSelector,
              description: 'Form error handling verification needed',
              automatedFindings: 'Form has required fields that need validation testing',
              reviewRequired:
                'Test form submission with missing/invalid data to verify error messages appear and are helpful',
              priority: 'high',
              estimatedTime: 5,
            });
          } else {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'Form structure analyzed',
              value: 'Form does not have required fields or validation needs manual testing',
              selector: formSelector,
              severity: 'info',
            });
          }
        } catch (error) {
          manualReviewItems.push({
            selector: `form:nth-of-type(${i + 1})`,
            description: 'Form testing failed - manual verification needed',
            automatedFindings: 'Automated form testing encountered errors',
            reviewRequired:
              'Manually test form submission with invalid data to verify error handling',
            priority: 'high',
            estimatedTime: 5,
          });
        }
      }
    } catch (error) {
      issues.push('Error testing form submission');
      recommendations.push('Manual form submission testing required');
    }

    return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
  }
}
