/**
 * WCAG-057: Status Messages Check (4.1.3 Level AA)
 * 80% Automated - Detects status messages and their accessibility
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced, WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface StatusMessagesConfig extends EnhancedCheckConfig {
  enableAISemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
}

export class StatusMessagesCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();

  async performCheck(config: StatusMessagesConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: StatusMessagesConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAISemanticValidation: true,
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-057',
      'Status Messages',
      'robust',
      0.0458,
      'AA',
      enhancedConfig,
      this.executeStatusMessagesCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with status message analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-057',
        ruleName: 'Status Messages',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.8,
          checkType: 'status-message-analysis',
          ariaLiveRegionDetection: true,
          statusMessageAccessibility: true,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 30,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeStatusMessagesCheck(
    page: Page,
    config: StatusMessagesConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze status messages and their accessibility
    const statusAnalysis = await page.evaluate(() => {
      const statusElements: Array<{
        selector: string;
        element: string;
        messageType: string;
        hasAriaLive: boolean;
        hasAriaAtomic: boolean;
        hasRole: boolean;
        isAccessible: boolean;
        content: string;
        liveValue: string;
        roleValue: string;
        severity: 'error' | 'warning' | 'info';
      }> = [];

      const potentialStatusElements: Array<{
        selector: string;
        element: string;
        messageType: string;
        content: string;
        needsAriaLive: boolean;
        suggestedRole: string;
        suggestedLive: string;
      }> = [];

      // Check for existing status message elements
      const statusSelectors = [
        '[role="status"]',
        '[role="alert"]',
        '[role="alertdialog"]',
        '[role="log"]',
        '[role="marquee"]',
        '[role="timer"]',
        '[aria-live]',
        '.alert',
        '.status',
        '.message',
        '.notification',
        '.toast',
        '.banner',
        '.success',
        '.error',
        '.warning',
        '.info',
        '.feedback',
        '.result',
        '.confirmation',
      ];

      statusSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element, index) => {
          const messageType = determineMessageType(element, selector);
          const hasAriaLive = element.hasAttribute('aria-live');
          const hasAriaAtomic = element.hasAttribute('aria-atomic');
          const hasRole = element.hasAttribute('role');
          const content = element.textContent?.trim().substring(0, 100) || '';
          const liveValue = element.getAttribute('aria-live') || '';
          const roleValue = element.getAttribute('role') || '';
          
          const isAccessible = checkStatusAccessibility(element, messageType);
          const severity = getSeverity(messageType, isAccessible);

          statusElements.push({
            selector: generateSelector(element, index),
            element: element.tagName.toLowerCase(),
            messageType,
            hasAriaLive,
            hasAriaAtomic,
            hasRole,
            isAccessible,
            content,
            liveValue,
            roleValue,
            severity,
          });
        });
      });

      // Check for potential status messages that lack proper markup
      const potentialSelectors = [
        'form + div',
        'form + p',
        '.form-group div:last-child',
        '.field div:last-child',
        'input + div',
        'input + span',
        'button + div',
        'button + span',
        '[id*="message"]',
        '[id*="status"]',
        '[id*="result"]',
        '[id*="feedback"]',
        '[class*="message"]',
        '[class*="status"]',
        '[class*="result"]',
        '[class*="feedback"]',
      ];

      potentialSelectors.forEach(selector => {
        try {
          const elements = document.querySelectorAll(selector);
          elements.forEach((element, index) => {
            // Skip if already identified as a status element
            if (statusElements.some(status => status.selector === generateSelector(element, index))) {
              return;
            }

            const content = element.textContent?.trim() || '';
            if (content.length < 5) return; // Skip very short content

            const messageType = determineMessageTypeFromContent(content);
            if (messageType !== 'unknown') {
              const needsAriaLive = !element.hasAttribute('aria-live') && !element.hasAttribute('role');
              const suggestedRole = getSuggestedRole(messageType);
              const suggestedLive = getSuggestedLive(messageType);

              potentialStatusElements.push({
                selector: generateSelector(element, index),
                element: element.tagName.toLowerCase(),
                messageType,
                content: content.substring(0, 100),
                needsAriaLive,
                suggestedRole,
                suggestedLive,
              });
            }
          });
        } catch (e) {
          // Skip invalid selectors
        }
      });

      return {
        statusElements,
        potentialStatusElements,
        totalStatusElements: statusElements.length,
        totalPotentialElements: potentialStatusElements.length,
      };

      function generateSelector(element: Element, index: number): string {
        if (element.id) return `#${element.id}`;
        if (element.className) {
          const classes = element.className.split(' ').filter(c => c.trim());
          if (classes.length > 0) return `.${classes[0]}`;
        }
        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function determineMessageType(element: Element, selector: string): string {
        const role = element.getAttribute('role');
        if (role) return role;

        const className = element.className.toLowerCase();
        const content = element.textContent?.toLowerCase() || '';

        if (selector.includes('alert') || className.includes('alert') || className.includes('error')) {
          return 'alert';
        }
        if (className.includes('success') || content.includes('success') || content.includes('saved')) {
          return 'status';
        }
        if (className.includes('warning') || content.includes('warning')) {
          return 'alert';
        }
        if (className.includes('info') || className.includes('message')) {
          return 'status';
        }
        if (className.includes('loading') || content.includes('loading')) {
          return 'status';
        }

        return 'status'; // Default to status
      }

      function determineMessageTypeFromContent(content: string): string {
        const lowerContent = content.toLowerCase();
        
        if (/error|failed|invalid|incorrect|required|missing/.test(lowerContent)) {
          return 'alert';
        }
        if (/success|saved|completed|confirmed|sent|submitted/.test(lowerContent)) {
          return 'status';
        }
        if (/warning|caution|note|important/.test(lowerContent)) {
          return 'alert';
        }
        if (/loading|processing|please wait|updating/.test(lowerContent)) {
          return 'status';
        }
        if (/\d+\s*(item|result|match|found|selected)/.test(lowerContent)) {
          return 'status';
        }

        return 'unknown';
      }

      function checkStatusAccessibility(element: Element, messageType: string): boolean {
        const hasAriaLive = element.hasAttribute('aria-live');
        const hasRole = element.hasAttribute('role');
        const role = element.getAttribute('role');
        const liveValue = element.getAttribute('aria-live');

        // Check if element has appropriate ARIA attributes
        if (messageType === 'alert') {
          return role === 'alert' || liveValue === 'assertive';
        }
        if (messageType === 'status') {
          return role === 'status' || liveValue === 'polite' || hasAriaLive;
        }

        return hasAriaLive || hasRole;
      }

      function getSuggestedRole(messageType: string): string {
        switch (messageType) {
          case 'alert': return 'alert';
          case 'status': return 'status';
          case 'log': return 'log';
          default: return 'status';
        }
      }

      function getSuggestedLive(messageType: string): string {
        switch (messageType) {
          case 'alert': return 'assertive';
          case 'status': return 'polite';
          case 'log': return 'polite';
          default: return 'polite';
        }
      }

      function getSeverity(messageType: string, isAccessible: boolean): 'error' | 'warning' | 'info' {
        if (!isAccessible) {
          return messageType === 'alert' ? 'error' : 'warning';
        }
        return 'info';
      }
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const totalElements = statusAnalysis.statusElements.length + statusAnalysis.potentialStatusElements.length;

    if (totalElements > 0) {
      // Analyze accessibility issues
      const inaccessibleStatus = statusAnalysis.statusElements.filter(status => !status.isAccessible);
      const potentialStatus = statusAnalysis.potentialStatusElements.filter(potential => potential.needsAriaLive);

      if (inaccessibleStatus.length > 0) {
        const criticalIssues = inaccessibleStatus.filter(status => status.messageType === 'alert');
        const warningIssues = inaccessibleStatus.filter(status => status.messageType !== 'alert');

        if (criticalIssues.length > 0) {
          score -= Math.min(40, criticalIssues.length * 15);
          issues.push(`${criticalIssues.length} critical status messages are not accessible`);
        }

        if (warningIssues.length > 0) {
          score -= Math.min(30, warningIssues.length * 10);
          issues.push(`${warningIssues.length} status messages lack proper accessibility markup`);
        }
      }

      if (potentialStatus.length > 0) {
        score -= Math.min(20, potentialStatus.length * 5);
        issues.push(`${potentialStatus.length} potential status messages need accessibility markup`);
      }

      evidence.push({
        type: 'interaction',
        description: 'Status messages accessibility analysis',
        value: `Found ${statusAnalysis.totalStatusElements} status elements and ${statusAnalysis.totalPotentialElements} potential status messages`,
        elementCount: totalElements,
        affectedSelectors: [
          ...statusAnalysis.statusElements.map(s => s.selector),
          ...statusAnalysis.potentialStatusElements.map(p => p.selector)
        ],
        severity: inaccessibleStatus.some(s => s.messageType === 'alert') ? 'error' : 
                 inaccessibleStatus.length > 0 ? 'warning' : 'info',
        fixExample: {
          before: '<div class="error">Please enter a valid email address</div>',
          after: '<div class="error" role="alert" aria-live="assertive">Please enter a valid email address</div>',
          description: 'Add appropriate ARIA attributes to status messages',
          codeExample: `
<!-- Before: Inaccessible status messages -->
<div class="error">Please enter a valid email address</div>
<div class="success">Form submitted successfully</div>
<div class="loading">Processing your request...</div>

<!-- After: Accessible status messages -->
<div class="error" role="alert" aria-live="assertive">
  Please enter a valid email address
</div>

<div class="success" role="status" aria-live="polite">
  Form submitted successfully
</div>

<div class="loading" role="status" aria-live="polite" aria-atomic="true">
  Processing your request...
</div>

<!-- For dynamic content -->
<div id="search-results" role="status" aria-live="polite" aria-atomic="false">
  <!-- Results will be announced when updated -->
</div>

<!-- For form validation -->
<input type="email" aria-describedby="email-error">
<div id="email-error" role="alert" aria-live="assertive" style="display: none;">
  <!-- Error message will be announced when shown -->
</div>
          `,
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/status-messages.html',
            'https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-live',
            'https://www.w3.org/WAI/WCAG21/Techniques/aria/ARIA22'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: totalElements,
          checkSpecificData: {
            totalStatusElements: statusAnalysis.totalStatusElements,
            totalPotentialElements: statusAnalysis.totalPotentialElements,
            inaccessibleStatus: inaccessibleStatus.length,
            accessibleStatus: statusAnalysis.statusElements.length - inaccessibleStatus.length,
            messageTypes: [...new Set(statusAnalysis.statusElements.map(s => s.messageType))],
            potentialNeedingMarkup: potentialStatus.length,
          },
        },
      });

      // Add specific examples for inaccessible status messages
      inaccessibleStatus.slice(0, 5).forEach(status => {
        evidence.push({
          type: 'interaction',
          description: `Inaccessible ${status.messageType} message`,
          value: `"${status.content}" - Missing: ${!status.hasAriaLive ? 'aria-live' : ''} ${!status.hasRole ? 'role' : ''}`,
          selector: status.selector,
          severity: status.severity,
          metadata: {
            checkSpecificData: {
              messageType: status.messageType,
              hasAriaLive: status.hasAriaLive,
              hasRole: status.hasRole,
              liveValue: status.liveValue,
              roleValue: status.roleValue,
            },
          },
        });
      });

      // Add examples for potential status messages
      potentialStatus.slice(0, 5).forEach(potential => {
        evidence.push({
          type: 'interaction',
          description: `Potential ${potential.messageType} message needs markup`,
          value: `"${potential.content}" - Suggested: role="${potential.suggestedRole}" aria-live="${potential.suggestedLive}"`,
          selector: potential.selector,
          severity: 'warning',
          metadata: {
            checkSpecificData: {
              messageType: potential.messageType,
              suggestedRole: potential.suggestedRole,
              suggestedLive: potential.suggestedLive,
              needsAriaLive: potential.needsAriaLive,
            },
          },
        });
      });

      recommendations.push('Add role="alert" and aria-live="assertive" to error messages');
      recommendations.push('Add role="status" and aria-live="polite" to success and info messages');
      recommendations.push('Use aria-atomic="true" for messages that should be read completely');
      recommendations.push('Ensure status messages are programmatically determinable');
      
      if (inaccessibleStatus.some(s => s.messageType === 'alert')) {
        recommendations.push('CRITICAL: Error messages must be immediately announced to screen readers');
      }
    } else {
      // No status messages found
      evidence.push({
        type: 'info',
        description: 'No status messages detected',
        value: 'Page does not contain identifiable status messages',
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: 0,
          checkSpecificData: {
            noStatusMessages: true,
          },
        },
      });
    }

    // Ensure score doesn't go below 0
    score = Math.max(0, score);

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
